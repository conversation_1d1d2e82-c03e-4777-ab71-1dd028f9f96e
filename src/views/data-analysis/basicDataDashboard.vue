<!-- 我的运营数据 -->
<template>
  <a-empty :description="isPageData === 0?'暂无数据':'暂无数据权限'" v-if="isPageData < 1"/>
  <!-- 基础看板数据 -->
  <a-spin :spinning="boardDataLoading" v-if="isPageData >= 1">
    <div class="basic-top" v-if="isPageData >= 1">
      <div class="net-article-box">
        <div class="progress-box">
          <a-progress
            trailColor="#BAE6FD"
            :stroke-color="{
            '0%': 'rgba(32,209,246,0.50)',
            '100%': '#20D1F6',
            }"
            :size="20"
            :percent="boardData.weChatCountTotal/boardData.contentCountTotal*100"
            :show-info="false"
          />
        </div>
        <div class="account-list">
          <div class="account-list-top">
            <p class="account-color account-large-color"></p>
            <p>公众账号总数</p>
          </div>
          <p class="account-count">
            <countTo :start-val="0" :end-val="boardData.weChatCountTotal || 0" :duration="2000"/>
          个</p>
        </div>
        <div class="account-list">
          <div class="account-list-top">
            <p class="account-color account-middle-color"></p>
            <p>在网文章总数</p>
          </div>
          <p class="account-count">
            <countTo :start-val="0" :end-val="boardData.contentCountTotal || 0" :duration="2000"/>
          篇</p>
        </div>
      </div>
      <div class="chart-box">
          <div class="title-text">单篇最高阅读量（7日内）</div>
          <div class="wechat-count">
            <countTo :start-val="0" :end-val="boardData.maxReadCountSevenDay || 0" :duration="2000"/>
          </div>
          <div class="wechat-name">
            <p class="wechat-name-color"></p>
            {{ boardData.maxReadCountSevenDayWeChatName?boardData.maxReadCountSevenDayWeChatName[0]:'' }}
          </div>
      </div>
      <div class="chart-box">
          <div class="title-text">7日内容总数</div>
          <div class="wechat-count">
            <countTo :start-val="0" :end-val="boardData.contentCountSevenDay || 0" :duration="2000"/>
            <div>篇</div>
          </div>
          <div class="wechat-name">
            <p class="count-chain-ratio">
              环比
              <span :class="{'negative-text':boardData.contentCountSevenDayMom<0}">
                <countTo :start-val="0" :end-val="boardData.contentCountSevenDayMom || 0" :duration="2000"/>
              %</span>
            </p>
          </div>
      </div>
      <div class="count-class-box">
          <div class="count-class-list">
              <div class="title-text">近7日阅读总数</div>
              <div class="count-class-con">
                  <p class="count-class-num">
                    <countTo :start-val="0" :end-val="boardData.readCountSevenDay || 0" :duration="2000"/>
                  </p>
                  <p class="count-chain-ratio">
                      环比
                      <span :class="{'negative-text':boardData.readCountThisMonthMom<0}">
                        <countTo :start-val="0" :end-val="boardData.readCountSevenDayMom || 0" :duration="2000"/>
                      %</span>
                  </p>
              </div>
          </div>
          <div class="count-class-list">
              <div class="title-text">近7日分享总数</div>
              <div class="count-class-con">
                  <p class="count-class-num">{{ boardData.shareCountSevenDay }}</p>
                  <p class="count-chain-ratio">
                      环比
                      <span :class="{'negative-text':boardData.readCountThisMonthMom<0}">
                        <countTo :start-val="0" :end-val="boardData.shareCountSevenDayMom || 0" :duration="2000"/>
                      %</span>
                  </p>
              </div>
          </div>
      </div>
      <div class="count-class-box">
        <div class="count-class-list">
            <div class="title-text">本月阅读总数</div>
            <div class="count-class-con">
                <p class="count-class-num">{{ boardData.readCountThisMonth }}</p>
                <p class="count-chain-ratio">
                    环比
                    <span :class="{'negative-text':boardData.readCountThisMonthMom<0}">
                    <countTo :start-val="0" :end-val="boardData.readCountThisMonthMom || 0" :duration="2000"/>
                    %</span>
                </p>
            </div>
        </div>
        <div class="count-class-list">
            <div class="title-text">本月分享总数</div>
            <div class="count-class-con">
                <p class="count-class-num">
                  <countTo :start-val="0" :end-val="boardData.shareCountThisMonth || 0" :duration="2000"/>
                </p>
                <p class="count-chain-ratio">
                    环比
                    <span :class="{'negative-text':boardData.readCountThisMonthMom<0}">
                      <countTo :start-val="0" :end-val="boardData.shareCountThisMonthMom || 0" :duration="2000"/>%
                    </span>
                </p>
            </div>
        </div>
      </div>
    </div>
  </a-spin>
  <!-- 表格数据 -->
  <basicTable/>
</template>

<script setup>
import { CountTo } from 'vue3-count-to'
import basicTable from "./basic-data-dashboard/basicTable.vue";
import { message } from "ant-design-vue";
import { boardTotal } from "@/api/basicDataDashboard";
//页面数据权限（0暂无数据）（-1暂无权限）
const isPageData = ref(1)
//获取页面环比数据
const boardData = ref({});
const boardDataLoading = ref(false)
const getBoardTotal = async () => {
  boardDataLoading.value = true
  const res = await boardTotal();
  boardDataLoading.value = false
  if (res.retCode === "000000") {
    isPageData.value = 1
    boardData.value = res.data;
  } else if(res.retCode === "000031") {
    isPageData.value = 0
  } else {
    message.error(res.retMsg);
    boardData.value = {}
    isPageData.value = 0
  }
};
onMounted(() => {
  getBoardTotal()
})
</script>
<style lang="less" scoped>
.basic-top > div {
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
}
.basic-top {
  height: 226px;
  display: flex;
  margin-bottom: 25px;
  
  .net-article-box {
    width: 195px;
    padding: 20px;
    background: #fff;
    .progress-box {
      margin-bottom: 30px;
    }
  }
  .chart-box {
    flex: 1;
    background: #fff;
    margin-left: 8px;
    position: relative;
  }
  .wechat-count {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 48px;
    div {
        position: relative;
        top: 10px;
        font-size: 18px;
    }
  }
  .wechat-name {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 20px;
    left: 25px;
    z-index: 2;
    font-size: 14px;
    color: #1E293B;
    .wechat-name-color {
        width: 6px;
        height: 6px;
        display: inline-block;
        margin-right: 5px;
        background: #0058FF;
    }
  }
  .count-class-box {
    width: 180px;
    margin-left: 8px;
    display: flex;
    flex-wrap: wrap;
    align-content: space-between;
  }
  .count-class-list {
    width: 100%;
    height: 110px;
    background: #fff;
  }
  .count-class-con {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 23px 10px;
  }
  .count-class-num {
    font-size: 16px;
    font-weight: bold;
    color: #1e293b;
  }
  .count-chain-ratio {
    font-size: 14px;
    color: #64748b;
    span {
      background-image: linear-gradient(270deg, #10b981 2%, #34d399 100%) !important;
      box-shadow: 0 2px 4px 0 rgba(57, 218, 138, 0.5) !important;
      border-radius: 11px;
      color: #fff;
      padding: 2px 5px;
    }
    .negative-text {
      background-image: linear-gradient(270deg, #f59e0b 2%, #fbbf24 100%);
      box-shadow: 0 2px 4px 0 rgba(245, 158, 11, 0.5);
    }
  }
  .title-text {
    font-size: 16px;
    color: #1e293b;
    border-bottom: 1px solid #f1f5f9;
    padding: 15px;
  }
  .account-list {
    margin-bottom: 15px;
  }
  .account-list-top {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #64748b;
    margin-bottom: 10px;
  }
  .account-color {
    width: 6px;
    height: 6px;
    margin-right: 5px;
  }
  .account-large-color {
    background-color: #bae6fd;
  }
  .account-middle-color {
    background-color: #21d1f5;
  }
  .account-count {
    font-size: 16px;
    color: #1e293b;
    font-weight: bold;
    padding-left: 12px;
  }
}
.ant-empty {
  padding: 60px;
}
.province-select {
  position: absolute;
  right: 10px;
  top: 0px;
  .ant-select {
    width: 180px;
  }
}
</style>
