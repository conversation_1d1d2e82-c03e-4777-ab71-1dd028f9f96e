<!-- 新媒体账号明细 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-input
        v-model:value="searchFormm.name"
        placeholder="按账号名搜索（模糊）"
      />
      <a-button type="primary" @click="searchFun" class="submit-btn">
        <template #icon>
          <SearchOutlined :style="{ fontSize: '14px'}" />
        </template>
        查询
      </a-button>
    </div>
    <div class="top-box">
      <a-button type="primary" @click="showModal()" class="submit-btn">
        <template #icon>
          <PlusOutlined />
        </template>
        账号信息申报
      </a-button>
      <!-- <a-button
				type="primary"
					:loading="exportLoading"
				@click="exportAccountExcle()"
				class="submit-btn"
			>
				<template #icon>
					<DownloadOutlined />
				</template>
				导出数据
			</a-button> -->
    </div>
  </div>
  <div class="account-list">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 1800, y: 500, hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'platform'">
          <span>{{ 
            { 
              1: '公众号', 
              2: '小红书', 
              3: '微博', 
              4: '视频号', 
              5: '抖音', 
              6: '头条', 
              7: '快手',
              100: '其他' 
            }[record.platform] || '-' 
            }}</span>
        </template>
        <template v-if="column.dataIndex === 'accountType'">
          <span>{{ 
            { 
              1: '营销类', 
              2: '综合类', 
              20: '内宣类', 
              30: '其他类'
            }[record.accountType] || '-' 
            }}</span>
        </template>
        <template v-if="column.dataIndex === 'fansCount'">
          {{ record.fansCount || "-" }}
        </template>
        <template v-if="column.dataIndex === 'contentCount'">
          {{ record.contentCount || "-" }}
        </template>
        <template v-if="column.dataIndex === 'cityName'">
          {{ record.cityName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'mcnFlag'">
          {{ record.mcnFlag == 1 ? "是" : "否" }}
        </template>
        <template v-if="column.dataIndex === 'auditStatus'">
          {{
            record.auditStatus == 1
              ? "审核中"
              : record.auditStatus == 2
              ? "已通过"
              : "未通过"
          }}
        </template>
        <template v-if="column.dataIndex === 'updateFrequency'">
          <span v-if="record.updateFrequency === 1">7日内更新</span>
          <span v-else-if="record.updateFrequency === 2">15日内更新</span>
          <span v-else-if="record.updateFrequency === 3">30日内更新</span>
          <span v-else-if="record.updateFrequency === 4">超过30日未更新</span>
          <span v-else>-</span>
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span class="operate-btn" @click="showModal(record)">修改</span>
            <span class="operate-btn" @click="showMsgModal(record)"
              >信息维护</span
            >
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <addEdit :addEditData="addEditData" />
  <msgMaintain :msgData="msgData" />
</template>
<script setup>
import { message, notification } from "ant-design-vue";
import addEdit from "./account-list/addEdit.vue";
import msgMaintain from "./account-list/msgMaintain.vue";
import { accountPage, accountExport } from "@/api/accountList";
import useGetList from "@/hooks/useGetList";
import useExportData from "@/hooks/useExportData";
import emitter from "@/assets/js/emitter";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "账号",
    dataIndex: "name",
    key: "name",
    width: "120px",
    ellipsis: true,
    fixed: "left",
  },
  {
    title: "平台",
    dataIndex: "platform",
    key: "platform",
    width: "90px",
    fixed: "left",
    filters: [
      {text: "公众号",value: 1},
      {text: "小红书",value: 2},
      {text: "微博",value: 3},
      {text: "视频号",value: 4},
      {text: "抖音",value: 5},
      {text: "头条",value: 6},
      {text: "快手",value: 7},
      {text: "其他",value: 100}
    ],
    filterMultiple: false,
  },
  {
    title: "认证主体/状态",
    dataIndex: "mainBody",
    ellipsis: true,
  },
  {
    title: "运营管理归口",
    dataIndex: "cityName",
  },
  {
    title: "运营管理员",
    dataIndex: "operateAdmin",
  },
  {
    title: "账号类型",
    dataIndex: "accountType",
  },
  {
    title: "粉丝量",
    dataIndex: "fansCount",
  },
  {
    title: "在网内容（条）",
    dataIndex: "contentCount",
  },
  {
    title: "更新频率",
    dataIndex: "updateFrequency",
  },
  {
    title: "融媒体接入",
    dataIndex: "mcnFlag",
  },
  {
    title: "审核状态",
    dataIndex: "auditStatus",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    width: "120px",
    fixed: "right",
  },
]);
const pageLoading = ref(false);
//查询列表
const searchFormm = ref({
  name: "",
  platform: "",
});
// 分页/筛选
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
const handleTableChange = (pag, filters, sorter) => {
  searchFormm.value.platform = filters.platform ? filters.platform[0] : "";
  useGetList(accountPage, pageLoading, dataSource, pagination, {
    ...searchFormm.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
//搜索
const searchFun = () => {
  useGetList(accountPage, pageLoading, dataSource, pagination, {
    ...searchFormm.value,
    pageNum: 1,
  });
};
// 获取列表数据
onMounted(() => {
  useGetList(accountPage, pageLoading, dataSource, pagination);
});
//新增编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: "账号信息申报",
  item: {
    name: "",
    platform: null,
    mainBody: "",
    mcnFlag: null,
    weChatId: "",
    operateAdmin: "",
  },
});
const showModal = (item = {}) => {
  if (item.id) {
    if (item.createFlag === 0) {
      message.error("账号申请人才能修改");
      return false;
    } else if (item.auditStatus === 1 || item.auditStatus === 2) {
      message.error("审核未通过的账号才能修改");
      return false;
    } else {
      addEditData.modalTitle = "账号信息申报-修改";
      Object.assign(addEditData.item, item);
    }
  } else {
    addEditData.modalTitle = "账号信息申报";
  }
  addEditData.modalOpen = true;
};
// 数据导出
const exportLoading = ref(false);
const exportAccountExcle = () => {
  useExportData(accountExport,pageLoading,exportLoading,searchFormm.value)
};
//信息维护
const msgData = reactive({
  modalOpen: false,
  item: {
    name: "成都移动",
    fansCount: "",
    contentCount: "",
    cntEcOprAccountId: "",
    updateFrequency: null,
  },
});
const showMsgModal = (item) => {
  if (item.auditStatus === 1 || item.auditStatus === 3) {
    message.error("审核已通过的账号才能维护信息");
    return false;
  } else if (!item.informationEditAuth) {
    message.error("无权操作此账号");
    return false;
  }
  msgData.item.cntEcOprAccountId = item.id;
  msgData.item.name = item.name;
  msgData.item.fansCount = item.fansCount || "";
  msgData.item.contentCount = item.contentCount || "";
  msgData.item.updateFrequency = item.updateFrequency;
  msgData.modalOpen = true;
};
//新增/编辑/信息维护后更新数据
emitter.on("refreshAccount", (value) => {
  if (value === "addEdit") {
    addEditData.modalOpen = false;
  } else if (value === "msg") {
    msgData.modalOpen = false;
  }
  useGetList(accountPage, pageLoading, dataSource, pagination);
});
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("refreshAccount");
});
</script>

<style lang="less" scoped>
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-box,
  .top-box {
    display: flex;
    .ant-select {
      margin-left: 15px;
    }
  }
  .top-box {
    .ant-btn {
      margin-left: 15px;
    }
  }
}
.account-list {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  .operate-box {
    span {
      color: #FE8308;
      margin-right: 10px;
      cursor: pointer;
    }
  }
}
</style>
