{"key": "3436adcf2334fdd5259d44af2c606f087edacb2e28bd981477225bf6d0985ccd", "data": "\n期望你扮演资深前端开发专家角色，精通Vue.js (2/3) 和 JavaScript/TypeScript，能够识别项目代码API风格(Options/Composition)，根据Vue版本进行特定检查，能够识别项目代码语言，如果未使用typescript则不进行\"TypeScript问题\"检查，\n\n【重要指示】\n1. 必须严格按照下方\"审查结果完整结构\"输出代码审查结果，缺少任何一个部分都是不合格的!\n2. 走查问题数量要求：\n智能分析指导原则：\n- 根据文件行数、复杂度、文件类型等因素智能确定问题数量\n\n问题分布建议：\n（根据代码复杂度动态调整）\n- 严重问题：15-30%\n- 一般问题：45-55%\n- 轻微问题：20-40%\n\n特殊情况：如果文件确实没有明显问题，可输出\"该文件代码质量良好，未发现明显问题\"\n3. 多文件模式(当前为多文件模式)：必须针对每个文件进行单独分析并输出详细结果\n4. 输出格式必须完全符合要求结构，按照章节顺序依次展示：一、整体概述 二、重要问题清单 三、详细分析 四、问题列表表格\n5. 在上述要求的基础上需要额外关注业务逻辑问题和编码安全问题，并且需要根据代码复杂度分析结果，合理分配问题数量\n6. **严禁在问题描述中使用模糊表述**：不得使用\"多个组件\"、\"多个文件\"、\"多处\"、\"多种\"、\"某些文件\"、\"部分组件\"等含糊不清的表述，必须明确指出具体的文件路径(如src/components/Button.vue)和具体行号(如第15行或第20-25行)\n\n任务:\n对代码进行全面审查，根据以下检查规则提供结构化分析。\n\n问题分类规则如下：\n\n- **严重程度** - **问题分类**\n- **【轻微】** - 文件命名是否规范(#3)\n- **【轻微】** - 类命名是否规范(#4)\n- **【轻微】** - 方法名是否规范(#5)\n- **【一般】** - 是否存在超1000行大类(#6)\n- **【一般】** - 是否存在超300行方法(#7)\n- **【轻微】** - 无效注释/缺失必要注释(#8)\n- **【严重】** - 存在3层以上嵌套循环(#10)\n- **【一般】** - 存在性能隐患(#13)\n- **【一般】** - 异常处理不正确(#15)\n- **【一般】** - 异常用于流程控制(#16)\n- **【严重】** - finally块中使用return(#17)\n- **【一般】** - 日志信息当成调试用(#19)\n- **【轻微】** - 日志打印不正确(#20)\n- **【一般】** - 是否存在魔法字(#27)\n- **【轻微】** - 是否存在无效代码/引用/配置(#28)\n- **【严重】** - 是否存在安全隐患(#29)\n- **【一般】** - 代码不合理编写(#30)\n- **【严重】** - 是否存在业务逻辑问题(#31)\n- **【一般】** - 没有抽取公共代码(#32)\n- **【一般】** - 建议使用工具类方法(#33)\n- **【严重】** - 空指针异常(#34)\n- **【一般】** - 存在3层以上嵌套if/else(#36)\n- **【一般】** - 建议做成可配置(#38)\n- **【一般】** - 子系统/模块的分层不合理(#39)\n- **【轻微】** - 字段名是否规范(#40)\n- **【一般】** - 变量作用域不合理(#41)\n- **【轻微】** - 日志打印不正确或缺失日志(#42)\n- **【严重】** - React组件内存泄漏(#43)\n- **【一般】** - React Hook使用不当(#44)\n- **【一般】** - React组件重渲染优化(#45)\n- **【轻微】** - React组件命名规范(#46)\n- **【一般】** - React状态管理不当(#47)\n- **【严重】** - React副作用处理错误(#48)\n- **【一般】** - JSX语法不规范(#49)\n- **【轻微】** - React Props类型定义缺失(#50)\n\n### 问题严重程度分为三级：\n- 严重 - 可能导致系统故障、安全漏洞或数据错误的问题\n- 中等 - 降低系统性能、用户体验或维护性的问题\n- 轻微 - 不影响功能但违反最佳实践的问题\n\n\n检查范围包括（包括但不限于）：\n\n1. **功能性业务逻辑问题**\n包括但不限于：\n   - 业务流程不完整、状态管理缺陷、边界情况处理不当\n   - 数据验证逻辑不严谨不清晰，表单校验规则不完善\n   - 需求理解偏差导致功能实现与预期不符\n2. **安全问题**\n包括但不限于：\n   - 防止XSS攻击(避免直接使用v-html渲染用户输入)\n   - CSRF风险、敏感信息处理不应明文存储、权限控制\n   - 前后端接口入参敏感信息未做加密处理\n   - 前端未对敏感输入/输出进行转义处理\n3. **代码质量问题**\n包括但不限于：\n   - 代码风格不一致\n   - 复杂功能代码注释不足\n   - 存在大量重复代码\n   - 代码可读性差，难以理解\n   - 代码可维护性差，难以维护\n4. **性能问题**\n包括但不限于：\n   - 避免3层以上嵌套循环\n   - 不必要的渲染、复杂计算未优化、大型组件未拆分\n   - 未使用虚拟滚动处理长列表数据\n   - 未使用防抖/节流优化频繁触发事件\n   - 未使用懒加载优化首屏加载时间\n5. **代码结构问题**\n包括但不限于：\n   - 组件职责不明确、逻辑过度耦合、复用性差\n   - 缺少统一的状态管理策略\n   - 组件层级嵌套过深，props透传过多层级\n6. **健壮性问题**\n包括但不限于：\n   - 空值/异常处理是否完善\n   - 错误边界处理不当、异常捕获不完善\n   - 网络请求没有完整的加载状态处理\n7. **编码规范问题**\n包括但不限于：\n   - 命名是否规范(Pascal/Camel/Kebab)\n   - 魔法数字/字符串未提取为常量\n   - 注释不足(特别是复杂逻辑)\n9. **Vue最佳实践问题**\n包括但不限于：\n   - vue3项目ref/reactive使用是否恰当\n   - computed/watch使用是否恰当\n   - 生命周期函数正确使用\n   - 组合式API设计是否合理\n   - 组件通信方式选择是否合理\n   - v-for键值设置、v-if与v-show选择\n   - 模板语法和指令使用规范\n   - Vuex/Pinia状态管理使用\n   - 组件Props定义和校验\n\n关注领域:\n- 质量: 分析代码清晰度、Vue组件设计、Composition API使用合理性、Options API结构、组件分层、代码重用和复杂度控制\n- 性能: 识别不必要的组件重渲染、大型组件分割、懒加载使用、计算属性缓存、长列表虚拟滚动、资源加载优化和打包体积问题\n- 可维护性: 评估组件文档、模板结构、Props定义、事件命名、Hooks抽取、模块化设计和目录结构合理性\n- 安全性: 查找前端常见漏洞(XSS、CSRF)、不安全的数据存储、敏感数据暴露、未校验的用户输入、不安全的第三方依赖和API调用安全问题\n- Vue最佳实践: 评估组件生命周期使用、计算属性vs方法、v-for键值设置、v-if与v-show选择、动态组件实现、Vue插件集成和依赖注入使用\n\n所有代码行的默认指派人: 提交者\n\n【格式要求】必须严格按照以下结构输出代码审查结果:\n\n## 审查结果完整结构\n\n### 一. 代码整体功能概述\n[此部分必须存在] 提供对代码整体功能的清晰概述，包括各文件的主要功能和它们之间的关系。对于多文件走读，需要解释整个文件夹的结构和功能分布。\n\n### 二. 重要问题及缺陷清单(按严重程度排序)\n[此部分必须存在] 列出所有重要问题，必须按严重程度排序，每个问题包含简短描述和所在文件/行号。\n\n### 三. 代码走查问题详细分析\n[此部分必须存在] 必须详细分析每个发现的问题，遵循以下格式：\n\n**问题标题**: [问题简要描述]\n**严重程度**: [严重/中等/轻微]\n**问题描述**: [代码文件相对路径/文件名-->具体行号，如第X-Y行，详细分析问题根源]\n**指派人**: [根据上面提供的代码行提交者信息，查找对应行号的提交者]\n**修复建议**:\n[具体修复建议]\n[示例代码]\n\n### 四. 走查问题列表\n[此部分必须存在] 以表格格式列出所有问题，表格必须包含以下列：\n\n| 问题标题 | 提出人 | 问题分类 | 问题描述 | 指派给 |\n|---------|--------|---------|-----------------|--------|\n| [代码走读]-[标题1] | [提出人] | [分类] | [文件路径-->行号: 问题描述和修复建议] | [根据代码行提交者信息查找对应行号的提交者] |\n| [代码走读]-[标题2] | [提出人] | [分类] | [文件路径-->行号: 问题描述和修复建议] | [根据代码行提交者信息查找对应行号的提交者] |\n\n#### 输出要求：\n- 所有问题必须提供具体行号、问题根源分析和可行的修复建议\n- 提出人赋值为CodeReviewer\n- 指派给：必须根据上面提供的\"代码行提交者信息\"，查找问题所在行号对应的提交者。如果找不到对应行号的提交者，则使用提交者默认值\n- 问题分类必须从上面的\"问题分类规则\"中选取，要求根据问题描述智能匹配问题分类\n- 表格中的问题描述一列必须采用\"文件路径-->行号: 问题描述和修复建议\"格式，并且要求修复建议尽可能详细最好从代码走查问题详细分析里面摘抄\n= 表格中的问题标题前面需添加[代码走读]-[标题]前缀\n- **重要：问题描述中禁止使用\"多个组件\"、\"多个文件\"、\"多处\"、\"多种\"等模糊表述，必须明确指出具体的文件路径和行号**\n\n- **总计问题数**：一共xx个，其中严重问题xx个，一般问题xx个，轻微问题xx个。\n\n\n待审查代码:\nThis file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.\n================\nFile Summary\n================\nPurpose:\n--------\nThis file contains a packed representation of the entire repository's contents.\nIt is designed to be easily consumable by AI systems for analysis, code review,\nor other automated processes.\n\nFile Format:\n================\nThe content is organized as follows:\n1. This summary section\n2. Repository information\n3. Directory structure\n4. Repository files (if enabled)\n5. Multiple file entries, each consisting of:\n  a. A separator line (================)\n  b. The file path (File: path/to/file)\n  c. Another separator line\n  d. The full contents of the file\n  e. A blank line\n\nUsage Guidelines:\n================\n- This file should be treated as read-only. Any changes should be made to the\n  original repository files, not this packed version.\n- When processing this file, use the file path to distinguish\n  between different files in the repository.\n- Be aware that this file may contain sensitive information. Handle it with\n  the same level of security as you would the original repository.\n\nNotes:\n------\n- Some files may have been excluded based on .gitignore rules and Repomix's configuration\n- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files\n- Only files matching these patterns are included: .\n- Files matching these patterns are excluded: node_modules, dist\n- Files matching patterns in .gitignore are excluded\n- Files matching default ignore patterns are excluded\n- Files are sorted by Git change count (files with more changes are at the bottom)\n================\nDirectory Structure\n================\nsrc/\n  api/\n    accountList.js\n    accountOverview.js\n    articleConfig.js\n    articleShareDetail.js\n    auditAccount.js\n    basicDataDashboard.js\n    bigBusinessCategory.js\n    businessLink.js\n    conOprWork.js\n    contentReview.js\n    contentTag.js\n    deptManage.js\n    index.js\n    indexContent.js\n    intelligentTopicSelect.js\n    intelligentWrite.js\n    login.js\n    materialTemplate.js\n    operationNode.js\n    smallBusinessCategory.js\n    userManage.js\n    weChatConfig.js\n    weChatKeyword.js\n    weChatMenu.js\n    weChatMultimediaMaterial.js\n    weChatNotReply.js\n    weChatStaffManage.js\n    weChatTextMaterial.js\n    weChatWelcome.js\n  assets/\n    css/\n      base.less\n      reset.less\n    js/\n      emitter.js\n      formValidator.js\n      request.js\n      utils.js\n  components/\n    weChat-config/\n      weChatConfig.vue\n  hooks/\n    loadMore.js\n    useDel.js\n    useExportData.js\n    useGetList.js\n    useLoadMore.js\n    useUpload.js\n  router/\n    index.js\n  views/\n    data-analysis/\n      account-list/\n        addEdit.vue\n        msgMaintain.vue\n      account-overview/\n        accountNumber.vue\n        topBox.vue\n      basic-data-dashboard/\n        basicTable.vue\n        lineChart.vue\n        lineChartMom.vue\n      accountList.vue\n      accountOverview.vue\n      basicDataDashboard.vue\n    index/\n      upload-img/\n        eidtImg.vue\n      index copy.vue\n      index.vue\n      indexContent.vue\n    intelligent-center/\n      content-review/\n        addEdit.vue\n        auditDetail.vue\n      intelligent-topic-select/\n        hotspot.vue\n        industry.vue\n      intelligent-write/\n        edit.vue\n        imitate.vue\n        style.vue\n        write.vue\n      contentReview.vue\n      intelligentTopicSelect.vue\n      intelligentWrite.vue\n    platform-manage/\n      audit-account/\n        audit.vue\n      dept-manage/\n        setMenu.vue\n      user-manage/\n        accreditMenu.vue\n        addEdit.vue\n        deptManage.vue\n        setAccount.vue\n      auditAccount.vue\n      deptManage copy.vue\n      deptManage.vue\n      userManage.vue\n    workbench/\n      con-opr-work/\n        msgSupplement.vue\n        msgSupplementForm.vue\n        schedule.vue\n        scheduleForm.vue\n        uploadImg.vue\n      functional-component/\n        weChat-staff-manage/\n          addEdit.vue\n      operate-manage/\n        content-tag/\n          addEdit.vue\n          tableTag.vue\n        operation-node/\n          addEdit.vue\n          setAccount.vue\n        contentTag.vue\n        operationNode.vue\n      wechat-manage/\n        material-template/\n          template/\n            image.vue\n            news.vue\n            newsitem.vue\n            post.vue\n            text.vue\n            video.vue\n            voice.vue\n          materialTemplate.vue\n        weChat-keyword/\n          addEdit.vue\n        weChat-menu/\n          addEdit.vue\n        weChat-multimedia-material/\n          add.vue\n          imageTabs.vue\n          newsTabs.vue\n          videoTabs.vue\n          voiceTabs.vue\n        weChat-not-reply/\n          addEdit.vue\n        weChat-text-material/\n          addEdit.vue\n        weChat-welcome/\n          addEdit.vue\n        weChatKeyword.vue\n        weChatMenu.vue\n        weChatMultimediaMaterial.vue\n        weChatNotReply.vue\n        weChatTextMaterial.vue\n        weChatWelcome.vue\n      conOprWork.vue\n    blank.vue\n    login.vue\n  App.vue\n  main.js\n.eslintrc-auto-import.json\n.gitignore\nauto-imports.d.ts\nindex.html\njsconfig.json\npackage.json\nREADME.md\nvite.config.js\n================\nFiles\n================================\n================\nFile: src/api/articleConfig.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 微信id数据\nexport const articleList = (data) => {\n    return $https.fetchGet('/cop/wx/back/articleShare/articleConfig/page',data)\n}\nexport const articleDel = (data) => {\n  return $https.fetchGet('/cop/wx/back/articleShare/articleConfig/del',data)\n}\nexport const articleSort = (data,chkCurWx) => {\n  return $https.fetchPost('/cop/wx/back/articleShare/sort?chkCurWx='+chkCurWx,data)\n}\nexport const articleDetail = (data) => {\n  return $https.fetchGet('/cop/wx/back/articleShare/articleConfig/detail',data)\n}\nexport const articleEdit = (data,chkCurWx) => {\n  return $https.fetchPost('/cop/wx/back/articleShare/articleConfig/edit?chkCurWx='+chkCurWx,data)\n}\nexport const articleAdd = (data,chkCurWx) => {\n  return $https.fetchPost('/cop/wx/back/articleShare/articleConfig/add?chkCurWx='+chkCurWx,data)\n}\n================\n================\nFile: src/api/indexContent.js\n================\n================\nimport $https from \"@/assets/js/request\"\n// 日历关键字\nexport const homeKeyword = () => {\n    return $https.fetchGet('/cop/home/<USER>/keyword')\n}\n================\n================\nFile: src/api/intelligentTopicSelect.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 预选行业\nexport const industryMine = (data) => {\n    return $https.fetchGet('/cop/ai/topic/industry/mine',data)\n}\n// 节日\nexport const getFestival = (data) => {\n    return $https.fetchGet('/cop/ai/topic/festival/page',data)\n}\n// 热榜\nexport const getHotspot = (data) => {\n    return $https.fetchGet('/cop/ai/topic/hot/page',data)\n}\n// 保存行业\nexport const industrySave = (data) => {\n    return $https.fetchPost('/cop/ai/topic/industry/user/rel/save',data)\n}\n// 获取所有行业\nexport const getIndustry = (data) => {\n  return $https.fetchGet('/cop/ai/topic/industry/all',data)\n}\n================\n================\nFile: src/api/intelligentWrite.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 历史数据\nexport const writeHistory = (data) => {\n    return $https.fetchGet('/cop/ai/write/history',data)\n}\n================\n================\nFile: src/api/materialTemplate.js\n================\n================\nimport $https from \"@/assets/js/request\"\n// 素材\nexport const listMaterialSelectorData = (data) => {\n    return $https.fetchGet('/cop/wx/back/common/list/materialSelectorData',data)\n}\n================\n================\nFile: src/api/weChatConfig.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 微信id数据\nexport const commonInit = (data) => {\n    return $https.fetchGet('/cop/manage/wechat/common/init',data)\n}\n// 选中提交\nexport const switchAccount = (data) => {\n    return $https.fetchPost('/cop/manage/wechat/common/switch/account',data)\n}\n================\n================\nFile: src/api/weChatKeyword.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 列表\nexport const rspList = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/list',data)\n}\n// 详情\nexport const rspDetail = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/detail',data)\n}\n// 编辑 \nexport const rspEdit = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/rsp/edit?chkCurWx='+chkCurWx,data,{'headers': {'Content-Type':'application/json'}})\n}\n// 新增\nexport const rspAdd = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/rsp/add?chkCurWx='+chkCurWx,data,{'headers': {'Content-Type':'application/json'}})\n}\n// 删除\nexport const rspDel = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/del',data)\n}\n================\n================\nFile: src/api/weChatNotReply.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 列表\nexport const ukwList = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/ukw/list',data)\n}\n//触发类型\nexport const userSendMsgType = (data) => {\n    return $https.fetchGet('/cop/wx/back/common/list/userSendMsgType',data)\n}\n// 编辑 \nexport const ukwEdit = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/rsp/ukw/edit?chkCurWx='+chkCurWx,data)\n}\n// 新增\nexport const ukwAdd = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/rsp/ukw/add?chkCurWx='+chkCurWx,data)\n}\n// 删除\nexport const ukwDel = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/ukw/del',data)\n}\n================\n================\nFile: src/api/weChatStaffManage.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 列表\nexport const staffPage = (data) => {\n    return $https.fetchGet('/cop/wx/back/staff/page',data)\n}\n// 编辑 \nexport const staffEdit = (data, chkCurWx) => {\n  return $https.fetchPost('/cop/wx/back/staff/edit?chkCurWx=' + chkCurWx, data)\n}\n// 新增\nexport const staffAdd = (data, chkCurWx) => {\n  return $https.fetchPost('/cop/wx/back/staff/add?chkCurWx=' + chkCurWx, data)\n}\n// 查询业务大类\nexport const bizList = (data) => {\n  return $https.fetchGet('/cop/manage/tag/first/biz/list', data)\n}\n// 详情\nexport const staffDetail = (data) => {\n  return $https.fetchGet('/cop/wx/back/staff/detail', data)\n}\n// 删除\nexport const staffDel = (data) => {\n  return $https.fetchGet('/cop/wx/back/staff/delete',data)\n}\n================\n================\nFile: src/api/weChatTextMaterial.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 列表\nexport const txtList = (data) => {\n    return $https.fetchGet('/cop/wx/back/template/txt/list',data)\n}\n// 编辑 \nexport const txtEdit = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/template/txt/edit?chkCurWx='+chkCurWx,data)\n}\n// 新增\nexport const txtAdd = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/template/txt/add?chkCurWx='+chkCurWx,data)\n}\n// 删除\nexport const txtDel = (data) => {\n    return $https.fetchGet('/cop/wx/back/template/txt/del',data)\n}\n================\n================\nFile: src/api/weChatWelcome.js\n================\n================\nimport $https from \"@/assets/js/request\"\n\n// 列表\nexport const subList = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/sub/list',data)\n}\n// 编辑 \nexport const subEdit = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/rsp/sub/edit?chkCurWx='+chkCurWx,data)\n}\n// 新增\nexport const subAdd = (data,chkCurWx) => {\n    return $https.fetchPost('/cop/wx/back/rsp/sub/add?chkCurWx='+chkCurWx,data)\n}\n// 删除\nexport const subDel = (data) => {\n    return $https.fetchGet('/cop/wx/back/rsp/sub/del',data)\n}\n================\n================\nFile: src/assets/css/base.less\n================\n================\n// 主题颜色\n@subject-color: #48cfd6;\n// 主要文字大小\n@fontSize12: 12px;\n@fontSize14: 14px;\n@fontSize16: 16px;\n@primary-color: red;\n================\n================\nFile: src/assets/js/emitter.js\n================\n================\n// 引入mitt\r\nimport mitt from 'mitt'\r\n\r\n// 调用mitt得到emitter，emitter能：绑定事件、触发事件\r\nconst emitter = mitt()\r\n\r\nexport default emitter\n================\n================\nFile: src/hooks/loadMore.js\n================\n================\nimport emitter from \"@/assets/js/emitter\";\nconst domRef = ref()\n// // 定义滚动函数\nconst pageNo = ref(1)\nconst handleScroll = () => {\n\tconst tableContainer = domRef.value.$el.querySelector('.ant-spin-container');\n\tconst scrollPosition = tableContainer.scrollTop;\n\tconst isTop = scrollPosition === 0;\n\tconst isBottom = tableContainer.scrollHeight - scrollPosition === tableContainer.clientHeight;\n\tif (isBottom) {\n        pageNo.value++\n        emitter.emit(\"loadingMore\", pageNo.value);\n\t}\n}\n// 添加scroll监听\nemitter.on(\"onMounted\", (value) => {\n    nextTick(()=>{\n        domRef.value = value\n      if (domRef.value) {\n        const tableContainer = domRef.value.$el.querySelector('.ant-spin-container');\n        tableContainer.addEventListener('scroll', handleScroll);\n      }\n    })\n});\n// 移除scroll监听\nemitter.on(\"onBeforeUnmount\", (value) => {\n    nextTick(()=>{\n\t\tif (domRef.value) {\n\t\t\tconst tableContainer = domRef.value.$el.querySelector('.ant-spin-container');\n\t\t\ttableContainer.removeEventListener('scroll', handleScroll);\n\t\t}\n\t})\n});\n================\n================\nFile: src/hooks/useLoadMore.js\n================\n================\nimport emitter from \"@/assets/js/emitter\";\nconst domRef = ref()\n// // 定义滚动函数\nconst pageNo = ref(1)\nconst handleScroll = () => {\n\tconst tableContainer = domRef.value.$el.querySelector('.ant-spin-container');\n\tconst scrollPosition = tableContainer.scrollTop;\n\tconst isTop = scrollPosition === 0;\n\tconst isBottom = tableContainer.scrollHeight - scrollPosition === tableContainer.clientHeight;\n\tif (isBottom) {\n        pageNo.value++\n        emitter.emit(\"loadingMore\", pageNo.value);\n\t}\n}\n// 添加scroll监听\nemitter.on(\"onMounted\", (value) => {\n    nextTick(()=>{\n        domRef.value = value\n      if (domRef.value) {\n        const tableContainer = domRef.value.$el.querySelector('.ant-spin-container');\n        tableContainer.addEventListener('scroll', handleScroll);\n      }\n    })\n});\n// 移除scroll监听\nemitter.on(\"onBeforeUnmount\", (value) => {\n    nextTick(()=>{\n\t\tif (domRef.value) {\n\t\t\tconst tableContainer = domRef.value.$el.querySelector('.ant-spin-container');\n\t\t\ttableContainer.removeEventListener('scroll', handleScroll);\n\t\t}\n\t})\n});\n================\n================\nFile: src/views/data-analysis/account-list/msgMaintain.vue\n================\n================\n<!-- 信息维护 -->\n<template>\n  <a-modal\n    :maskClosable=\"false\"\n    v-model:open=\"props.msgData.modalOpen\"\n    title=\"信息维护\"\n    okText=\"确定\"\n    :afterClose=\"resetFields\"\n    :confirm-loading=\"modalLoading\"\n    @ok=\"msgSubmit\"\n    width=\"460px\"\n  >\n    <a-spin :spinning=\"confirmLoading\">\n      <a-form class=\"msg-maintain\">\n        <a-form-item label=\"账号名称\" v-bind=\"validateInfos.name\">\n          <a-input v-model:value=\"msgFrom.name\" placeholder=\"请输入账号名称\" />\n        </a-form-item>\n        <a-form-item label=\"粉丝量\" v-bind=\"validateInfos.fansCount\">\n          <a-input\n            v-model:value=\"msgFrom.fansCount\"\n            placeholder=\"请输入粉丝量\"\n            @blur=\"validate('fansCount', { trigger: 'blur' }).catch(() => {})\"\n          />\n          <div class=\"get-data\" @click=\"getFansCount\">手动获取</div>\n        </a-form-item>\n        <a-form-item label=\"在网内容\" v-bind=\"validateInfos.contentCount\">\n          <a-input\n            v-model:value=\"msgFrom.contentCount\"\n            placeholder=\"请输入在网内容\"\n            @blur=\"\n              validate('contentCount', { trigger: 'blur' }).catch(() => {})\n            \"\n          />\n          <div class=\"get-data\" @click=\"getContentCount\">手动获取</div>\n        </a-form-item>\n        <a-form-item label=\"更新频率\" v-bind=\"validateInfos.updateFrequency\">\n          <a-select\n            v-model:value=\"msgFrom.updateFrequency\"\n            :options=\"updateFrequencyOpt\"\n            :field-names=\"{ label: 'name', value: 'id' }\"\n            placeholder=\"请选择更新频率\"\n          >\n          </a-select>\n          <div class=\"get-data\" @click=\"getEditHz\">手动获取</div>\n        </a-form-item>\n      </a-form></a-spin\n    >\n  </a-modal>\n</template>\n  \n<script setup>\nimport { message, Form } from \"ant-design-vue\";\nimport { fansCount, contentCount, editHz, editOpr } from \"@/api/accountList\";\nimport emitter from \"@/assets/js/emitter\";\nimport { validateNumber } from \"@/assets/js/formValidator\";\nconst props = defineProps({\n  msgData: Object,\n});\nconst confirmLoading = ref(false);\nconst modalLoading = ref(false);\nconst msgFrom = props.msgData.item;\nconst updateFrequencyOpt = ref([\n  { id: 1, name: \"7日内更新\" },\n  { id: 2, name: \"15日内更新\" },\n  { id: 3, name: \"30日内更新\" },\n  { id: 4, name: \"超过30日未更新\" },\n]);\n//手动获取粉丝量\nconst getFansCount = async () => {\n  confirmLoading.value = true;\n  const res = await fansCount({ cntEcOprAccountId: msgFrom.cntEcOprAccountId });\n  confirmLoading.value = false;\n  if (res.retCode === \"000000\") {\n    msgFrom.fansCount = res.data;\n  } else if (res.retCode === \"000031\") {\n    message.error(\"无效账号，无法获取\");\n  } else if (res.retCode === \"000020\") {\n    message.error(\"无权获取\");\n  } else if (res.retCode === \"000040\") {\n    message.error(\"审核通过的账号才能获取\");\n  } else if (res.retCode === \"990009\") {\n    message.error(\"接入融媒体的公众号才能获取\");\n  } else if (res.retCode === \"990010\") {\n    message.error(\"公众号ID为空，无法获取\");\n  } else if (res.retCode === \"990013\") {\n    message.error(\"获取失败，请手动输入\");\n  } else {\n    message.error(res.retMsg);\n  }\n};\n//手动获取在网内容\nconst getContentCount = async () => {\n  confirmLoading.value = true;\n  const res = await contentCount({\n    cntEcOprAccountId: msgFrom.cntEcOprAccountId,\n  });\n  confirmLoading.value = false;\n  if (res.retCode === \"000000\") {\n    msgFrom.contentCount = res.data;\n  } else if (res.retCode === \"000031\") {\n    message.error(\"无效账号，无法获取\");\n  } else if (res.retCode === \"000020\") {\n    message.error(\"无权获取\");\n  } else if (res.retCode === \"000040\") {\n    message.error(\"审核通过的账号才能获取\");\n  } else if (res.retCode === \"990009\") {\n    message.error(\"接入融媒体的公众号才能获取\");\n  } else if (res.retCode === \"990010\") {\n    message.error(\"公众号ID为空，无法获取\");\n  } else if (res.retCode === \"990013\") {\n    message.error(\"获取失败，请手动输入\");\n  } else {\n    message.error(res.retMsg);\n  }\n};\n//手动获取更新频率\nconst getEditHz = async () => {\n  confirmLoading.value = true;\n  const res = await editHz({ cntEcOprAccountId: msgFrom.cntEcOprAccountId });\n  confirmLoading.value = false;\n  if (res.retCode === \"000000\") {\n    msgFrom.updateFrequency = res.data;\n  } else if (res.retCode === \"000031\") {\n    message.error(\"无效账号，无法获取\");\n  } else if (res.retCode === \"000020\") {\n    message.error(\"无权获取\");\n  } else if (res.retCode === \"000040\") {\n    message.error(\"审核通过的账号才能获取\");\n  } else if (res.retCode === \"990009\") {\n    message.error(\"接入融媒体的公众号才能获取\");\n  } else if (res.retCode === \"990010\") {\n    message.error(\"公众号ID为空，无法获取\");\n  } else if (res.retCode === \"990013\") {\n    message.error(\"获取失败，请手动输入\");\n  } else {\n    message.error(res.retMsg);\n  }\n};\n//表单规则验证数据\nconst msgRules = reactive({\n  name: [\n    {\n      required: true,\n      message: \"请输入账号名称\",\n    },\n    {\n      min: 1,\n      max: 50,\n      message: \"长度在1 到 50个字符\",\n      trigger: \"change\",\n    },\n  ],\n  fansCount: [\n    {\n      required: true,\n      validator: validateNumber,\n      trigger: \"blur\",\n    },\n  ],\n  contentCount: [\n    {\n      required: true,\n      validator: validateNumber,\n      trigger: \"blur\",\n    },\n  ],\n  updateFrequency: [\n    {\n      required: true,\n      message: \"请选择更新频率\",\n      trigger: \"change\",\n    },\n  ],\n});\n\nconst useForm = Form.useForm;\nconst { resetFields, validate, validateInfos } = useForm(msgFrom, msgRules);\nconst msgSubmit = () => {\n  validate().then(async () => {\n    confirmLoading.value = true;\n    modalLoading.value = true;\n    const fromData = toRaw(msgFrom);\n    const res = await editOpr(fromData);\n    confirmLoading.value = false;\n    modalLoading.value = false;\n    if (res.retCode === \"000000\") {\n      message.success(\"操作成功！\");\n      emitter.emit(\"refreshAccount\", \"msg\");\n    } else if (res.retCode === \"990006\") {\n      message.error(\"已存在相同名称的账号\");\n    } else {\n      message.error(res.retMsg);\n    }\n  });\n};\n</script>\n  \n<style lang=\"less\" scoped>\n.msg-maintain {\n  display: block;\n  :deep(.ant-form-item-control-input-content) {\n    display: flex;\n    align-items: center;\n  }\n  .get-data {\n    font-size: 12px;\n    color: #FE8308;\n    cursor: pointer;\n    margin-left: 5px;\n  }\n}\n</style>\n================\n================\nFile: src/views/data-analysis/account-overview/accountNumber.vue\n================\n================\n<!-- 账号总数分布 -->\n<template>\n  <a-spin :spinning=\"spinLoading\">\n    <div class=\"account-number\" id=\"funnelBox\"></div>\n  </a-spin>\n</template>\n\n<script setup>\nimport * as echarts from \"echarts/core\";\nimport {\n  TitleComponent,\n  ToolboxComponent,\n  TooltipComponent,\n  LegendComponent,\n} from \"echarts/components\";\nimport { FunnelChart } from \"echarts/charts\";\nimport { CanvasRenderer } from \"echarts/renderers\";\nimport emitter from \"@/assets/js/emitter\";\necharts.use([\n  TitleComponent,\n  ToolboxComponent,\n  TooltipComponent,\n  LegendComponent,\n  FunnelChart,\n  CanvasRenderer,\n]);\nconst spinLoading = ref(true)\n//数据传值\nemitter.on(\"initChart\", (pageData) => {\n  let realdata = reactive([\n    // { value: pageData.countyAccountCount || 0, name: \"区级账号数量\" },\n    // { value: pageData.cityAccountCount || 0, name: \"市级账号数\" },\n    // { value: pageData.provinceAccountCount || 0, name: \"省级账号数\" }\n    { value: 45, name: \"账号接入数\" },\n    { value: 17, name: \"地市接入数\" },\n    { value: 8, name: \"省专接入数\" }\n  ]);\n  let length = realdata.length;\n  let cdata = [];\n  let tdata = [];\n  realdata.forEach((obj, index) => {\n    cdata.push({\n      name: obj.name,\n      value: 20 * (length - index),\n    });\n    tdata.push({\n      name: obj.name,\n      value: 10 * (length - index),\n    });\n  });\n  const setFunnel = () => {\n    let myChart = echarts.init(document.getElementById(\"funnelBox\"));\n    myChart.setOption({\n      tooltip: {\n        trigger: \"item\",\n        formatter: function(params) {\n            return `${params.name}：${realdata[params.dataIndex].value == null ? '--' : realdata[params.dataIndex].value}`\n        },\n      },\n      legend: {\n        bottom: 30,\n        // data: [\"区级账号数量\", \"市级账号数\", \"省级账号数\"],\n        data: [\"账号接入数\", \"地市接入数\", \"省专接入数\"]\n      },\n      series: [\n        {\n          name: \"Funnel\",\n          type: \"funnel\",\n          left: \"center\",\n          bottom: 90,\n          width: \"90%\",\n          min: 0,\n          max: 60,\n          minSize: \"0%\",\n          maxSize: \"100%\",\n          sort: \"ascending\", //设置漏斗图方向，\n          //'ascending-正三角'，'descending-倒三角（默认）'，'none'（表示按 data 顺序）\n          gap: 30, // 间隙\n          label: {\n            show: true,\n            normal: {\n              position: \"inside\",\n              verticalAlign: \"top\",\n              formatter: function (params) {\n                var result;\n                if (realdata[params.dataIndex].value == null) {\n                  result = params.name + \"--\";\n                } else {\n                  result = realdata[params.dataIndex].value;\n                }\n                return result;\n              },\n              textStyle: {\n                color: \"#fff\",\n              },\n            },\n          },\n          labelLine: {\n            // label在外时生效\n            length: 10,\n            lineStyle: {\n              width: 1,\n              type: \"solid\",\n            },\n          },\n          itemStyle: {\n            color: function (params) {\n              // 定义渐变色从左(#ff6f3d)到右(#ff6f3d)的渐变\n              var colorList = [\n                new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                  { offset: 0, color: \"#CCE4FF\" },\n                  { offset: 1, color: \"#93C5FD\" },\n                ]),\n                new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                  { offset: 0, color: \"#8AF5E4\" },\n                  { offset: 1, color: \"#44DEC5 \" },\n                ]),\n                new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                  { offset: 0, color: \"#FFE4E6\" },\n                  { offset: 1, color: \"#FDA7B2\" },\n                ]),\n              ];\n              return colorList[params.dataIndex];\n            },\n          },\n          emphasis: {\n            label: {\n              fontSize: 24,\n            },\n          },\n          data: cdata,\n        },\n      ],\n    });\n  };\n  nextTick(() => {\n    setFunnel();\n    spinLoading.value = false\n  });\n});\n</script>\n<style lang=\"less\" scoped>\n.account-number {\n  height: 420px;\n}\n</style>\n================\n================\nFile: src/views/data-analysis/basic-data-dashboard/lineChart.vue\n================\n================\n<!-- 单篇最高阅读量（7日内）- 折线图 -->\n<template>\n  <div class=\"line-chart\" ref=\"lineChartDom\"></div>\n</template>\n\n<script setup>\nimport * as echarts from \"echarts/core\";\nimport { GridComponent, MarkPointComponent } from \"echarts/components\";\nimport { LineChart } from \"echarts/charts\";\nimport { UniversalTransition } from \"echarts/features\";\nimport { CanvasRenderer } from \"echarts/renderers\";\nimport emitter from \"@/assets/js/emitter\";\necharts.use([\n  GridComponent,\n  MarkPointComponent,\n  LineChart,\n  CanvasRenderer,\n  UniversalTransition,\n]);\nconst props = defineProps({\n    maxReadCountSevenDay: Number,\n});\n//数据传值\nfunction shuffleArray(array) {//打乱数组\n  for (let i = array.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [array[i], array[j]] = [array[j], array[i]];\n  }\n  return array;\n}\nlet readData = reactive([props.maxReadCountSevenDay]);\nfor(let i = 1;i <= 6;i++){\n    //制造一些假数据\n    readData.push(parseInt(Math.random() * props.maxReadCountSevenDay))\n}\nshuffleArray(readData);\n// 计算最大值\nconst maxValue = Math.max.apply(null, readData);\nconst lineChartDom = ref(null);\nconst setFunnel = () => {\n  let myChart = echarts.init(lineChartDom.value);\n  myChart.setOption({\n    xAxis: {\n      type: \"category\",\n      boundaryGap: false,\n      show: false,\n    },\n    yAxis: {\n      type: \"value\",\n      show: false,\n    },\n    series: [\n      {\n        data: readData.map(function (item) {\n          // 根据比例判断是否显示符号\n          var showSymbol = item / maxValue > 0.99; // 比例阈值可以自定义\n          return {\n            value: item,\n            symbolSize: showSymbol ? 10 : 0, // 符号大小，0 表示不显示\n            symbol: \"circle\", // 可以是 'circle', 'rect', 'triangle' 等\n          };\n        }),\n        type: \"line\",\n        itemStyle: {\n          color: \"#76a2f2\",\n        },\n        smooth: 0.5,\n        markPoint: {\n          data: [\n            {\n              type: \"max\",\n            },\n          ],\n          label: {\n            fontWeight: \"bold\",\n            fontSize: 14,\n            color: \"rgba(11, 11, 11, 1)\",\n          },\n          itemStyle: {\n            color: \"transparent\",\n          },\n        },\n        areaStyle: {\n          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n            {\n              offset: 0,\n              color: \"#9BC9FE\",\n            },\n            {\n              offset: 1,\n              color: \"#FFFFFF\",\n            },\n          ]),\n        },\n      },\n    ],\n  });\n};\nonMounted(() => {\n  nextTick(() => {\n    setFunnel();\n  });\n});\n</script>\n\n<style lang=\"less\" scoped>\n.line-chart {\n  height: 240px;\n  position: relative;\n  top: -20px;\n}\n</style>\n================\n================\nFile: src/views/data-analysis/basic-data-dashboard/lineChartMom.vue\n================\n================\n<!-- 7日内容总数 - 折线图 -->\n<template>\n  <div class=\"line-chart-mom\" ref=\"lineChartMomDom\"></div>\n</template>\n  \n  <script setup>\nimport * as echarts from \"echarts/core\";\nimport { GridComponent, MarkPointComponent } from \"echarts/components\";\nimport { LineChart } from \"echarts/charts\";\nimport { UniversalTransition } from \"echarts/features\";\nimport { CanvasRenderer } from \"echarts/renderers\";\necharts.use([\n  GridComponent,\n  MarkPointComponent,\n  LineChart,\n  CanvasRenderer,\n  UniversalTransition,\n]);\nconst props = defineProps({\n  contentCountSevenDay: Number,\n  contentCountSevenDayMom: Number,\n});\n//数据传值\nfunction shuffleArray(array) {\n  //打乱数组\n  for (let i = array.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [array[i], array[j]] = [array[j], array[i]];\n  }\n  return array;\n}\nconst conCountData = reactive([props.contentCountSevenDay]);\nconst conCountDataMom = reactive([props.contentCountSevenDayMom]);\nfor (let i = 1; i <= 6; i++) {\n  //制造一些假数据\n  conCountData.push(parseInt(Math.random() * props.contentCountSevenDay));\n  conCountDataMom.push(parseInt(Math.random() * props.contentCountSevenDayMom));\n}\nshuffleArray(conCountData);\nshuffleArray(conCountDataMom);\n// 计算最大值\nconst lineChartMomDom = ref(null);\nconst setFunnel = () => {\n  let myChart = echarts.init(lineChartMomDom.value);\n  myChart.setOption({\n    xAxis: {\n      type: \"category\",\n      boundaryGap: false,\n      show: false,\n    },\n    yAxis: {\n      type: \"value\",\n      show: false,\n    },\n    series: [\n      {\n        data: conCountData,\n        type: \"line\",\n        showSymbol: false,\n        itemStyle: {\n          color: \"#6366F1\",\n        },\n        areaStyle: {\n          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n            {\n              offset: 0,\n              color: \"#9BC9FE\",\n            },\n            {\n              offset: 1,\n              color: \"#FFFFFF\",\n            },\n          ]),\n        },\n      },\n      {\n        data: conCountDataMom,\n        type: \"line\",\n        showSymbol: false,\n        itemStyle: {\n          color: props.contentCountSevenDayMom > 0 ? \"#34D399\" : \"#FBBF24\",\n        },\n      },\n    ],\n  });\n};\nonMounted(() => {\n  nextTick(() => {\n    setFunnel();\n  });\n});\n</script>\n  \n  <style lang=\"less\" scoped>\n.line-chart-mom {\n  height: 240px;\n  position: relative;\n  top: -20px;\n}\n</style>\n================\n================\nFile: src/views/index/index copy.vue\n================\n================\n<template>\n  <a-spin :spinning=\"pageLoading\">\n    <a-layout class=\"index-page\">\n      <a-layout-header>\n        <div class=\"header-left\">\n          <!-- logo平台 -->\n          <div class=\"layout-logo-box\">\n            <img src=\"@/assets/image/logo-img.png\" alt=\"\" />\n            <span>内容集中化运营平台</span>\n          </div>\n          <!-- 当前省市位置 -->\n          <div class=\"user-box\">\n            <div class=\"user-dept-tree\">\n              <img\n                src=\"@/assets/image/icon-location.png\"\n                class=\"icon-location\"\n                alt=\"\"\n              />\n              <span class=\"location-name\">当前</span>\n              <a-cascader\n                class=\"user-cascader\"\n                :allowClear=\"false\"\n                v-model:value=\"institutionVal\"\n                :field-names=\"{ label: 'label', value: 'id' }\"\n                :options=\"userData?.deptTree\"\n                @change=\"changeInstitution\"\n              />\n            </div>\n          </div>\n          <div class=\"header-menu\">\n            <a-menu\n              v-model:selectedKeys=\"heaStedK\"\n              theme=\"dark\"\n              mode=\"horizontal\"\n            >\n              <a-menu-item\n                v-for=\"item in userData.menuTree\"\n                :key=\"item.id\"\n                @click=\"changMenu(item)\"\n                >{{ item.menuName }}</a-menu-item\n              >\n            </a-menu>\n          </div>\n        </div>\n        <div class=\"header-right\">\n          <!-- 用户信息 -->\n          <div class=\"header-box\">{{ userData.mobile }}，您好</div>\n          <!-- 用户头像 -->\n          <div class=\"user-img-box\">\n            <img\n              :src=\"editData.imgs[editData.img < 4 ? editData.img : 0].url\"\n              class=\"user-img\"\n            />\n            <div class=\"user-edit\">\n              <EditOutlined class=\"edit-icon\" @click=\"showModal\" />\n            </div>\n          </div>\n        </div>\n      </a-layout-header>\n      <a-layout-content>\n        <a-layout>\n          <a-layout-sider\n            class=\"layout-menu-sider\"\n            v-show=\"heaStedK[0] !== '1'\"\n          >\n            <a-menu\n              v-model:selectedKeys=\"leftMenuState.selectedKeys\"\n              :open-keys=\"leftMenuState.openKeys\"\n              @openChange=\"onOpenChange\"\n              mode=\"inline\"\n              :items=\"currenTmenus\"\n              @click=\"onMenuFun\"\n            />\n            <div class=\"layout-loginout-box\" @click=\"loginOut\">\n              <PoweroffOutlined />\n              <span>退出</span>\n            </div>\n          </a-layout-sider>\n          <a-layout-content class=\"page-content\" :class=\"{'index-page-content':heaStedK[0] === '1'}\">\n            <!-- 面包屑 -->\n              <div\n                class=\"breadcrumb-box\"\n                v-show=\"heaStedK[0] !== '1'\"\n              >\n              <div v-for=\"item in breadcrumb\" :key=\"item\">\n                  <CaretUpOutlined />\n                  <span class=\"breadcrumb-name\">{{ item }}</span>\n              </div>\n              <CaretUpOutlined />\n            </div>\n            <!-- 页面内容 -->\n            <router-view></router-view>\n            <indexContent v-show=\"heaStedK[0] === '1'\"/>\n          </a-layout-content>\n        </a-layout>\n      </a-layout-content>\n    </a-layout>\n  </a-spin>\n  <a-config-provider\n    :theme=\"{\n      token: {\n        colorPrimary: '#FE8308',\n        borderRadius: 4,\n      },\n    }\"\n  >\n  </a-config-provider>\n  <eidtImg v-if=\"editData.modalOpen\" :editData=\"editData\" />\n</template>\n<script setup>\nimport { ExclamationCircleOutlined } from \"@ant-design/icons-vue\";\nimport { createVNode } from \"vue\";\nimport { Modal, message } from \"ant-design-vue\";\nimport { logout, switchDept } from \"@/api/index\";\nimport indexContent from \"./indexContent.vue\";\nimport emitter from \"@/assets/js/emitter\";\nimport eidtImg from \"./upload-img/eidtImg.vue\";\nimport img1 from \"@/assets/image/user-img-0.png\";\nimport img2 from \"@/assets/image/user-img-1.png\";\nimport img3 from \"@/assets/image/user-img-2.png\";\nimport img4 from \"@/assets/image/user-img-3.png\";\nconst router = useRouter();\nconst route = useRoute();\nconst breadcrumb = ref([]);\n//菜单数据\nconst leftMenuState = reactive({\n  rootSubmenuKeys: [], //当前左侧菜单数组的父级id\n  openKeys: [], //当前展开的 SubMenu 菜单项 key 数组\n  selectedKeys: [], //当前选中的菜单项 key 数组\n});\nconst heaStedK = ref(['1']); //header导航选中的菜单项 key 数组\n//点击菜单\nconst currenTmenus = ref([]); //当前左侧菜单数组\nconst noMenus = [10, 13, 30, 31, 32, 33];\nconst changMenu = (item) => {\n  // heaStedK.value[0] = item.id;\n  // localStorage.setItem('heaStedK',JSON.stringify(heaStedK.value))\n  if (!item.children) {\n    router.push(item.address);\n    // leftMenuState.selectedKeys = [];\n  } else {\n    // currenTmenus.value = item.children;\n    // leftMenuState.rootSubmenuKeys = [];\n    // leftMenuState.selectedKeys = [];\n    // currenTmenus.value.forEach((val) => {\n    //   leftMenuState.rootSubmenuKeys.push(val.id);\n    // });\n    // if (currenTmenus.value[0].children) {\n      // breadcrumb.value = [item.menuName,item.children[0].menuName,item.children[0].children[0].menuName];\n      //如果当前第一列有children则展开并且选中\n      // leftMenuState.openKeys.push(currenTmenus.value[0].id);\n      // leftMenuState.selectedKeys.push(currenTmenus.value[0].children[0].id);\n      // router.push(currenTmenus.value[0].children[0].address);\n    // } else {\n      // breadcrumb.value = [item.menuName,item.children[0].menuName];\n      // leftMenuState.selectedKeys.push(currenTmenus.value[0].id);\n      router.push(item.children[0].address);\n    // }\n    // localStorage.setItem('leftMenuState',JSON.stringify(leftMenuState))\n    // localStorage.setItem('breadcrumb',JSON.stringify(breadcrumb.value))\n    // localStorage.setItem('currenTmenus',JSON.stringify(currenTmenus.value))\n  }\n};\n//用户数据\nconst userData = reactive(JSON.parse(localStorage.getItem(\"userData\")));\n//用户选择的机构\nconst institutionVal = ref([]);\n//初始化用户机构\nlet unseInstitution = ref([]);\nconst pageLoading = ref(false);\n//切换机构\nconst changeInstitution = (_value, selectedOptions) => {\n  pageLoading.value = true;\n  unseInstitution.value = selectedOptions.map((o) => o.id);\n  //缓存部门数据\n  localStorage.setItem(\n    \"unseInstitution\",\n    JSON.stringify(unseInstitution.value)\n  );\n  toggleInstitution();\n};\n// 使用递归函数遍历树形菜单结构，给菜单添加属性\nfunction findNodeById(treeData,address) {\n  // 遍历当前层级的所有节点\n  for (var i = 0; i < treeData.length; i++) {\n    var node = treeData[i];\n    node.title = node.menuName;\n    node.label = node.menuName;\n    \n    if(!node.parentId || node.children.length === 0) {\n      node.address = address?address+'/'+node.address:'/'+node.address;\n    } else {\n      node.address = address;\n    }\n    node.key = node.id.toString(); \n    node.id = node.id.toString();\n    if(node.children.length === 0) {\n      node.children = null;\n    }\n    // 如果当前节点有子节点，则递归调用当前函数继续查找子节点\n    if (node.children && node.children.length > 0) {\n      findNodeById(node.children,node.address);\n    }\n  }\n  return treeData;\n}\n//切换机构后刷新菜单\nconst toggleInstitution = async () => {\n  const params = {\n    province: institutionVal.value[0],\n    city: institutionVal.value[1],\n  };\n  const res = await switchDept(params);\n  pageLoading.value = false;\n  if (res.retCode === \"000000\") {\n    userData.menuTree = res.data;\n    //重新缓存用户数据\n    localStorage.clear()\n    userData.menuTree = findNodeById(res.data,null);\n    localStorage.setItem(\"userData\", JSON.stringify(userData));\n    breadcrumb.value = [];\n    heaStedK.value = ['1'];\n    if (route.path === \"/homePage\") {\n      router.go(0);\n    } else {\n      router.push(\"/homePage\");\n    }\n  } else {\n    message.error(res.retMsg);\n  }\n};\nonBeforeMount(() => {\n  //判断当前省市数据\n  if (localStorage.getItem(\"unseInstitution\")) {\n    institutionVal.value = JSON.parse(localStorage.getItem(\"unseInstitution\"));\n  } else {\n    institutionVal.value = [\n      userData.deptTree[0].id,\n      userData.deptTree[0].children[0].id,\n    ];\n  }\n  //判断是否有当前菜单\n  // if (JSON.parse(localStorage.getItem('heaStedK'))) {\n  //   Object.assign(leftMenuState,JSON.parse(localStorage.getItem('leftMenuState')))\n  //   breadcrumb.value = JSON.parse(localStorage.getItem('breadcrumb'))\n  //   currenTmenus.value = JSON.parse(localStorage.getItem('currenTmenus'))\n  //   heaStedK.value = JSON.parse(localStorage.getItem('heaStedK'))\n  // }\n});\n//切换左边菜单(展开/关闭的回调)\nconst onOpenChange = (openKeys) => {\n  const latestOpenKey = openKeys.find(\n    (key) => leftMenuState.openKeys.indexOf(key) === -1\n  );\n  if (leftMenuState.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {\n    leftMenuState.openKeys = openKeys;\n  } else {\n    leftMenuState.openKeys = latestOpenKey ? [latestOpenKey] : [];\n  }\n};\n// 递归函数，用于查找某个节点的所有父节点\nfunction findAncestors(tree, nodeId, ancestors = []) {\n  for (let node of tree) {\n    // console.log(node.id,nodeId)\n    if (node.id === nodeId) {\n      return ancestors;\n    }\n    if (node.children && node.children.length > 0) {\n      const found = findAncestors(node.children, nodeId, [\n        ...ancestors,node.menuName\n      ]);\n      if (found) {\n        return found;\n      }\n    }\n  }\n  return null;\n}\nfunction getMenuData(tree, nodeUrl, getName,ancestors = []) {\n  for (let node of tree) {\n    if (node.address.indexOf(nodeUrl) > -1) {\n      if(getName === 'menuName') {\n        ancestors.push(node[getName])\n      }\n      return ancestors;\n    }\n    if (node.children && node.children.length > 0) {\n      const found = getMenuData(node.children, nodeUrl,getName, [\n        ...ancestors,node[getName]\n      ]);\n      if (found) {\n        return found;\n      }\n    }\n  }\n  return null;\n}\n// 监听路由更改菜单数据\nwatch(()=>route.path,(newVal,oldVal)=>{\n  if(route.path !== '/homePage') {\n    breadcrumb.value = getMenuData(userData.menuTree, newVal.split('/')[2],'menuName')\n    heaStedK.value[0] = getMenuData(userData.menuTree, newVal.split('/')[2],'id')[0]\n    currenTmenus.value = getMenuData(userData.menuTree, newVal.split('/')[2],'children')[0]\n    console.log(currenTmenus.value)\n  }\n})\nconst onMenuFun = (item) => {\n  router.push(item.item.address);\n  // breadcrumb.value = findAncestors(userData.menuTree, item.key);\n  // breadcrumb.value.push(item.item.menuName)\n  leftMenuState.selectedKeys = [item.key]\n  localStorage.setItem('leftMenuState',JSON.stringify(leftMenuState))\n  // localStorage.setItem('breadcrumb',JSON.stringify(breadcrumb.value))\n};\n\n//头像编辑\nconst editData = reactive({\n  modalOpen: false,\n  img: userData?.headImg ? parseInt(userData?.headImg.split(\"/\")[3]) : 0,\n  imgs: [\n    { url: img1, id: 0 },\n    { url: img2, id: 1 },\n    { url: img3, id: 2 },\n    { url: img4, id: 3 },\n  ],\n});\nconst showModal = () => {\n  editData.modalOpen = true;\n};\nemitter.on(\"editImg\", (value) => {\n  editData.modalOpen = false;\n  editData.img = value;\n  userData.headImg = `https://mcn.i139.cn/${value}`;\n  localStorage.setItem(\"userData\", JSON.stringify(userData));\n});\n//登出\nconst loginOut = () => {\n  Modal.confirm({\n    title: \"系统提示\",\n    icon: createVNode(ExclamationCircleOutlined),\n    content: \"您确定要退出吗？\",\n    okText: \"确认\",\n    okType: \"danger\",\n    cancelText: \"取消\",\n    async onOk() {\n      const res = await logout();\n      if (res.retCode === \"000000\") {\n        router.push(\"/login\");\n      } else {\n        message.error(res.retMsg);\n      }\n    },\n  });\n};\n</script>\n<style lang=\"less\" scoped>\n.ant-layout {\n  height: 100vh;\n  background: #fdfdfd;\n  .page-content {\n    padding: 10px 10px 0;\n  }\n  .index-page-content {\n    padding: 0;\n  }\n  .ant-layout-header {\n    height: auto;\n    background: #fff;\n    padding-left: 10px;\n    display: flex;\n    border-bottom: 1px solid #e2e8f0;\n    justify-content: space-between;\n    padding: 0 20px 0 10px;\n    .header-left,\n    .header-right {\n      display: flex;\n      align-items: center;\n    }\n    .header-left {\n      flex: 1;\n    }\n    .header-right {\n      width: 170px;\n    }\n    .header-box {\n      font-weight: bold;\n      border-radius: 2px;\n      position: relative;\n      overflow: hidden;\n      font-size: 14px;\n      color: rgba(1, 23, 56, 0.5);\n    }\n    .title-img {\n      width: 99px;\n      position: absolute;\n      right: 0;\n      top: 16px;\n    }\n    .user-img-box {\n      width: 40px;\n      height: 40px;\n      position: relative;\n      border-radius: 7px;\n      overflow: hidden;\n    }\n    .user-edit {\n      width: 40px;\n      height: 40px;\n      background-color: rgb(0 0 0 / 25%);\n      position: absolute;\n      top: 0;\n      left: 0;\n      display: none;\n    }\n    .edit-icon {\n      position: absolute;\n      bottom: 3px;\n      right: 3px;\n      cursor: pointer;\n    }\n    .user-img-box:hover {\n      .user-edit {\n        display: block;\n      }\n    }\n    .user-img {\n      width: 40px;\n      vertical-align: top;\n    }\n  }\n\n  .header-menu {\n    margin-left: 50px;\n    .ant-menu-dark.ant-menu-horizontal > :deep(.ant-menu-item) {\n      color: #011738;\n      font-size: 16px;\n      padding: 0;\n      padding-right: 20px;\n    }\n    .ant-menu-dark.ant-menu-horizontal > :deep(.ant-menu-item-selected),\n    .ant-menu-dark.ant-menu-horizontal > :deep(.ant-menu-submenu-selected) {\n      color: #fe8308;\n      background: #fff;\n    }\n  }\n  .layout-logo-box {\n    font-size: 16px;\n    color: #011738;\n    font-weight: bold;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding-right: 50px;\n    img {\n      width: 40px;\n    }\n    span {\n      margin-left: 8px;\n    }\n  }\n  .user-box {\n    font-size: 14px;\n    color: #011738;\n\n    .user-dept-tree {\n      display: flex;\n      align-items: center;\n      position: relative;\n    }\n    .user-cascader {\n      width: 200px;\n    }\n    .icon-location {\n      width: 24px;\n      vertical-align: top;\n    }\n    .location-name {\n      font-size: 14px;\n      color: rgba(1, 23, 56, 0.6);\n      padding-right: 5px;\n    }\n  }\n  .layout-loginout-box {\n    width: 100%;\n    height: 40px;\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 14px;\n    color: #94a3b8;\n    border-top: 1px solid rgba(148, 163, 184, 0.5);\n    background: #fff;\n    cursor: pointer;\n    span {\n      margin-left: 10px;\n    }\n    .anticon-poweroff {\n      font-size: 18px;\n    }\n  }\n\n  .layout-menu-sider {\n    .ant-menu {\n      height: calc(100vh - 67px);\n      overflow-y: auto;\n    }\n  }\n  .ant-layout-sider {\n    flex: 0 0 240px !important;\n    max-width: 240px !important;\n    min-width: 200px;\n    width: 240px !important;\n    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.13);\n  }\n  :deep(.ant-layout-sider-children) {\n    background: #fff;\n  }\n  :deep(.base-icon) {\n    font-size: 20px;\n    margin-right: 10px;\n    cursor: pointer;\n  }\n  .ant-menu-dark {\n    color: rgba(1, 23, 56, 0.5);\n    background: #fff;\n  }\n\n  :deep(.ant-menu-inline) {\n    .ant-menu-inline {\n      background: #fff;\n    }\n    .ant-menu-item {\n      color: #011738;\n    }\n    .ant-menu-item-selected {\n      background: #fff;\n      color: #fe8308;\n    }\n  }\n  :deep(.ant-menu-light) {\n    .ant-menu-submenu-selected > .ant-menu-submenu-title {\n      color: #fe8308;\n    }\n    .ant-menu-item::after {\n      height: 30px;\n      top: 5px;\n      right: -1px;\n      border: 2px solid #fe8308;\n    }\n  }\n  .ant-menu-light.ant-menu-inline .ant-menu-item::after :deep(.ant-menu-dark) {\n    .ant-menu-submenu-selected > .ant-menu-submenu-title {\n      color: rgba(1, 23, 56, 0.5);\n    }\n  }\n  .breadcrumb-box {\n    margin-bottom: 10px;\n    display: flex;\n    .breadcrumb-name {\n      margin-right: 1px;\n      color: rgba(1, 23, 56, 0.5);\n    }\n    .anticon-caret-up {\n      transform: rotate(128deg);\n      position: relative;\n      top: 4px;\n      color: rgba(1, 23, 56, 0.5);\n    }\n    .anticon-caret-up:last-child{\n      color: rgba(1, 23, 56, 1);\n    }\n  }\n}\n</style>\n\n<a-menu\n            v-model:selectedKeys=\"leftMenuState.selectedKeys\"\n            @openChange=\"onOpenChange\"\n            :open-keys=\"leftMenuState.openKeys\"\n            mode=\"inline\"\n          >\n          <template v-for=\"item in currenTmenus\" :key=\"item.id\">\n            <a-sub-menu v-if=\"item.children\" :key=\"item.id\">\n              <template #title>\n                <span>\n                  {{item.menuName}}\n                </span>\n              </template>\n              <template #icon>\n                <img class=\"menu-icon\" :src=\"getAssetURL(item,true)\" alt=\"\" v-if=\"leftMenuState.openSelectedKeys === item.id\">\n                <img class=\"menu-icon\" :src=\"getAssetURL(item)\" alt=\"\" v-else>\n              </template>\n              <template v-for=\"cItem in item.children\" :key=\"cItem.id\">\n                <!-- 二级菜单 -->\n                <a-sub-menu v-if=\"cItem.children\" :key=\"cItem.id\">\n                  <template #title>\n                    <span>{{ cItem.menuName }}</span>\n                  </template>\n                  <!-- 循环显示三级菜单项 -->\n                    <a-menu-item v-for=\"j of cItem.children\" :key=\"j.id\" @click=\"onMenuFun(j)\">\n                      <span>{{ j.menuName }}</span>\n                    </a-menu-item>\n                </a-sub-menu>\n                <!-- 如果没有三级菜单，则显示二级菜单项 -->\n                <template v-else>\n                  <a-menu-item\n                    :key=\"cItem.id\"\n                    @click=\"onMenuFun(cItem)\"\n                  >\n                    <span>{{ cItem.menuName }}</span>\n                  </a-menu-item>\n                </template>\n              </template>\n            </a-sub-menu>\n            <a-menu-item :key=\"item.id\" v-else @click=\"onMenuFun(item)\">\n              <template #title>\n                <span>\n                  {{item.menuName}}\n                </span>\n              </template>\n              <template #icon>\n                <img class=\"menu-icon\" :src=\"getAssetURL(item,true)\" alt=\"\" v-if=\"leftMenuState.openSelectedKeys === item.id\">\n                <img class=\"menu-icon\" :src=\"getAssetURL(item)\" alt=\"\" v-else>\n              </template>\n            </a-menu-item>\n          </template>\n          </a-menu>\n\n\n\n\n          <a-menu\n            v-model:selectedKeys=\"leftMenuState.selectedKeys\"\n            @openChange=\"onOpenChange\"\n            :open-keys=\"leftMenuState.openKeys\"\n            mode=\"inline\"\n          >\n          <template v-for=\"item in currenTmenus\" :key=\"item.id\">\n            <a-sub-menu v-if=\"item.children\" :key=\"item.id\">\n              <template #title>\n                <span>\n                  {{item.menuName}}\n                </span>\n              </template>\n              <template #icon>\n                <img class=\"menu-icon\" :src=\"getAssetURL(item,true)\" alt=\"\" v-if=\"leftMenuState.openSelectedKeys === item.id\">\n                <img class=\"menu-icon\" :src=\"getAssetURL(item)\" alt=\"\" v-else>\n              </template>\n              <template v-for=\"cItem in item.children\" :key=\"cItem.id\">\n                <!-- 二级菜单 -->\n                <a-sub-menu v-if=\"cItem.children\" :key=\"cItem.id\">\n                  <template #title>\n                    <span>{{ cItem.menuName }}</span>\n                  </template>\n                  <!-- 循环显示三级菜单项 -->\n                    <a-menu-item v-for=\"j of cItem.children\" :key=\"j.id\" @click=\"onMenuFun(j)\">\n                      <span>{{ j.menuName }}</span>\n                    </a-menu-item>\n                </a-sub-menu>\n                <!-- 如果没有三级菜单，则显示二级菜单项 -->\n                <template v-else>\n                  <a-menu-item\n                    :key=\"cItem.id\"\n                    @click=\"onMenuFun(cItem)\"\n                  >\n                    <span>{{ cItem.menuName }}</span>\n                  </a-menu-item>\n                </template>\n              </template>\n            </a-sub-menu>\n            <a-menu-item :key=\"item.id\" v-else @click=\"onMenuFun(item)\">\n              <template #icon v-if=\"leftMenuState.openSelectedKeys === item.id\">\n                <img class=\"menu-icon\" :src=\"getAssetURL(item,true)\" alt=\"\">\n              </template>\n              <template #icon v-else>\n                <img class=\"menu-icon\" :src=\"getAssetURL(item)\" alt=\"\" >\n              </template>\n              <span>\n                {{item.menuName}}\n              </span>\n            </a-menu-item>\n          </template>\n          </a-menu>\n================\n================\nFile: src/views/platform-manage/dept-manage/setMenu.vue\n================\n================\n<!-- 授权省份菜单 -->\n<template>\n  <a-modal\n    :maskClosable=\"false\"\n    v-model:open=\"props.setData.modalOpen\"\n    title=\"授权菜单\"\n    okText=\"确定\"\n    :confirm-loading=\"modalLoading\"\n    @ok=\"setAccSubmit\"\n  >\n    <a-tree\n      v-model:checkedKeys=\"checkedKeys\"\n      checkable\n      :fieldNames=\"{ children: 'children', title: 'label', key: 'id' }\"\n      :tree-data=\"props.setData.deptListDatas\"\n    >\n    </a-tree>\n  </a-modal>\n</template> \n  \n<script setup>\nimport { message } from \"ant-design-vue\";\nimport { menuAllSave } from \"@/api/deptManage\";\nimport emitter from \"@/assets/js/emitter\";\nconst props = defineProps({\n  setData: Object,\n  accountId: Array,\n});\n//账号树形控件\n//这两个值只接受ref的值\nconst checkedKeys = ref([]); //选中复选框\nObject.assign(checkedKeys.value, props.accountId);\n//筛选选中的值\nfunction getOnlyLeafKeys(selectedKeys, treeData) {\n  const onlyLeafKeys = [];\n \n  function traverse(nodes) {\n    nodes.forEach(node => {\n      if (selectedKeys.includes(node.id) && (!node.children || node.children.length === 0)) {\n        onlyLeafKeys.push(node.id);\n      }\n      if (node.children) {\n        traverse(node.children);\n      }\n    });\n  }\n  traverse(treeData);\n  return onlyLeafKeys;\n}\n\n//提交数据\nconst modalLoading = ref(false);\nconst setAccSubmit = async () => {\n  const checkedIds = getOnlyLeafKeys(checkedKeys.value, props.setData.deptListDatas)\n  if (!checkedIds.length) {\n    message.error(\"请选择需要配置的账号\");\n    return;\n  }\n  modalLoading.value = true;\n  const res = await menuAllSave({\n    deptId: props.setData.id,\n    menu: checkedIds.join(\",\"),\n  });\n  modalLoading.value = false;\n  if (res.retCode === \"000000\") {\n    message.success(\"操作成功！\");\n    emitter.emit(\"setMenu\", true);\n    emitter.emit(\"setMenu\", true);\n  } else {\n    message.error(res.retMsg);\n  }\n};\n</script>\n================\n================\nFile: src/views/platform-manage/user-manage/accreditMenu.vue\n================\n================\n<!-- 账号管理菜单授权 -->\n<template>\n  <a-modal\n    :maskClosable=\"false\"\n    v-model:open=\"props.setMenuData.modalOpen\"\n    title=\"授权菜单\"\n    okText=\"确定\"\n    :afterClose=\"resetFields\"\n    :confirm-loading=\"modalLoading\"\n    @ok=\"addEditSubmit\"\n  >\n    <a-spin :spinning=\"spinningLoading\">\n      <a-form>\n        <a-form-item label=\"机构\" v-bind=\"validateInfos.deptId\">\n          <a-select\n            v-model:value=\"addEditFrom.deptId\"\n            :options=\"props.setMenuData.provinceTree\"\n            :field-names=\"{ label: 'name', value: 'id' }\"\n            placeholder=\"请选择机构\"\n            @change=\"changeProvince\"\n          >\n          </a-select>\n        </a-form-item>\n        <a-form-item label=\"菜单\" v-bind=\"validateInfos.menu\">\n          <a-tree-select\n            v-model:value=\"addEditFrom.menu\"\n            :tree-data=\"menuTree\"\n            tree-checkable\n            allow-clear\n            :placeholder=\"addEditFrom.deptId ? '请选择菜单' : '请先选择机构'\"\n            :maxTagCount=\"1\"\n            :field-names=\"{ label: 'label', value: 'id' }\"\n            tree-node-filter-prop=\"label\"\n          />\n        </a-form-item>\n      </a-form>\n    </a-spin>\n  </a-modal>\n</template>\n  <script setup>\nimport { message, Form } from \"ant-design-vue\";\nimport { menuDeptTree, menuRelSave, menuRelDetail } from \"@/api/userManage\";\nconst props = defineProps({\n  setMenuData: Object,\n});\n\nconst modalLoading = ref(false);\nconst spinningLoading = ref(false);\nconst addEditFrom = props.setMenuData.list;\nconst menuTree = ref([]);\n// 获取当前机构已授权的菜单\nasync function getMenuRelDetail(deptId) {\n  const params = {\n    userId: props.setMenuData.list.id,\n    deptId: deptId\n  }\n  const menuRes = await menuRelDetail(params);\n  spinningLoading.value = false;\n  if (menuRes.retCode === \"000000\") {\n    addEditFrom.menu = menuRes.data;\n  } else {\n    message.error(menuRes.retMsg);\n  }\n}\n// 获取菜单树结构\nasync function getMenuTree(deptId) {\n  spinningLoading.value = true;\n  const menuRes = await menuDeptTree({ deptId });\n  if (menuRes.retCode === \"000000\") {\n    menuTree.value = menuRes.data;\n    getMenuRelDetail(deptId)\n  } else {\n    message.error(menuRes.retMsg);\n  }\n}\n\nconst changeProvince = (value) => {\n    getMenuTree(value)\n};\n//表单规则验证数据\nconst addEditRules = reactive({\n  menu: [\n    {\n      required: true,\n      trigger: \"change\",\n      message: \"请选择菜单\",\n    },\n  ],\n  deptId: [\n    {\n      required: true,\n      trigger: \"change\",\n      message: \"请选择机构\",\n    },\n  ],\n});\n\nconst useForm = Form.useForm;\nconst { resetFields, validate, validateInfos } = useForm(\n  addEditFrom,\n  addEditRules\n);\n//筛选选中的值\nfunction getOnlyLeafKeys(selectedKeys, treeData) {\n  const onlyLeafKeys = [];\n \n  function traverse(nodes) {\n    nodes.forEach(node => {\n      if (selectedKeys.includes(node.id) && (!node.children || node.children.length === 0)) {\n        onlyLeafKeys.push(node.id);\n      }\n      if (node.children) {\n        traverse(node.children);\n      }\n    });\n  }\n  traverse(treeData);\n  return onlyLeafKeys;\n}\n//菜单授权\nconst addEditSubmit = () => {\n  validate()\n    .then(async () => {\n      modalLoading.value = spinningLoading.value = true;\n      let res = {};\n      const { menu, deptId, id } = toRaw(addEditFrom);\n      const checkedIds = getOnlyLeafKeys(menu, menuTree.value)\n      const fromData = {\n        userId: id,\n        menu: checkedIds.join(\",\"),\n        deptId\n      };\n      res = await menuRelSave(fromData);\n      modalLoading.value = spinningLoading.value = false;\n      if (res.retCode === \"000000\") {\n        message.success(\"操作成功！\");\n        props.setMenuData.modalOpen = false;\n      } else {\n        message.error(res.retMsg);\n      }\n    })\n    .catch((err) => {\n      console.log(\"error\", err);\n    });\n};\n</script>\n================\n================\nFile: src/views/platform-manage/user-manage/deptManage.vue\n================\n================\n<!-- 机构管理 -->\n<template>\n  <div ref=\"pageContent\" class=\"dept-content page-content\">\n    <a-table \n    :columns=\"columns\" \n    :data-source=\"dataSource\" \n    borderedsize=\"small\"\n    :pagination=\"false\"\n    bordered\n    :loading=\"pageLoading\"\n    :scroll=\"{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }\"\n    >\n      <template #bodyCell=\"{ column, record }\">\n        <template v-if=\"column.dataIndex === 'county'\">\n          {{ record.county }}\n          <MenuOutlined @click=\"showModaAccount(record)\"/>\n      </template>\n      </template>\n    </a-table>\n  </div>\n  <setMenu v-if=\"setData.modalOpen\" :accountId=\"accountId\" :setData=\"setData\" />\n</template>\n<script setup>\nimport { deptList, menuAllTree, menuAllDetail } from \"@/api/deptManage\";\nimport setMenu from './dept-manage/setMenu.vue';\nimport emitter from '@/assets/js/emitter';\n\nconst dataSource = ref([]);\nconst countyfilters = ref([])\nconst pageLoading = ref(false)\n//定义合并的时期变量\nconst rowCountyArr = ref([]);\nconst roadCityArr = ref([]);\n//合并行\nconst getRowspan = (dataScroce, filed) => {\n  let spanArr = [];\n  let position = 0;\n  dataScroce.forEach((item, index) => {\n    if (index === 0) {\n      spanArr.push(1);\n      position = 0;\n    } else {\n      //需要合并的地方判断\n      if (dataScroce[index][filed] === dataScroce[index - 1][filed]) {\n        spanArr[position] += 1;\n        spanArr.push(0);\n      } else {\n        spanArr.push(1);\n        position = index;\n      }\n    }\n  });\n  return spanArr;\n};\n// 数据处理的方法\nconst toDataSource = (resData) => {\n  let arr = [];\n  resData.map((responseDataItem) => {\n    countyfilters.value.push({\n      text: responseDataItem.label,\n      value: responseDataItem.label\n    })\n    responseDataItem.children.map((item) => {\n      arr = [\n        ...arr,\n        {\n          county: responseDataItem.label,\n          city: item.label,\n          id: responseDataItem.id\n          // channel: it.label,\n        },\n      ];\n      // item.children.map((it) => {\n      //   console.log(it)\n      //   arr = [\n      //     ...arr,\n      //     {\n      //       county: responseDataItem.label,\n      //       city: item.label,\n      //       id: responseDataItem.id,\n      //       channel: it.label,\n      //     },\n      //   ];\n      // });\n    });\n  });\n  dataSource.value = arr;\n  \n  rowCountyArr.value = getRowspan(dataSource.value, \"county\");\n  roadCityArr.value = getRowspan(dataSource.value, \"city\");\n};\n// 获取列表数据\nconst getDeptList = async () => {\n  pageLoading.value = true\n  const res = await deptList();\n  pageLoading.value = false\n  if (res.retCode === \"000000\") {\n    toDataSource(res.data);\n  } else {\n    message.error(res.retMsg);\n  }\n};\n// useMousePosition()\nonMounted(()=>{\n  getDeptList();\n})\nconst columns = [\n  {\n    title: \"省级部门\",\n    dataIndex: \"county\",\n    customCell: (record, rowIndex, column) => {\n      return {\n        rowSpan: rowCountyArr.value[rowIndex],\n        style: {\n          \"text-align\": \"center\", // 单元格文本居中\n          \"vertical-align\": \"middle\", // 单元格内容垂直居中\n        },\n      };\n    },\n    filterMultiple: false,\n    onFilter: (value, record) => record.county.indexOf(value) === 0,\n    filters: countyfilters.value\n  },\n  {\n    title: \"市级部门\",\n    dataIndex: \"city\"\n    // customCell: (record, rowIndex, column) => {\n    //   return {\n    //     rowSpan: roadCityArr.value[rowIndex],\n    //     style: {\n    //       \"text-align\": \"center\", // 单元格文本居中\n    //       \"vertical-align\": \"middle\", // 单元格内容垂直居中\n    //     },\n    //   };\n    // },\n  }\n  // ,\n  // {\n  //   title: \"区级部门\",\n  //   dataIndex: \"channel\",\n  // }\n];\n\n//分配账号\nconst setData = reactive({\n  modalOpen: false,\n  id: null,\n  deptListDatas: []\n})\n\nconst showModaAccount = async (item) => {\n  pageLoading.value = true;\n  const res = await menuAllTree();\n  if (res.retCode === \"000000\") {\n    setData.id = item.id;\n    // let treeData = res.data\n    // treeData.forEach((el,i) => {\n    //   treeData[i].id = (i+1) * 100000\n    // });\n    Object.assign(setData.deptListDatas,res.data)\n    getMenuAllDetail(item.id)\n  } else {\n    message.error(res.retMsg);\n  }\n};\n//获取省份关联菜单详情\nconst accountId = ref([])\nconst getMenuAllDetail = async (deptId) => {\n  const res = await menuAllDetail({deptId});\n  if (res.retCode === \"000000\") {\n    pageLoading.value = false;\n    accountId.value = res.data || [];\n    setData.modalOpen = true;\n  } else {\n    message.error(res.retMsg);\n  }\n}\n//设置菜单后更新数据\nemitter.on('setMenu',()=>{\n  setData.modalOpen = false;\n}) \n//组件卸载后销毁emitter\nonUnmounted(()=>{\n  emitter.off('setMenu')\n})\n</script>\n<style lang=\"less\" scoped>\n.dept-content {\n  background: #fff;\n  padding: 10px;\n  height: calc(100vh - 120px);\n  .anticon-menu {\n    font-size: 12px;\n    color: #FE8308;\n  }\n}\n</style>\n================\n================\nFile: src/views/platform-manage/user-manage/setAccount.vue\n================\n================\n<!-- 账号授权 -->\n<template>\n  <a-modal\n    :maskClosable=\"false\"\n    v-model:open=\"props.setData.modalOpen\"\n    title=\"适用账号\"\n    okText=\"确定\"\n    :confirm-loading=\"modalLoading\"\n    @ok=\"setAccSubmit\"\n  >\n  <a-tree\n    v-model:checkedKeys=\"checkedKeys\"\n    checkable\n    :fieldNames=\"{ children: 'children', title: 'label', key: 'id' }\"\n    :tree-data=\"props.setData.deptListDatas\"\n  >\n  </a-tree>\n  </a-modal>\n</template> \n\n<script setup>\nimport { message } from \"ant-design-vue\";\nimport { relSave } from '@/api/userManage';\nimport emitter from \"@/assets/js/emitter\";\nconst props = defineProps({\n  setData: Object,\n  accountId: Array\n});\n//账号树形控件\n//这两个值只接受ref的值\nconst checkedKeys = ref([]); //选中复选框\nObject.assign(checkedKeys.value,props.accountId)\n//提交数据\nconst modalLoading = ref(false)\nconst setAccSubmit = async () => {\n  let checkedIds = []\n  checkedKeys.value.forEach((el,i) => {\n    if(el < 100000) {\n      checkedIds.push(el)\n    }\n  });\n  if(!checkedIds.length) {\n    message.error(\"请选择需要配置的账号\");\n    return\n  }\n  const res = await relSave({ userId: props.setData.userId, accountId: checkedIds.join(',') });\n  modalLoading.value = false\n  if (res.retCode === \"000000\") {\n    message.success(\"操作成功！\");\n    emitter.emit(\"addEditUser\", true);\n  } else {\n    message.error(res.retMsg);\n  }\n\n\n}\n// const addEditFrom = props.setData.list\n</script>\n\n<style lang=\"less\" scoped>\n</style>\n================\n================\nFile: src/views/platform-manage/deptManage copy.vue\n================\n================\n<!-- 机构管理 -->\n<template>\n  <div ref=\"pageContent\" class=\"dept-content page-content\">\n    <a-table \n    :columns=\"columns\" \n    :data-source=\"dataSource\" \n    borderedsize=\"small\"\n    :pagination=\"false\"\n    bordered\n    :loading=\"pageLoading\"\n    :scroll=\"{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }\"\n    >\n      <template #bodyCell=\"{ column, record }\">\n        <template v-if=\"column.dataIndex === 'county'\">\n          {{ record.county }}\n          <MenuOutlined @click=\"showModaAccount(record)\"/>\n      </template>\n      </template>\n    </a-table>\n  </div>\n  <setMenu v-if=\"setData.modalOpen\" :accountId=\"accountId\" :setData=\"setData\" />\n</template>\n<script setup>\nimport { deptList, menuAllTree, menuAllDetail } from \"@/api/deptManage\";\nimport setMenu from './dept-manage/setMenu.vue';\nimport emitter from '@/assets/js/emitter';\n\nconst dataSource = ref([]);\nconst countyfilters = ref([])\nconst pageLoading = ref(false)\n//定义合并的时期变量\nconst rowCountyArr = ref([]);\nconst roadCityArr = ref([]);\n//合并行\nconst getRowspan = (dataScroce, filed) => {\n  let spanArr = [];\n  let position = 0;\n  dataScroce.forEach((item, index) => {\n    if (index === 0) {\n      spanArr.push(1);\n      position = 0;\n    } else {\n      //需要合并的地方判断\n      if (dataScroce[index][filed] === dataScroce[index - 1][filed]) {\n        spanArr[position] += 1;\n        spanArr.push(0);\n      } else {\n        spanArr.push(1);\n        position = index;\n      }\n    }\n  });\n  return spanArr;\n};\n// 数据处理的方法\nconst toDataSource = (resData) => {\n  let arr = [];\n  resData.map((responseDataItem) => {\n    countyfilters.value.push({\n      text: responseDataItem.label,\n      value: responseDataItem.label\n    })\n    responseDataItem.children.map((item) => {\n      arr = [\n        ...arr,\n        {\n          county: responseDataItem.label,\n          city: item.label,\n          id: responseDataItem.id\n          // channel: it.label,\n        },\n      ];\n      // item.children.map((it) => {\n      //   console.log(it)\n      //   arr = [\n      //     ...arr,\n      //     {\n      //       county: responseDataItem.label,\n      //       city: item.label,\n      //       id: responseDataItem.id,\n      //       channel: it.label,\n      //     },\n      //   ];\n      // });\n    });\n  });\n  dataSource.value = arr;\n  \n  rowCountyArr.value = getRowspan(dataSource.value, \"county\");\n  roadCityArr.value = getRowspan(dataSource.value, \"city\");\n};\n// 获取列表数据\nconst getDeptList = async () => {\n  pageLoading.value = true\n  const res = await deptList();\n  pageLoading.value = false\n  if (res.retCode === \"000000\") {\n    toDataSource(res.data);\n  } else {\n    message.error(res.retMsg);\n  }\n};\n// useMousePosition()\nonMounted(()=>{\n  getDeptList();\n})\nconst columns = [\n  {\n    title: \"省级部门\",\n    dataIndex: \"county\",\n    customCell: (record, rowIndex, column) => {\n      return {\n        rowSpan: rowCountyArr.value[rowIndex],\n        style: {\n          \"text-align\": \"center\", // 单元格文本居中\n          \"vertical-align\": \"middle\", // 单元格内容垂直居中\n        },\n      };\n    },\n    filterMultiple: false,\n    onFilter: (value, record) => record.county.indexOf(value) === 0,\n    filters: countyfilters.value\n  },\n  {\n    title: \"市级部门\",\n    dataIndex: \"city\"\n    // customCell: (record, rowIndex, column) => {\n    //   return {\n    //     rowSpan: roadCityArr.value[rowIndex],\n    //     style: {\n    //       \"text-align\": \"center\", // 单元格文本居中\n    //       \"vertical-align\": \"middle\", // 单元格内容垂直居中\n    //     },\n    //   };\n    // },\n  }\n  // ,\n  // {\n  //   title: \"区级部门\",\n  //   dataIndex: \"channel\",\n  // }\n];\n\n//分配账号\nconst setData = reactive({\n  modalOpen: false,\n  id: null,\n  deptListDatas: []\n})\n\nconst showModaAccount = async (item) => {\n  pageLoading.value = true;\n  const res = await menuAllTree();\n  if (res.retCode === \"000000\") {\n    setData.id = item.id;\n    // let treeData = res.data\n    // treeData.forEach((el,i) => {\n    //   treeData[i].id = (i+1) * 100000\n    // });\n    Object.assign(setData.deptListDatas,res.data)\n    getMenuAllDetail(item.id)\n  } else {\n    message.error(res.retMsg);\n  }\n};\n//获取省份关联菜单详情\nconst accountId = ref([])\nconst getMenuAllDetail = async (deptId) => {\n  const res = await menuAllDetail({deptId});\n  if (res.retCode === \"000000\") {\n    pageLoading.value = false;\n    accountId.value = res.data || [];\n    setData.modalOpen = true;\n  } else {\n    message.error(res.retMsg);\n  }\n}\n//设置菜单后更新数据\nemitter.on('setMenu',()=>{\n  setData.modalOpen = false;\n}) \n//组件卸载后销毁emitter\nonUnmounted(()=>{\n  emitter.off('setMenu')\n})\n</script>\n<style lang=\"less\" scoped>\n.dept-content {\n  background: #fff;\n  padding: 10px;\n  height: calc(100vh - 120px);\n  .anticon-menu {\n    font-size: 12px;\n    color: #FE8308;\n  }\n}\n</style>\n================\n================\nFile: src/views/workbench/con-opr-work/uploadImg.vue\n================\n================\n<!-- 审核图片上传 -->\n<template>\n  <a-modal\n    :maskClosable=\"false\"\n    v-model:open=\"props.uploadImgData.modalOpen\"\n    :title=\"props.uploadImgData.title\"\n    okText=\"确定\"\n    @ok=\"addGivingHandle\"\n    :confirm-loading=\"modalLoading\"\n    width=\"320px\" \n  >\n    <a-spin :spinning=\"confirmLoading\">\n      <div class=\"upload-box\">\n        <a-upload\n          v-model:file-list=\"fileList\"\n          list-type=\"picture-card\"\n          @preview=\"handlePreview\"\n          @change=\"successUpload\"\n          :before-upload=\"beforeUpload\"\n        >\n          <div v-if=\"fileList.length < 1\">\n            <plus-outlined />\n            <div style=\"margin-top: 8px\">上传图片</div>\n          </div>\n        </a-upload>\n        <p class=\"upload-desc\">请上传jpg/png，文件大小不能超过5MB；每个留痕最多可上传三次。</p>\n      </div>\n    </a-spin>\n  </a-modal>\n  <a-modal\n    :open=\"previewVisible\"\n    :title=\"previewTitle\"\n    :footer=\"null\"\n    @cancel=\"handleCancel\"\n  >\n    <img alt=\"example\" style=\"width: 100%\" :src=\"previewImage\" />\n  </a-modal>\n</template>\n\n<script setup>\nimport { message } from \"ant-design-vue\";\nimport https from \"@/assets/js/request\";\nimport emitter from \"@/assets/js/emitter\";\nconst props = defineProps({\n  uploadImgData: Object,\n});\nconst confirmLoading = ref(false);\nconst modalLoading = ref(false);\n\nfunction getBase64(file) {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result);\n    reader.onerror = (error) => reject(error);\n  });\n}\nconst previewVisible = ref(false);\nconst previewImage = ref(\"\");\nconst previewTitle = ref(\"\");\nconst fileList = ref([])\nwatch(props,(newValue,oldValue)=>{\n  if(newValue.uploadImgData.title === '初审' && newValue.uploadImgData.item.firstAuditUrl) {\n    fileList.value.push({\n      uid: '-1',\n      name: newValue.uploadImgData.item.firstAuditName,\n      status: 'done',\n      url: newValue.uploadImgData.item.firstAuditUrl\n    })\n  } else if(newValue.uploadImgData.title === '二审' && newValue.uploadImgData.item.secondAuditUrl) {\n    fileList.value.push({\n      uid: '-1',\n      name: newValue.uploadImgData.item.secondAuditName,\n      status: 'done',\n      url: newValue.uploadImgData.item.secondAuditUrl\n    })\n  } else if(newValue.uploadImgData.title === '终审' && newValue.uploadImgData.item.finalAuditUrl) {\n    fileList.value.push({\n      uid: '-1',\n      name: newValue.uploadImgData.item.finalAuditName,\n      status: 'done',\n      url: newValue.uploadImgData.item.finalAuditUrl\n    })\n  }\n},{immediate:true})\nconst handleCancel = () => {\n  previewVisible.value = false;\n  previewTitle.value = \"\";\n};\n//预览\nconst handlePreview = async (file) => {\n  if (!file.url && !file.preview) {\n    file.preview = await getBase64(file.originFileObj);\n  }\n  previewImage.value = file.url || file.preview;\n  previewVisible.value = true;\n  previewTitle.value =\n    file.name || file.url.substring(file.url.lastIndexOf(\"/\") + 1);\n};\n// 上传前判断格式\nconst beforeUpload = (file) => {\n  confirmLoading.value = true;\n  const isJpgOrPng = file.type === \"image/jpeg\" || file.type === \"image/png\";\n  if (!isJpgOrPng) {\n    message.error(\"上传格式有误！\");\n    return false;\n  }\n  const isLt2M = file.size / 1024 / 1024 < 5;\n  if (!isLt2M) {\n    message.error(\"图片大小不能超过5M！\");\n    return false;\n  }\n  fileList.value = [...(fileList.value || []), file];\n  return false;\n};\n// 本地上传成功\nlet uploadImg = {};\nconst successUpload = (file) => {\n  uploadImg = file.file;\n  confirmLoading.value = false;\n};\n// 上传图片\nconst addGivingHandle = () => {\n  if(JSON.stringify(uploadImg) === '{}') {\n    message.error(\"请上传图片文件\");\n    return false\n  }\n  const formData = new FormData();\n  formData.append(\"scheduleId\", props.uploadImgData.item.id);\n  formData.append(\"file\", uploadImg);\n  const config = {\n    headers: { \"Content-Type\": \"multipart/form-data; charset=utf-8\" },\n  };\n  const instance = https.axios.create({ withCredentials: true });\n  modalLoading.value = true;\n  confirmLoading.value = true;\n  instance\n    .post(`${props.uploadImgData.item.uploadUrl}?replayTime=` + new Date().getTime(), formData, config)\n    .then((xhr) => {\n      modalLoading.value = false;\n      confirmLoading.value = false;\n      if (xhr.data.retCode === \"000000\") {\n        message.success(\"操作成功\");\n        emitter.emit(\"refreshWork\", true);\n      } else if (xhr.data.retCode === \"000520\") {\n        message.error(\"请上传图片文件\");\n      } else if (xhr.data.retCode === \"990011\") {\n        message.error(\"初审图片只能上传3次\");\n      } else {\n        message.error(xhr.data.retMsg);\n      }\n    });\n};\n</script>\n<style lang=\"less\" scoped>\n.upload-box {\n  :deep(.ant-upload-list) {\n    display: flex;\n    justify-content: center;\n  }\n  .ant-upload-select-picture-card i {\n    font-size: 32px;\n    color: #999;\n  }\n\n  .ant-upload-select-picture-card .ant-upload-text {\n    margin-top: 8px;\n    color: #666;\n  }\n  .upload-desc {  \n    font-size: 12px;\n    color: #A8AEB8;\n  }\n}\n</style>\n================\n================\nFile: src/views/workbench/operate-manage/content-tag/tableTag.vue\n================\n================\n<!-- 业务标签 -->\n<template>\n  <div class=\"biz-tag\">\n    <a-button type=\"primary\" @click=\"showModal()\" class=\"add-btn\">\n      <template #icon>\n        <PlusOutlined />\n      </template>\n      新增标签\n    </a-button>\n    <div class=\"tag-list\">\n      <a-table\n        :dataSource=\"dataSource\"\n        :columns=\"tableColumns\"\n        childrenColumnName=\"list\"\n        size=\"small\"\n        :pagination=\"pagination\"\n        @change=\"handleTableChange\"\n        :loading=\"pageLoading\"\n        :scroll=\"{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }\"\n      >\n        <template #bodyCell=\"{ column, record, index }\">\n          <template v-if=\"column.dataIndex === 'broadCategory'\">\n            {{ record.label }}\n          </template>\n          <template v-if=\"column.dataIndex === 'subclass'\">\n            <a-space>\n              <a-tag\n                :bordered=\"false\"\n                v-for=\"item in record.children\"\n                :key=\"item\"\n                :color=\"acquiesceColor[index]\"\n              >\n                {{ item.label }}\n              </a-tag>\n            </a-space>\n          </template>\n          <template v-if=\"column.dataIndex === 'operate'\">\n            <div class=\"operate-box\">\n              <span class=\"edit-btn\" @click=\"showModal(record)\">编辑</span>\n              <span class=\"del-btn\" @click=\"delFun(record)\">删除</span>\n            </div>\n          </template>\n        </template>\n      </a-table>\n    </div>\n    <addEdit\n      v-if=\"addEditData.modalOpen\"\n      :addEditData=\"addEditData\"\n      @addEditTag=\"callbackDddEditTag\"\n    />\n  </div>\n</template>\n\n<script setup>\nimport useGetList from \"@/hooks/useGetList\";\nimport addEdit from \"./addEdit.vue\";\nimport useDel from \"@/hooks/useDel\";\nimport { tagDel } from \"@/api/contentTag\";\nimport { message } from \"ant-design-vue\";\nconst props = defineProps({\n  tagData: Object,\n});\nconst acquiesceColor = ref([\n  \"red\",\n  \"volcano\",\n  \"orange\",\n  \"gold\",\n  \"lime\",\n  \"green\",\n  \"cyan\",\n  \"blue\",\n  \"geekblue\",\n  \"purple\",\n]);\nconst pageLoading = ref(false);\nlet dataSource = ref([]);\nconst tableColumns = reactive([\n  {\n    title: \"一类\",\n    dataIndex: \"broadCategory\",\n  },\n  {\n    title: \"二类\",\n    dataIndex: \"subclass\",\n  },\n  {\n    title: \"操作\",\n    dataIndex: \"operate\",\n  },\n]);\n// 分页/筛选\nconst pagination = reactive({\n  total: 0,\n  current: 0,\n  pageSize: 0,\n});\n// 获取列表数据\nonMounted(() => {\n  useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination);\n});\nconst handleTableChange = (pag, filters, sorter) => {\n  useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination, {\n    pageNum: pag.current,\n    pageSize: pag.pageSize,\n  });\n};\n//新增/编辑数据\nconst addEditData = reactive({\n  modalOpen: false,\n  modalTitle: props.tagData.addText,\n  addTag: props.tagData.addTag,\n  list: {\n    name: \"\",\n    children: [],\n  },\n});\nconst showModal = (item = { name: \"\", children: [], id: null }) => {\n  Object.assign(addEditData.list, item);\n  if (item.id) {\n    addEditData.modalTitle = props.tagData.editText;\n  } else {\n    addEditData.modalTitle = props.tagData.addText;\n    addEditData.list.label = \"\";\n  }\n  addEditData.modalOpen = true;\n};\n\nconst callbackDddEditTag = () => {\n  addEditData.modalOpen = false;\n  useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination);\n};\n// 删除数据\nconst delFun = async (list) => {\n  useDel(tagDel, { firstTagId: list.id }, pageLoading, (res) => {\n    if (res.retCode === \"000000\") {\n      useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination);\n      message.success(\"操作成功！\");\n    } else {\n      message.error(res.retMsg);\n    }\n  });\n};\n</script>\n\n<style lang=\"less\" scoped>\n.biz-tag {\n  background: #fff;\n  padding: 10px;\n  border: 1px solid #E2E8F0;\n  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);\n  .add-btn {\n    position: absolute;\n    right: 0;\n    top: -54px;\n  }\n}\n.tag-list {\n  background: #fff;\n  height: calc(100vh - 200px);\n  .operate-box {\n    span {\n      margin-right: 10px;\n      cursor: pointer;\n    }\n    .edit-btn {\n      color: #FE8308;\n    }\n    .del-btn {\n      color: #fd7271;\n    }\n  }\n}\n</style>\n================\n================\nFile: src/views/workbench/operate-manage/operation-node/setAccount.vue\n================\n================\n<!-- 账号授权 -->\n<template>\n  <a-modal\n    :maskClosable=\"false\"\n    v-model:open=\"props.setData.modalOpen\"\n    title=\"适用账号\"\n    okText=\"确定\"\n    :afterClose=\"resetFields\"\n    :confirm-loading=\"modalLoading\"\n    @ok=\"setAccSubmit\"\n  >\n    <a-form>\n      <a-form-item label=\"适用账号\" v-bind=\"validateInfos.accountId\">\n        <a-select\n          mode=\"multiple\"\n          v-model:value=\"addEditFrom.accountId\"\n          :options=\"props.setData.deptListDatas\"\n          :field-names=\"{ label: 'name', value: 'id' }\"\n          :max-tag-count=\"1\"\n          placeholder=\"请选择运营账号\"\n          :filter-option=\"filterOption\"\n          allow-clear\n        >\n        </a-select>\n      </a-form-item>\n    </a-form>\n  </a-modal>\n</template> \n\n<script setup>\nimport { message, Form } from \"ant-design-vue\";\nimport { relSave } from \"@/api/operationNode\";\nimport emitter from \"@/assets/js/emitter\";\nconst props = defineProps({\n  setData: Object,\n});\n//账号树形控件\nconst addEditFrom = reactive({\n  accountId: []\n});\nconst filterOption = (input, option) => {\n  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;\n};\naddEditFrom.accountId = props.setData.accountId;\n//表单规则验证数据\nconst addEditRules = reactive({\n  accountId: [\n    {\n      required: true,\n      trigger: \"change\",\n      message: \"请选择运营账号\",\n    },\n  ],\n});\n\nconst useForm = Form.useForm;\nconst { resetFields, validate, validateInfos } = useForm(\n  addEditFrom,\n  addEditRules\n);\n//提交数据\nconst modalLoading = ref(false);\nconst setAccSubmit = async () => {\n  validate()\n    .then(async () => {\n      const { accountId } = toRaw(addEditFrom);\n      console.log(addEditFrom, accountId);\n      const fromData = {\n        nodeId: props.setData.nodeId,\n        accountId: accountId.join(\",\"),\n      };\n      const res = await relSave(fromData);\n      modalLoading.value = false;\n      if (res.retCode === \"000000\") {\n        message.success(\"操作成功！\");\n        emitter.emit(\"addEditComplete\", true);\n      } else {\n        message.error(res.retMsg);\n      }\n    })\n    .catch((err) => {\n      console.log(\"error\", err);\n    });\n};\n</script>\n================\n================\nFile: src/views/workbench/operate-manage/contentTag.vue\n================\n================\n<!-- 内容标签 -->\n<template>\n  <div class=\"content-tag\">\n    <a-tabs v-model:activeKey=\"activeKey\" \n    :destroyInactiveTabPane=\"isTabPane\">\n      <a-tab-pane key=\"1\" tab=\"业务类标签\">\n        <bizTag :tagData=\"initBizTag\" />\n      </a-tab-pane>\n      <a-tab-pane key=\"2\" tab=\"综合类标签\">\n        <bizTag :tagData=\"initSynthesisTag\" />\n      </a-tab-pane>\n    </a-tabs>\n  </div>\n</template>\n<script setup>\nimport { bizAdd, bizPage, synthesisAdd, synthesisPage } from \"@/api/contentTag\";\nimport bizTag from \"./content-tag/tableTag.vue\";\n//业务标签数据\nconst initBizTag = {\n  tagPage: bizPage, //tabl的请求url\n  addTag: bizAdd, //新增的请求url\n  addText: \"新增业务类标签\",\n  editText: \"编辑业务类标签\",\n};\n//综合类标签数据\nconst initSynthesisTag = {\n  tagPage: synthesisPage, //tabl的请求url\n  addTag: synthesisAdd, //新增的请求url\n  addText: \"新增综合类标签\",\n  editText: \"编辑综合类标签\",\n};\nconst activeKey = ref(\"1\");\nconst isTabPane = ref(true);\n</script>\n<style lang=\"less\" scoped>\n::deep(.ant-tabs) {\n  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {\n    font-size: 14px;\n    color: #011738;\n  }\n}\n</style>\n================\n================\nFile: src/views/workbench/wechat-manage/material-template/template/image.vue\n================\n================\n<!-- 图片 -->\n<template>\n  <a-list \n  ref=\"listItemRef\" \n  size=\"small\" \n  :grid=\"{ gutter: 6, column: 4 }\"\n  bordered \n  class=\"image-list\"\n  :data-source=\"materialData\">\n    <template #renderItem=\"{ item }\">\n      <a-list-item @click=\"selectMaterial(item)\">\n            <a-image\n                class=\"image-el\"\n                :src=\"item.localUrl\"\n                fallback=\"data:image/png;base64,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\"\n            />\n            <p class=\"list-text\">{{ item.templateName }}</p>\n      </a-list-item>\n    </template>\n  </a-list>\n</template>\n\n<script setup>\nimport emitter from \"@/assets/js/emitter\";\nimport \"@/hooks/useLoadMore\";\n\nconst props = defineProps({\n    materialData: Object\n});\nconsole.log(props.materialData)\nconst listItemRef = ref();\n \n// 添加scroll监听\nonMounted(()=>{\n    nextTick(()=>{\n        emitter.emit(\"onMounted\", listItemRef.value);\n    })\n})\n// 移除scroll监听\nonUnmounted(()=>{\n    emitter.off(\"onBeforeUnmount\");\n})\n//选择的素材\nconst selectMaterial = (item) => {\n    item.typeName = '图片';\n    item.templateType = 'image';\n    emitter.emit(\"selectArray\", item);\n    selectItem.checkboxs = data;\n}\n</script>\n\n<style lang=\"less\" scoped>\n.ant-list-item:hover {\n    cursor: pointer;\n    background-color: #f5f7fa;\n}\n:deep(.ant-spin-container) {\n    max-height: 360px;\n    overflow-y: scroll;\n    overflow-x: hidden;\n}\n.image-list {\n    :deep(.ant-list-item) {\n        padding: 0;\n        box-sizing: border-box;\n        border: 1px solid #fff;\n    }\n    :deep(.ant-row) {\n        padding: 5px;\n    }\n    :deep(.ant-image) {\n        width: 100%;\n        height: 170px;\n    }\n    :deep(.list-text) {\n        overflow: hidden;\n        text-overflow: ellipsis;\n        display: -webkit-box;\n        -webkit-line-clamp: 2;\n        -webkit-box-orient: vertical;\n        word-wrap: break-word;\n        word-break: break-all;\n    }\n    :deep(.ant-list-item:hover) {\n        cursor: pointer;\n        border: 1px solid #FE8308;\n        -webkit-box-shadow: 0 0 5px #FE8308;\n        box-shadow: 0 0 5px #FE8308;\n    }\n}\n</style>\n================\n================\nFile: src/views/workbench/wechat-manage/material-template/template/news.vue\n================\n================\n<!-- 图文消息 -->\n<template>\n  <a-list ref=\"listItemRef\" size=\"small\" bordered :data-source=\"materialData\">\n    <template #renderItem=\"{ item }\">\n      <a-list-item @click=\"selectMaterial(item)\">{{\n        item.templateName\n      }}</a-list-item>\n    </template>\n  </a-list>\n</template>\n  <script setup>\nimport emitter from \"@/assets/js/emitter\";\nimport \"@/hooks/useLoadMore\";\n\nconst props = defineProps({\n  materialData: Object,\n});\nconst listItemRef = ref();\n\n// 添加scroll监听\nonMounted(() => {\n  nextTick(() => {\n    emitter.emit(\"onMounted\", listItemRef.value);\n  });\n});\n// 移除scroll监听\nonUnmounted(() => {\n  emitter.off(\"onBeforeUnmount\");\n});\n//选择的素材\nconst selectMaterial = (item) => {\n  item.typeName = \"图文\";\n  item.templateType = \"news\";\n  emitter.emit(\"selectArray\", item);\n};\n</script>\n<style lang=\"less\" scoped>\n.ant-list-item:hover {\n  cursor: pointer;\n  background-color: #f5f7fa;\n}\n:deep(.ant-spin-container) {\n  max-height: 360px;\n  overflow-y: scroll;\n}\n</style>\n================\n================\nFile: src/views/workbench/wechat-manage/material-template/template/newsitem.vue\n================\n================\n<!-- 单图文 -->\n<template>\n  <a-checkbox-group v-model:value=\"checkboxs\" @change=\"setNum\">\n    <a-list ref=\"listItemRef\" size=\"small\" bordered :data-source=\"materialData\">\n      <template #renderItem=\"{ item }\">\n        <a-list-item>\n          <a-checkbox :value=\"item.id\">\n            <span class=\"serial-number\">{{\n              item?.num >= 1 ? item?.num : \"\"\n            }}</span>\n            {{ item.title }}\n          </a-checkbox>\n        </a-list-item>\n      </template>\n    </a-list>\n  </a-checkbox-group>\n</template>\n  <script setup>\nimport emitter from \"@/assets/js/emitter\";\nimport \"@/hooks/useLoadMore\";\n\nconst props = defineProps({\n  materialData: Object,\n});\nconst listItemRef = ref();\nconst checkboxs = ref([]);\n// 添加scroll监听\nonMounted(() => {\n  nextTick(() => {\n    emitter.emit(\"onMounted\", listItemRef.value);\n  });\n});\nconst setNum = (data) => {\n  // 取消选中清除序号\n  props.materialData.forEach((i) => {\n    i.num = 0;\n  });\n  let selectItem = {}\n  data.forEach((item, index) => {\n    props.materialData.forEach((i) => {\n      if (item == i.id) {\n        i.num = index + 1;\n        i.templateName = i.title;\n        selectItem = i\n      }\n    });\n  });\n  selectItem.checkboxs = data;\n  selectItem.typeName = \"单图文\";\n  selectItem.templateType = 'newsitem';\n  emitter.emit(\"selectPost\", selectItem);\n};\nemitter.on(\"resetCheckboxs\", () => {\n  checkboxs.value = [];\n  props.materialData.forEach((i) => {\n    i.num = 0;\n  });\n});\n// 移除scroll监听\nonUnmounted(() => {\n  emitter.off(\"onBeforeUnmount\");\n});\n</script>\n  <style lang=\"less\" scoped>\n.ant-checkbox-group,\n.ant-list {\n  width: 100%;\n}\n:deep(.ant-spin-container) {\n  max-height: 360px;\n  overflow-y: scroll;\n}\n:deep(.ant-list-items) {\n  display: flex;\n  flex-direction: column;\n}\n.ant-list-bordered.ant-list-sm :deep(.ant-list-item) {\n  padding: 5px 15px;\n}\n.ant-checkbox-wrapper {\n  position: relative;\n}\n.serial-number {\n  position: absolute;\n  left: -12px;\n  top: 3px;\n  font-size: 12px;\n  color: #FE8308;\n}\n</style>\n================\n================\nFile: src/views/workbench/wechat-manage/material-template/template/post.vue\n================\n================\n<!-- 文章 -->\n<template>\n    <a-checkbox-group v-model:value=\"checkboxs\" @change=\"setNum\">\n        <a-list ref=\"listItemRef\" size=\"small\" bordered :data-source=\"materialData\">\n            <template #renderItem=\"{ item }\">\n                <a-list-item>\n                    <a-checkbox :value=\"item.id\">\n                        <span class=\"serial-number\">{{ item?.num>=1?item?.num:\"\" }}</span>\n                        <div class=\"post-list\">\n                            <a-image\n                            class=\"image-el\"\n                            :src=\"item.headerImage\"\n                            fallback=\"data:image/png;base64,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\"\n                            />\n                            {{ item.title }}\n                        </div>\n                    </a-checkbox>\n                </a-list-item>\n            </template>\n        </a-list>\n</a-checkbox-group>\n</template>\n  <script setup>\nimport emitter from \"@/assets/js/emitter\";\nimport \"@/hooks/useLoadMore\";\n\nconst props = defineProps({\n  materialData: Object,\n});\nconst listItemRef = ref();\nconst checkboxs = ref([])\n// 添加scroll监听\nonMounted(() => {\n  nextTick(() => {\n    emitter.emit(\"onMounted\", listItemRef.value);\n  });\n});\nconst setNum = (data) => {\n    // 取消选中清除序号\n    props.materialData.forEach(i => {\n        i.num = 0\n    })\n    data.forEach((item, index) => {\n        props.materialData.forEach(i => {\n            if (item == i.id) {\n                i.num = index + 1\n                i.typeName = '文章';\n                i.templateType = 'post';\n                i.templateName = i.title;\n                i.checkboxs = data;\n                emitter.emit(\"selectPost\", i);\n            }\n        })\n    })\n}\n//重置\nemitter.on(\"resetCheckboxs\", () => {\n    checkboxs.value = []\n    props.materialData.forEach(i => {\n        i.num = 0\n    })\n});\n// 移除scroll监听\nonUnmounted(() => {\n    emitter.off(\"onBeforeUnmount\");\n});\n</script>\n<style lang=\"less\" scoped>\n.ant-checkbox-group,.ant-list {\n    width: 100%;\n}\n:deep(.ant-spin-container) {\n  max-height: 360px;\n  overflow-y: scroll;\n}\n:deep(.ant-list-items) {\n    display: flex;\n    flex-direction: column;\n}\n.ant-list-bordered.ant-list-sm :deep(.ant-list-item) {\n    padding: 5px 10px;\n}\n.ant-checkbox-wrapper {\n    position: relative;\n}\n.serial-number {\n    position: absolute;\n    left: 5px;\n    top: 50px;\n    font-size: 12px;\n    color: #FE8308;\n}\n.post-list {\n    :deep(.ant-image) {\n        width: 80px;\n    }\n}\n</style>\n================\n================\nFile: src/views/workbench/wechat-manage/material-template/template/text.vue\n================\n================\n<!-- 文本 -->\n<template>\n  <a-list ref=\"listItemRef\" size=\"small\" bordered :data-source=\"materialData\">\n    <template #renderItem=\"{ item }\">\n      <a-list-item @click=\"selectMaterial(item)\">{{ item.templateName }}</a-list-item>\n    </template>\n  </a-list>\n</template>\n<script setup>\nimport emitter from \"@/assets/js/emitter\";\nimport \"@/hooks/useLoadMore\";\nconst props = defineProps({\n    materialData: Object\n});\nconst listItemRef = ref();\n// 添加scroll监听\nonMounted(()=>{\n    nextTick(()=>{\n        emitter.emit(\"onMounted\", listItemRef.value);\n    })\n})\n// 移除scroll监听\nonUnmounted(()=>{\n    emitter.off(\"onBeforeUnmount\");\n})\n//选择的素材\nconst selectMaterial = (item) => {\n    item.typeName = '文本';\n    item.templateType = 'text';\n    emitter.emit(\"selectArray\", item);\n}\n</script>\n<style lang=\"less\" scoped>\n.ant-list-item:hover {\n    cursor: pointer;\n    background-color: #f5f7fa;\n}\n:deep(.ant-spin-container) {\n    max-height: 360px;\n    overflow-y: scroll;\n}\n</style>\n================\n================\nFile: src/views/workbench/wechat-manage/material-template/template/video.vue\n================\n================\n<!-- 视频 -->\n<!-- 图片 -->\n<template>\n  <a-list\n    ref=\"listItemRef\"\n    size=\"small\"\n    :grid=\"{ gutter: 6, column: 4 }\"\n    bordered\n    class=\"image-list\"\n    :data-source=\"materialData\"\n  >\n    <template #renderItem=\"{ item }\">\n      <a-list-item @click=\"selectMaterial(item)\">\n        <a-image\n          class=\"image-el\"\n          :src=\"item.localUrl\"\n          fallback=\"data:image/png;base64,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\"\n        />\n        <p class=\"list-text\">{{ item.templateName }}</p>\n      </a-list-item>\n    </template>\n  </a-list>\n</template>\n  \n  <script setup>\nimport emitter from \"@/assets/js/emitter\";\nimport \"@/hooks/useLoadMore\";\n\nconst props = defineProps({\n  materialData: Object,\n});\nconst listItemRef = ref();\n\n// 添加scroll监听\nonMounted(() => {\n  nextTick(() => {\n    emitter.emit(\"onMounted\", listItemRef.value);\n  });\n});\n// 移除scroll监听\nonUnmounted(() => {\n    emitter.off(\"onBeforeUnmount\");\n});\n//选择的素材\nconst selectMaterial = (item) => {\n    item.typeName = '视频';\n    item.templateType = 'video';\n    emitter.emit(\"selectArray\", item);\n}\n</script>\n  \n<style lang=\"less\" scoped>\n:deep(.ant-list-item:hover) {\n    cursor: pointer;\n    border: 1px solid #FE8308;\n    -webkit-box-shadow: 0 0 5px #FE8308;\n    box-shadow: 0 0 5px #FE8308;\n}\n:deep(.ant-spin-container) {\n  max-height: 360px;\n  overflow-y: scroll;\n  overflow-x: hidden;\n}\n.image-list {\n  :deep(.ant-list-item) {\n    padding: 0;\n    box-sizing: border-box;\n    border: 1px solid transparent;\n  }\n  :deep(.ant-row) {\n    padding: 5px;\n  }\n  :deep(.image-el) {\n    width: 100%;\n    height: 170px;\n  }\n  :deep(.list-text) {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    word-wrap: break-word;\n    word-break: break-all;\n  }\n}\n</style>\n\n", "hash": "9a22eebb0a4490306e75d403ee37c1864d03667bf331b562e2d21835d876df71", "timestamp": 1751877653922, "expireAt": 1751964053922, "version": "1.0.0"}