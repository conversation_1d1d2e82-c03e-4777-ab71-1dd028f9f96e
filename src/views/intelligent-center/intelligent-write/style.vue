<!-- 润色 -->
<template>
  <div class="write-tab-content">
    <div class="write-form">
      <a-form>
        <a-form-item label="润色原文" v-bind="validateInfos.original">
          <a-textarea
            v-model:value="writeForm.original"
            placeholder="请输入润色原文"
            allow-clear
            show-count
            :maxlength="5000"
            :auto-size="{ minRows: 8, maxRows: 10 }"
          />
        </a-form-item>
        <a-form-item label="润色要求" v-bind="validateInfos.require">
          <a-textarea
            v-model:value="writeForm.require"
            placeholder="请描述您的润色要求，如信息更新、字数等"
            allow-clear
            show-count
            :maxlength="200"
            :auto-size="{ minRows: 4, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
      <p class="history-btn">
        <span @click="emit('emitHistory')"  v-if="props.historyData.isEmpty">查看历史文章</span>
      </p>
      <div class="button-box">
        <a-button
          :disabled="writeForm.require === ''?true:false || writeForm.original ===  ''?true:false || formDisabled"
          type="primary"
          :class="{'write-loading':reasoningMarkdown}"
          @click="addEditSubmit"
          >{{ submitText }}</a-button
        >
      </div>
    </div>
    <div class="write-result">
      <div class="write-banner" v-if="writeEmpty">
        <p class="write-banner-title">文章润色</p>
        <p class="write-banner-desc">填写左侧设置项，即可生成文案</p>
        <img src="../../../assets/image/write-banner.png" alt="">
      </div>
      <div class="result-box" ref="scrollContainer" v-else>
        <p class="result-reasoning" v-html="reasoningMarkdown"></p>
        <div class="result-content" v-html="compiledMarkdown"></div>
      </div>
      <div class="copy-box" v-show="compiledMarkdown">
        <span>内容由AI生成，请注意甄别真实性</span>
        <span class="copy-btn" @click="copyWrite"><CopyFilled />复制全文</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { message, Form } from "ant-design-vue";
import { marked } from "marked";
import emitter from "@/assets/js/emitter";
import { fetchEventSource } from '@microsoft/fetch-event-source';
const props = defineProps({
  historyData: Object
});
//查看历史创建文章
const emit = defineEmits(["emitHistory"]);
let submitText = ref("文案生成");
//表单规则验证数据
const writeForm = reactive({
  original: "",
  require: "",
});
const addEditRules = reactive({
  original: [
    {
      required: true,
      message: "请输入你润色原文",
    },
    {
      min: 1,
      max: 5000,
      message: "长度在1 到 5000个字符",
      trigger: "change",
    },
  ],
  require: [
    {
      required: true,
      message: "请输入您的润色要求",
    },
    {
      min: 1,
      max: 200,
      message: "长度在1 到 200个字符",
      trigger: "change",
    },
  ],
});
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  writeForm,
  addEditRules
);
const formDisabled = ref(false);
const writeEmpty = ref(true);
watch(()=>writeForm.require,(newValue,oldValue)=>{
  if(!newValue) {
    formDisabled.value = false;
  }
})
watch(()=>writeForm.original,(newValue,oldValue)=>{
  if(!newValue) {
    formDisabled.value = false;
  }
})
const writeData = reactive({
  reasoning: "",
  content: "",
});
//点击历史记录回显数据
emitter.on("historyTag", (value) => {
  writeData.content = value.content;
  writeData.reasoning = "";
  Object.assign(writeForm, value.userParam);
  writeEmpty.value = false;
  if (value.userParam === 2000) {
    writeForm.number = null;
  }
});
// 使用marked将Markdown转换为HTML
const compiledMarkdown = computed(() => {
  return marked(writeData.content);
});
// 使用marked将Markdown转换为HTML
const reasoningMarkdown = computed(() => {
  return marked(writeData.reasoning);
});
function copyWrite() {
  navigator.clipboard
  .writeText(JSON.stringify(compiledMarkdown.value))
  .then(() => {
    message.success("已复制到剪贴板");
  })
  .catch((error) => {
    message.success("系统繁忙，请稍后再试！");
  });
}
const scrollContainer = ref(null);
//生成数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      writeData.reasoning = '';
      writeData.content = '';
      const fromData = {...toRaw(writeForm)};
      let sseStatus = '';
      let params = {
        writeFlag: '4',
        prompt: `帮我润色以下文本：${fromData.original}，润色要求是：${fromData.require}。`,
        userParam: JSON.stringify(fromData).toString().replace(new RegExp("\\\\\"","gm"),"\"")
      }
      fetchEventSource(`/cop/ai/write/ali/dp/r?replayTime=${new Date().getTime()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        },
        openWhenHidden: true,
        body: `writeFlag=${params.writeFlag}&prompt=${params.prompt}&userParam=${encodeURIComponent(params.userParam)}`,
        onmessage(ev) {
          writeEmpty.value = false;
          let writeContent = JSON.parse(ev.data);
            // 这里可以根据接收到的流式数据更新前端界面
          if(writeContent.content === 'reasoning') {//推理状态
            submitText.value = '文案生成中...';
            sseStatus = writeContent.content;
            writeForm.require = '';
            formDisabled.value = true;
          }else if (writeContent.content === 'content') {//内容状态
            sseStatus = writeContent.content;
          }else if (writeContent.content === 'COPSSEEND'){//关闭
            submitText.value = '文案生成';
            formDisabled.value = false;
          }else if(writeContent.content === 'COP_SSE_DATA_INSPECTION_FAILED') {
            submitText.value = '文案生成';
            writeData.reasoning = '您的创作要求含有敏感词汇，请修改后再重新生成';
            formDisabled.value = false;
          }else if(writeContent.content === 'COPSSEERROR') {
            writeData.reasoning = '系统繁忙，请稍后再试';
            writeData.content = '';
            formDisabled.value = false;
          }
          if (sseStatus === 'reasoning' && !writeContent.content.includes('reasoning')) {
            writeData.reasoning += writeContent.content;
          }else if(sseStatus === 'content' && !writeContent.content.includes('content') && !writeContent.content.includes('COPSSEEND')) {
            writeData.content += writeContent.content;
          }
          //滚动条置底
          if(scrollContainer.value && scrollContainer.value.scrollHeight > scrollContainer.value.getBoundingClientRect().height) {
            scrollContainer.value.scrollTo({
              top: scrollContainer.value.scrollHeight,
              behavior: "smooth"
            })
          }
        },
        onerror(err) {
          writeData.reasoning = '系统繁忙，请稍后再试';
          writeData.content = '';
          writeEmpty.value = false;
          throw err;
        },
      });
    })
};
</script>

<style lang="less" scoped>
.write-tab-content {
  height: calc(100vh - 170px);
  display: flex;
  overflow-y: auto;
  .write-form {
    width: 465px;
    padding: 20px;
    border-right: 1px solid #e2e8f0;
    .ant-form-item {
      margin-bottom: 20px;
    }
  }
  .ant-form {
    margin-bottom: 20px;
    border: 1px solid transparent;
  }
  .write-result {
    flex: 1;
    padding: 30px;
  }
  .write-banner {
    display: flex;
    height: 100%;
    min-width: 600px;
    max-width: 1200px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img{
      width: 367px;
    }
  }
  .write-banner-title {
    font-size: 18px;
    font-weight: bold;
    color: #1E293B;
  }
  .write-banner-desc {
    font-size: 14px;
    color: #666;
  }
  .result-box {
    height: 100%;
    min-width: 640px;
    max-width: 1200px;
    margin: 0 auto;
    text-align: justify;
    overflow-y: auto;
  }
  .copy-box {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(0, 0, 0, .3);
    .anticon-copy {
      margin-right: 3px;
    }
  }
  .copy-btn {
    cursor: pointer;
    font-size: 14px;
    color: rgba(0, 0, 0, .5);
  }
  .result-reasoning {
    color: #8b8b8b;
  }
  .result-content {
    color: #011738;
  }
  :deep(.ant-form-item-label) {
    width: 80px;
  }
  .platform-form {
    :deep(.ant-form-item-control) {
      flex: 1;
    }
    .ant-radio-button-wrapper {
      width: 90px;
      padding: 0;
      border-inline-start-width: 1px;
      border-radius: 4px;
      font-size: 14px;
      color: #1e293b;
      text-align: center;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .ant-radio-button-wrapper-checked {
      background: #ffa31c;
      border-radius: 4px;
      color: #fff;
    }
    .ant-radio-button-wrapper-checked:not(
        .ant-radio-button-wrapper-disabled
      )::before,
    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;
    }
  }
  .history-btn {
    height: 20px;
    text-align: right;
    font-size: 12px;
    color: #1E293B;
    margin-bottom: 5px;
    span {
      cursor: pointer;
    }
  }
  .button-box {
    position: relative;
    button {
      width: 130px;
      position: absolute;
      right: 0;
      top: 0;
      border: 0;
    }
    :deep(span) {
      margin: 0 auto;
    }
    .ant-btn-primary:disabled {
      border: 0;
      color: #fff;
      background-image: linear-gradient(90deg, #CACACA 0%, #CACACA 100%);
    }
    .write-loading:disabled {
      background-image: linear-gradient(90deg, rgba(254, 115, 9, .7) 0%, rgba(254, 115, 9, .7) 100%);
    }
  }
}
</style>
