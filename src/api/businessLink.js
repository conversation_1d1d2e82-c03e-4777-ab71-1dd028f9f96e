import $https from "@/assets/js/request"

// 列表
export const linkPage = (data) => {
  return $https.fetchGet('/cop/wx/back/business/link/page', data)
}
// 查询业务小类
export const secondList = (data) => {
  return $https.fetchGet('/cop/manage/tag/first/second/list', data)
}
// 查询业务大类
export const bizList = (data) => {
  return $https.fetchGet('/cop/manage/tag/first/biz/list', data)
}
// 导出
export const linkExport = (data) => {
  return $https.fetchGet('/cop/wx/back/business/link/export', data)
}
// 下线
export const linkOffLine = (data, chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/business/link/offLine?chkCurWx=' + chkCurWx, data)
}
// 编辑 
export const linkEdit = (data, chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/business/link/edit?chkCurWx=' + chkCurWx, data)
}
// 新增
export const linkAdd = (data, chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/business/link/add?chkCurWx=' + chkCurWx, data)
}
// 二维码
export const qrcodeGseneric = (data) => {
  return $https.fetchGet('/cop/common/qrcode/generic', data)
}
export const businessLinkExport = '/cop/wx/back/business/link/export'
