<!-- 7日内容总数 - 折线图 -->
<template>
  <div class="line-chart-mom" ref="lineChartMomDom"></div>
</template>
  
  <script setup>
import * as echarts from "echarts/core";
import { GridComponent, MarkPointComponent } from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([
  GridComponent,
  MarkPointComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);
const props = defineProps({
  contentCountSevenDay: Number,
  contentCountSevenDayMom: Number,
});
//数据传值
function shuffleArray(array) {
  //打乱数组
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}
const conCountData = reactive([props.contentCountSevenDay]);
const conCountDataMom = reactive([props.contentCountSevenDayMom]);
for (let i = 1; i <= 6; i++) {
  //制造一些假数据
  conCountData.push(parseInt(Math.random() * props.contentCountSevenDay));
  conCountDataMom.push(parseInt(Math.random() * props.contentCountSevenDayMom));
}
shuffleArray(conCountData);
shuffleArray(conCountDataMom);
// 计算最大值
const lineChartMomDom = ref(null);
const setFunnel = () => {
  let myChart = echarts.init(lineChartMomDom.value);
  myChart.setOption({
    xAxis: {
      type: "category",
      boundaryGap: false,
      show: false,
    },
    yAxis: {
      type: "value",
      show: false,
    },
    series: [
      {
        data: conCountData,
        type: "line",
        showSymbol: false,
        itemStyle: {
          color: "#6366F1",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "#9BC9FE",
            },
            {
              offset: 1,
              color: "#FFFFFF",
            },
          ]),
        },
      },
      {
        data: conCountDataMom,
        type: "line",
        showSymbol: false,
        itemStyle: {
          color: props.contentCountSevenDayMom > 0 ? "#34D399" : "#FBBF24",
        },
      },
    ],
  });
};
onMounted(() => {
  nextTick(() => {
    setFunnel();
  });
});
</script>
  
  <style lang="less" scoped>
.line-chart-mom {
  height: 240px;
  position: relative;
  top: -20px;
}
</style>
  