<!-- 机构管理 -->
<template>
  <div
    ref="pageContent"
    class="dept-content"
    :class="{ 'page-empty': !dataSource || dataSource.length === 0 }"
  >
    <a-spin :spinning="pageLoading">
      <a-collapse v-model:activeKey="activeKey" accordion>
        <a-collapse-panel :key="item.id" v-for="item in dataSource">
          <template #header>
            <div class="collapse-header-box">
              <div class="header-left">
                {{ item.label }}
              </div>
              <div class="header-right">
                <span class="header-section"
                  >二级部门：{{ item.children.length }} 个</span
                >
                <MoreOutlined @click.stop="showModaAccount(item)" />
              </div>
            </div>
          </template>
          <div
            class="collapse-content-list"
            v-for="(childrenItem, i) in item.children"
            :key="i.id"
          >
            <p class="section-tag">二级部门</p>
            <p class="section-name">{{ childrenItem.label }}</p>
          </div>
          <div class="collapse-content-list" v-if="item.children.length === 0">
            暂无机构数据，请联系管理员
          </div>
        </a-collapse-panel>
      </a-collapse>
      <a-empty
        :image="simpleImage"
        v-if="!dataSource || dataSource.length === 0"
      />
    </a-spin>
  </div>
  <setMenu v-if="setData.modalOpen" :accountId="accountId" :setData="setData" />
</template>
<script setup>
import { deptList, menuAllTree, menuAllDetail } from "@/api/deptManage";
import { Empty } from "ant-design-vue";
import setMenu from "./dept-manage/setMenu.vue";
import emitter from "@/assets/js/emitter";
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const activeKey = ref([]);
const dataSource = ref([]);
const pageLoading = ref(false);
// 获取列表数据
const getDeptList = async () => {
  pageLoading.value = true;
  const res = await deptList();
  pageLoading.value = false;
  if (res.retCode === "000000") {
    dataSource.value = res.data;
    activeKey.value.push(res.data[0].id)
  } else {
    message.error(res.retMsg);
  }
};
// useMousePosition()
onMounted(() => {
  getDeptList();
});

//分配账号
const setData = reactive({
  modalOpen: false,
  id: null,
  deptListDatas: [],
});

const showModaAccount = async (item) => {
  pageLoading.value = true;
  const res = await menuAllTree();
  if (res.retCode === "000000") {
    setData.id = item.id;
    Object.assign(setData.deptListDatas, res.data);
    getMenuAllDetail(item.id);
  } else {
    message.error(res.retMsg);
  }
};
//获取省份关联菜单详情
const accountId = ref([]);
const getMenuAllDetail = async (deptId) => {
  const res = await menuAllDetail({ deptId });
  if (res.retCode === "000000") {
    pageLoading.value = false;
    accountId.value = res.data || [];
    setData.modalOpen = true;
  } else {
    message.error(res.retMsg);
  }
};
//设置菜单后更新数据
emitter.on("setMenu", () => {
  setData.modalOpen = false;
});
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("setMenu");
});
</script>
<style lang="less" scoped>
.page-empty {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dept-content {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 120px);
  overflow-y: auto;
  .anticon-menu {
    font-size: 12px;
    color: #fe8308;
  }
  .ant-collapse {
    border: 0;
    background: #fff;
  }
  .ant-collapse-item {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
    border-radius: 4px;
    margin-bottom: 10px;
    overflow: hidden;
  }
  .collapse-header-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .header-left {
    font-size: 14px;
    color: #1e293b;
    font-weight: bold;
  }
  .header-right {
    display: flex;
    font-size: 12px;
    color: #1e293b;
  }
  .header-section {
    padding-right: 20px;
  }
  .anticon-more {
    font-size: 16px;
  }
  .ant-collapse :deep(.ant-collapse-content > .ant-collapse-content-box) {
    padding: 0 15px;
  }
  .collapse-content-list {
    padding: 10px 25px;
    display: flex;
    align-items: center;
    color: rgba(30, 41, 59, 0.5);
    border-bottom: 1px solid #f0f3f7;
  }
  .section-tag {
    color: #fff;
    margin-right: 10px;
    font-size: 12px;
    padding: 5px 10px;
    background: rgba(254, 86, 8, 0.8);
    border-radius: 4px;
  }
  .section-name {
    font-size: 14px;
    padding: 5px 10px;
    color: #1e293b;
  }
}
</style>


