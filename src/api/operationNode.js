import $https from "@/assets/js/request"

// 列表
export const nodePage = (data) => {
    return $https.fetchGet('/cop/manage/node/page',data)
}
// 编辑 
export const nodeEdit = (data) => {
    return $https.fetchPost('/cop/manage/node/edit',data)
}
// 新增
export const nodeAdd = (data) => {
    return $https.fetchPost('/cop/manage/node/add',data)
}
// 删除
export const nodeDel = (data) => {
    return $https.fetchPost('/cop/manage/node/del',data)
}
// 账号适配树
export const relTree = (data) => {
    return $https.fetchGet('/cop/manage/account/node/rel/list',data)
}
// 设置账号适配
export const relSave = (data) => {
    return $https.fetchPost('/cop/manage/node/account/rel/save',data)
}
