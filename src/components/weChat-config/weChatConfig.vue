<!-- 选择平台 -->
<template>
  <div class="weChat-config-box">
    <a-select
      @change="changePlatform"
      v-model:value="searchData"
      :options="searchOpt"
      :field-names="{ label: 'name', value: 'weChatId' }"
      placeholder="请选择平台"
    >
    </a-select>
  </div>
</template>

<script setup>
import { message } from "ant-design-vue";
import { commonInit, switchAccount } from "@/api/weChatConfig";
import emitter from "@/assets/js/emitter";
const searchOpt = ref([]),searchData = ref(null)
const getCommonInit = async () => {
  const res = await commonInit();
  if (res.retCode === "000000") {
    searchData.value = res.data.selected;
    searchOpt.value = res.data.data;
    emitter.emit("initWeChatId", searchData.value)
  } else if (res.retCode === "000020") {
    message.error('暂无平台账号');
  } else {
    message.error(res.retMsg);
  }
}
const changePlatform = async (weChatId) => {
  const res = await switchAccount({weChatId});
  if (res.retCode === "000000") {
    emitter.emit("initWeChatId", searchData.value)
  } else {
    message.error(res.retMsg);
  }
}
onMounted(()=>{
  getCommonInit()
})
</script>
