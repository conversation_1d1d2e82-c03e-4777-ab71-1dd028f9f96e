<!-- 欢迎关注语新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="confirmLoading">
      <a-form class="weChat-from">
        <a-form-item label="素材类型" v-bind="validateInfos.materialType">
          <a-select
            v-model:value="addEditFrom.materialType"
            @change="changeMaterialType"
            :options="materialArry"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择素材类型"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="选择文件" v-bind="validateInfos.multipartFile">
          <a-upload
            v-model:fileList="fileList"
            list-type="picture"
            :max-count="1"
            :before-upload="beforeUpload"
            @remove="removeM"
          >
            <a-button :disabled="!addEditFrom.materialType">
              <upload-outlined></upload-outlined>
              点击上传
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item label="素材名称" v-bind="validateInfos.materialName">
          <a-input
            v-model:value="addEditFrom.materialName"
            placeholder="请选择素材名称"
          />
        </a-form-item>
        <a-form-item v-if="addEditFrom.materialType === 'video'" label="视频标题" v-bind="validateInfos.videoTitle">
          <a-input
            v-model:value="addEditFrom.videoTitle"
            placeholder="请选择视频标题"
          />
        </a-form-item>
        <a-form-item v-if="addEditFrom.materialType === 'video'" label="视频简介" v-bind="validateInfos.videoIntroduction">
          <a-input
            v-model:value="addEditFrom.videoIntroduction"
            placeholder="请选择视频简介"
          />
        </a-form-item>
      </a-form>
      <div class="notice-list">
        <em style="color: red;">注* </em><br>
        1.图片素材仅支持bmp/png/jpeg/jpg/gif；资源请小于10M。<br>
        2.音频素材仅支持mp3/wma/wav/amr格式，播放时长不超过60s；资源请小于2M。<br>
        3.视频素材仅支持mp4；资源请小于10M。<br>
      </div>
    </a-spin>
  </a-modal>
</template>
<script setup>
import { message, Form, Upload } from "ant-design-vue";
import https from "@/assets/js/request";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  addEditData: Object,
});
const confirmLoading = ref(false);
const modalLoading = ref(false);
const addEditFrom = props.addEditData.item;
const materialArry = ref([
  {value: 'image',label: '图片'},
  {value: 'video',label: '视频'},
  {value: 'voice',label: '音频'}
])
//上传图片命名（只能用组建专用命名;否则会影响删除后仍然有数据）
const fileList = ref([]);
const changeMaterialType = (value) => {
  fileList.value = addEditFrom.multipartFile = [];
  addEditRules.videoTitle[0].required = false;
  addEditRules.videoIntroduction[0].required = false;
  if(value === 'video') {
    addEditRules.videoTitle[0].required = true;
    addEditRules.videoIntroduction[0].required = true;
  }
}
function removeM() {
  fileList.value = addEditFrom.multipartFile = [];
}
// 上传前判断格式
const beforeUpload = (file) => {
  confirmLoading.value = true;
  let materialSize = 0;//素材限制大小
  if(addEditFrom.materialType === 'image') { //选择素材为图片
    const imgFormat = ['image/jpeg','image/jpg','image/png','image/gif','image/bmp']
    const isFormat = imgFormat.includes(file.type);
    materialSize = 10;
    if (!isFormat) {
      message.error("您上传的图片格式有误！");
      confirmLoading.value = false;
      return Upload.LIST_IGNORE;
    }
  }else if (addEditFrom.materialType === 'video') { //选择素材为视频
    const isFormat = file.type === 'video/mp4';
    materialSize = 10;
    if (!isFormat) {
      message.error("您上传的视频格式有误！");
      confirmLoading.value = false;
      return Upload.LIST_IGNORE;
    }
  }else if (addEditFrom.materialType === 'voice') { //选择素材为音频
    const audioFormat = ['audio/mp3','audio/wma','audio/wav','audio/amr']
    const isFormat = audioFormat.includes(file.type);
    materialSize = 2;
    if (!isFormat) {
      message.error("您上传的音频格式有误！");
      confirmLoading.value = false;
      return Upload.LIST_IGNORE;
    }
  }
  const isLt2M = file.size / 1024 / 1024 < materialSize;
  if (!isLt2M) {
    message.error(`素材大小不能超过${materialSize}M！`);
    confirmLoading.value = false;
    return Upload.LIST_IGNORE;
  }
  addEditFrom.multipartFile = [file];
  fileList.value = [file];
  confirmLoading.value = false;
  return false;
};
//表单规则验证数据
const addEditRules = reactive({
  materialName: [
    {
      required: true,
      message: "请输入素材名称",
    },
    {
      min: 1,
      max: 40,
      message: "长度在1 到 40个字符",
      trigger: "change",
    },
  ],
  multipartFile: [
    {
      required: true,
      message: "请选择文件",
      trigger: "change"
    }
  ],
  materialType: [
    {
      required: true,
      message: "请选择素材类型",
      trigger: "change"
    }
  ],
  videoTitle: [
    {
      required: false,
      message: "请输入视频标题",
    },
    {
      min: 1,
      max: 40,
      message: "长度在1 到 40个字符",
      trigger: "change",
    },
  ],
  videoIntroduction: [
    {
      required: false,
      message: "请输入视频简介",
    },
    {
      min: 1,
      max: 80,
      message: "长度在1 到 80个字符",
      trigger: "change",
    },
  ]
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//新增数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      confirmLoading.value = true;
      const { 
        multipartFile,
        materialName,
        materialType,
        videoTitle,
        videoIntroduction
      } = toRaw(addEditFrom);
      const formData = new FormData();
      formData.append("materialName", materialName);
      formData.append("materialType", materialType);
      formData.append("videoTitle", videoTitle);
      formData.append("videoIntroduction", videoIntroduction);
      formData.append("multipartFile", multipartFile[0]);
      const uploadUrl = `/cop/wx/back/news/uploadMaterial?chkCurWx=${props.addEditData.weChatId}&replayTime=` + new Date().getTime()
      const config = {
        headers: { "Content-Type": "multipart/form-data; charset=utf-8" },
      };
      const instance = https.axios.create({ withCredentials: true });
      instance.post(uploadUrl, formData, config)
      .then((xhr) => {
        modalLoading.value = false;
        confirmLoading.value = false;
        if (xhr.data.retCode === "000000") {
          message.success("操作成功");
          emitter.emit("ukwAdd", true);
        } else {
          message.error(xhr.data.retMsg);
        }
      });
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style scoped lang="less">
.ant-modal {
  .weChat-from {
    justify-content: flex-start;
  }
  .notice-list {
    font-size: 12px;
    color: #666;
  }
}
</style>