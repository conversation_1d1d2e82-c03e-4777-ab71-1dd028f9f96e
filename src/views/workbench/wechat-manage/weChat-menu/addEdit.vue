<!-- 欢迎关注语新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-form class="weChat-from">
        <a-form-item label="菜单名称" v-bind="validateInfos.menuName">
          <a-input
            v-model:value="addEditFrom.menuName"
            placeholder="请输入菜单名称"
          />
        </a-form-item>
        <a-form-item label="菜单类型" v-bind="validateInfos.menuType">
          <a-select
            v-model:value="addEditFrom.menuType"
            :options="menuTypeOp"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择菜单类型"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="菜单位置" v-bind="validateInfos.orders">
          <a-select
            v-model:value="addEditFrom.orders"
            :options="props.addEditData.menuOptions"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择菜单位置"
          >
          </a-select>
        </a-form-item>
        <a-form-item
          label="页面路径"
          v-bind="validateInfos.miniprogramPagepath"
          v-if="addEditFrom.menuType === 'miniprogram'"
        >
          <a-input
            v-model:value="addEditFrom.miniprogramPagepath"
            placeholder="请输入小程序页面路径"
          />
        </a-form-item>
        <a-form-item
          label="appid"
          v-bind="validateInfos.miniprogramAppid"
          v-if="addEditFrom.menuType === 'miniprogram'"
        >
          <a-input
            v-model:value="addEditFrom.miniprogramAppid"
            placeholder="请输入小程序appid"
          />
        </a-form-item>
        <a-form-item
          label="网页链接"
          v-bind="validateInfos.url"
          v-if="addEditFrom.menuType !== 'click'"
        >
          <a-input
            v-model:value="addEditFrom.url"
            placeholder="请输入网页链接"
          />
        </a-form-item>
        <a-form-item
          label="关联素材"
          v-bind="validateInfos.templateName"
          v-if="addEditFrom.menuType === 'click'"
        >
          <a-input
            v-model:value="addEditFrom.templateName"
            placeholder="请选择关联素材"
            disabled
          />
        </a-form-item>
        <a-button
          type="primary"
          v-if="addEditFrom.menuType === 'click'"
          @click="materialSelectorData"
          class="submit-btn"
        >
          选择
        </a-button>
      </a-form>
    </a-spin>
  </a-modal>
  <materialTemplate v-if="selectData.modalOpen" :selectData="selectData" />
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { menuType, menuEdit, menuAdd } from "@/api/weChatMenu";
import materialTemplate from "../material-template/materialTemplate.vue";
import emitter from "@/assets/js/emitter";
import { validateUrl } from "@/assets/js/formValidator";
const props = defineProps({ 
  addEditData: Object,
});

const modalLoading = ref(false);
const addEditFrom = props.addEditData.item;
//表单规则验证数据
const addEditRules = reactive({
  menuName: [
    {
      required: true,
      message: "请输入菜单名称",
    },
    {
      min: 1,
      max: 8,
      message: "长度在1 到 8个字符",
    },
  ],
  menuType: [
    {
      required: true,
      message: "请选择菜单类型",
      trigger: "change",
    },
  ],
  orders: [
    {
      required: true,
      message: "请选择菜单位置",
      trigger: "change",
    },
  ],
  miniprogramPagepath: [
    {
      required: true,
      message: "请输入小程序页面链接"
    }
  ],
  miniprogramAppid: [
    {
      required: true,
      message: "请输入小程序appid",
    }
  ],
  url: [
    {
      required: true,
      message: "请输入网页链接"
    }
  ],
  templateName: [
    {
      required: true,
      message: "请选择关联素材",
      trigger: "change",
    },
  ],
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
// 动态验证
watch(
  () => addEditFrom.menuType,
  (newVal, oldVal) => {
    if (newVal === "miniprogram") {
      //小程序
      addEditRules.miniprogramPagepath[0].required = true;
      addEditRules.miniprogramAppid[0].required = true;
      addEditRules.url[0].required = true;

      addEditRules.templateName[0].required = false;
    } else if (newVal === "click") {
      //消息触发类
      addEditRules.templateName[0].required = true;

      addEditRules.miniprogramPagepath[0].required = false;
      addEditRules.miniprogramAppid[0].required = false;
      addEditRules.url[0].required = false;
    } else if (newVal === "view") {
      //网页链接类
      addEditRules.url[0].required = true;

      addEditRules.templateName[0].required = false;
      addEditRules.miniprogramPagepath[0].required = false;
      addEditRules.miniprogramAppid[0].required = false;
    }
  },
  { immediate: true }
);
//选择素材
const selectData = reactive({
  modalOpen: false,
  weChatId: props.addEditData.weChatId,
  item: "",
});
const materialSelectorData = (item = {}) => {
  selectData.modalOpen = true;
};
// 移除scroll监听
onUnmounted(() => {
  emitter.off("selectArray");
});
let params = {};
emitter.on("selectArray", (value) => {
  console.log(value);
  params = value;
  addEditFrom.templateName = `${value.templateName}-${value.typeName}`;
  addEditFrom.msgtype = value.templateType;
  (addEditFrom.templateId =
    value.templateType === "post" || value.templateType === "newsitem"
      ? value.checkboxs.join(",")
      : value.id),
    (selectData.modalOpen = false);
});
//  获取菜单类型
const menuTypeOp = ref([]);
async function getMenuType() {
  modalLoading.value = true;
  const res = await menuType({ chkCurWx: props.addEditData.weChatId });
  modalLoading.value = false;
  if (res.retCode === "000000") {
    for (let k in res.data) {
      menuTypeOp.value.push({
        value: k,
        label: res.data[k],
      });
    }
  } else {
    message.error(res.retMsg);
  }
}
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const {
        id,
        menuName,
        menuType,
        orders,
        msgtype,
        templateId,
        miniprogramPagepath,
        miniprogramAppid,
        url,
        templateName,
      } = toRaw(addEditFrom);
      let fromData = {
        menuName,
        menuType,
        orders,
        msgtype,
        templateId,
        miniprogramPagepath,
        miniprogramAppid,
        url,
        templateName,
      };
      // if(params.templateType === 'post') {
      //   fromData.msgType = 'news';
      //   fromData.templateId = params.checkboxs.join(',');
      //   fromData.templateName = `${params.templateName}{${params.checkboxs.length}}`;
      // }else if(params.templateType === 'newsitem') {
      //   fromData.msgType = 'news';
      //   fromData.msgTriggerType = 'text';
      //   fromData.templateId = params.checkboxs.join(',');
      //   fromData.templateName = `${params.templateName}{${params.checkboxs.length}}`;
      // }
      if (!addEditFrom.id) {
        //新增
        res = await menuAdd(fromData, selectData.weChatId);
      } else {
        //编辑提交
        fromData.id = id;
        res = await menuEdit(fromData, selectData.weChatId);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditMenu", true);
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
onMounted(() => {
  getMenuType();
});
</script>
<style scoped lang="less">
.ant-modal {
  .weChat-from {
    justify-content: flex-start;
    .ant-btn-primary {
      margin-left: 10px;
    }
  }
}
::v-deep .ant-modal .ant-modal-body {
  border: 1px solid red;
}
</style>