<!-- 运营节点 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-date-picker
        picker="month"
        v-model:value="searchForm.searchDate"/>
      <a-button type="primary" @click="searchFun" class="submit-btn">
        <template #icon>
          <SearchOutlined />
        </template>
        查询 
      </a-button>
    </div>
    <a-button type="primary" @click="showModal()" class="submit-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增策划节点
    </a-button>
  </div>
  <div class="operation-node">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <!-- <span @click="showModaAccount(record)">账号</span> -->
            <span @click="showModal(record)">编辑</span>
            <span class="del-btn" @click="delFun(record)">删除</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <addEdit :addEditData="addEditData" />
  <setAccount v-if="setData.modalOpen" :setData="setData" />
</template>

<script setup>
import { nodePage, nodeDel, relTree } from '@/api/operationNode';
import useGetList from '@/hooks/useGetList';
import addEdit from './operation-node/addEdit.vue';
import setAccount from './operation-node/setAccount.vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import emitter from '@/assets/js/emitter';
import useDel from '@/hooks/useDel';
let dataSource = ref([]);

const tableColumns = reactive([
  {
    title: '日期',
    dataIndex: 'nodeDate'
  },
  {
    title: '可策划节点',
    dataIndex: 'name'
  },
  // {
  //   title: '适用账号',
  //   dataIndex: 'accountName',
  //   ellipsis: true
  // },
  {
    title: '操作',
    dataIndex: 'operate',
    width: 160,
    fixed: "right"
  },
]);
const pageLoading = ref(false);
//表单数据
const searchForm = reactive({
  searchDate: '',
});
// 获取列表数据
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
})
let date = null
onMounted(() => {
  useGetList(nodePage, pageLoading, dataSource, pagination);
});
const handleTableChange = (pag) => { // 点击分页处理
  useGetList(nodePage, pageLoading, dataSource, pagination,{ date ,pageNum: pag.current, pageSize: pag.pageSize });
};
//查询列表
const searchFun = () => {
  const { searchDate } = toRaw(searchForm);
  date = searchDate?searchDate.format('YYYY-MM'):null;
  useGetList(nodePage, pageLoading, dataSource, pagination,{ date,pageNum: 1 });
};
//分配账号
const setData = reactive({
  modalOpen: false,
  nodeId: null,
  accountId: [],
  deptListDatas: []
})
const showModaAccount = async (item) => {
  pageLoading.value = true;
  setData.deptListDatas = []
  setData.accountId = []
  const res = await relTree();
  if (res.retCode === "000000") {
    setData.modalOpen = true;
    pageLoading.value = false;
    setData.nodeId = item.id;
    if(item.accountId) {
      item.accountId.split(',').forEach((el,i) => {
        setData.accountId.push(parseInt(el))
      });
    }else {
      setData.accountId = []
    }
    Object.assign(setData.deptListDatas,res.data)
  } else {
    message.error(res.retMsg);
  }
};
//新增/编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: '新增策划节点',
  list: {name:'',nodeDate:''}
});
const showModal = (item = {name:'',nodeDate:'',id: null}) => {
  Object.assign(addEditData.list,item)
  if (item.id) {
    addEditData.modalTitle = '编辑策划节点';
    addEditData.list.nodeDate = dayjs(item.nodeDate, 'YYYY-MM-DD');
  }else {
    addEditData.modalTitle = '新增策划节点';
  }
  addEditData.modalOpen = true;
};
//新增/编辑之后更新数据
emitter.on('addEditComplete',(value)=>{
  addEditData.modalOpen = false;
  setData.modalOpen = false;
  useGetList(nodePage, pageLoading, dataSource, pagination);
}) 
//组件卸载后销毁emitter
onUnmounted(()=>{
  emitter.off('addEditComplete')
})
// 删除数据
const delFun = async (list) => {
  useDel(nodeDel,{id:list.id}, pageLoading, (res)=>{
    if (res.retCode === '000000') {
      useGetList(nodePage, pageLoading, dataSource, pagination);
      message.success('操作成功！')
    }else if(res.retCode === '990003') {
      message.error('此节点已新建排期，无法删除');
    } else {
      message.error(res.retMsg);
    }
  });
};
</script>
<style lang="scss" scoped>
.operation-node {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-box {
    display: flex;
  }
}
</style>