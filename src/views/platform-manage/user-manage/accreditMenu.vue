<!-- 账号管理菜单授权 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.setMenuData.modalOpen"
    title="授权菜单"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="spinningLoading">
      <a-form>
        <a-form-item label="机构" v-bind="validateInfos.deptId">
          <a-select
            v-model:value="addEditFrom.deptId"
            :options="props.setMenuData.provinceTree"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择机构"
            @change="changeProvince"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="菜单" v-bind="validateInfos.menu">
          <a-tree-select
            v-model:value="addEditFrom.menu"
            :tree-data="menuTree"
            tree-checkable
            allow-clear
            :placeholder="addEditFrom.deptId ? '请选择菜单' : '请先选择机构'"
            :maxTagCount="1"
            :field-names="{ label: 'label', value: 'id' }"
            tree-node-filter-prop="label"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>
  <script setup>
import { message, Form } from "ant-design-vue";
import { menuDeptTree, menuRelSave, menuRelDetail } from "@/api/userManage";
const props = defineProps({
  setMenuData: Object,
});

const modalLoading = ref(false);
const spinningLoading = ref(false);
const addEditFrom = props.setMenuData.list;
const menuTree = ref([]);
// 获取当前机构已授权的菜单
async function getMenuRelDetail(deptId) {
  const params = {
    userId: props.setMenuData.list.id,
    deptId: deptId
  }
  const menuRes = await menuRelDetail(params);
  spinningLoading.value = false;
  if (menuRes.retCode === "000000") {
    addEditFrom.menu = menuRes.data;
  } else {
    message.error(menuRes.retMsg);
  }
}
// 获取菜单树结构
async function getMenuTree(deptId) {
  spinningLoading.value = true;
  const menuRes = await menuDeptTree({ deptId });
  if (menuRes.retCode === "000000") {
    menuTree.value = menuRes.data;
    getMenuRelDetail(deptId)
  } else {
    message.error(menuRes.retMsg);
  }
}

const changeProvince = (value) => {
    getMenuTree(value)
};
//表单规则验证数据
const addEditRules = reactive({
  menu: [
    {
      required: true,
      trigger: "change",
      message: "请选择菜单",
    },
  ],
  deptId: [
    {
      required: true,
      trigger: "change",
      message: "请选择机构",
    },
  ],
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//筛选选中的值
function getOnlyLeafKeys(selectedKeys, treeData) {
  const onlyLeafKeys = [];
 
  function traverse(nodes) {
    nodes.forEach(node => {
      if (selectedKeys.includes(node.id) && (!node.children || node.children.length === 0)) {
        onlyLeafKeys.push(node.id);
      }
      if (node.children) {
        traverse(node.children);
      }
    });
  }
  traverse(treeData);
  return onlyLeafKeys;
}
//菜单授权
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = spinningLoading.value = true;
      let res = {};
      const { menu, deptId, id } = toRaw(addEditFrom);
      const checkedIds = getOnlyLeafKeys(menu, menuTree.value)
      const fromData = {
        userId: id,
        menu: checkedIds.join(","),
        deptId
      };
      res = await menuRelSave(fromData);
      modalLoading.value = spinningLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        props.setMenuData.modalOpen = false;
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
    