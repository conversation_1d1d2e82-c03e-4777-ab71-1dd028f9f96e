<template>
  <div class="login-page">
    <div class="login-box">
      <a-divider class="login-title">欢迎登录</a-divider>
      <h2>系统需要核验您的身份</h2>
      <div class="login-form-box">
        <a-form>
          <a-form-item v-bind="validateInfos.mobile">
            <i class="icon-mobile icon-label"></i>
            <a-input
              :bordered="false"
              :maxlength="11"
              v-model:value="loginFormm.mobile"
              @blur="validate('mobile', { trigger: 'blur' }).catch(() => {})"
              placeholder="请输入手机号码"
            />
          </a-form-item>
          <a-form-item v-bind="validateInfos.imgCode">
            <i class="icon-img-code icon-label"></i>
            <a-input
              :bordered="false"
              class="code-input"
              :maxlength="4"
              v-model:value="loginFormm.imgCode"
              placeholder="请输入图形验证码"
            />
            <div class="code-image-box">
              <img
                class="code-image"
                :src="codeImg"
                @click="getCodeImg"
                alt=""
              />
            </div>
          </a-form-item>
          <a-form-item v-bind="validateInfos.code">
            <i class="icon-code icon-label"></i>
            <a-input
              :bordered="false"
              class="code-input"
              :maxlength="6"
              v-model:value="loginFormm.code"
              placeholder="请输入验证码"
            />
            <div class="get-code-box">
              <a-button 
              @click="getSendcode"
              :disabled="!(isGetCodeBtn && loginFormm.mobile.length === 11)"
              type="link">
              {{ phoneVerifyBtnText }}
            </a-button>
            </div>
          </a-form-item>
          <a-form-item class="submit-box-item">
            <a-button
              type="primary"
              :loading="loadingLogin"
              @click.prevent="onSubmit"
              class="submit-btn"
              >登录</a-button
            >
          </a-form-item>
          <div class="checked-box">
            <a-checkbox size="12" v-model:checked="checked">
              <span>登录即代表同意</span
              ><a href="https://wap.cmpassport.com/resources/html/contract1.html"
                >《服务协议》</a
              >及<a
                href="https://wap.cmpassport.com/resources/html/contract2.html"
                >《隐私政策》</a
              >
            </a-checkbox>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>
<script setup>
import { notification, message, Form } from "ant-design-vue";
import * as loginApi from "@/api/login";
import {validateMobile} from "@/assets/js/formValidator";
//用户协议
const checked = ref(false);

const phoneVerifyBtnText = ref('获取验证码')
const router = useRouter()
//表单规则验证数据
const loginRules = reactive({
  mobile: [
    {
      required: true,
      validator: validateMobile,
      trigger: "blur"
    }
  ],
  imgCode: [
    {
      required: true,
      message: "图形验证码"
    },
  ],
  code: [
    {
      required: true,
      message: "请输入验证码"
    },
  ],
});
//表单数据
const loginFormm = reactive({
  mobile: "",//18208160195
  imgCode: "",
  code: "",
});
//获取验证码按钮置灰
const isGetCodeBtn = computed(() => {
  return loginFormm.imgCode.length === 4;
});
//登录按钮置灰
const isLoginBtn = computed(() => {
  return loginFormm.code.length === 6;
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  loginFormm,
  loginRules
);
//图形验证
const codeImg = ref(`${location.origin}/cop/comm/randCodeImage?replayTime=${new Date().getTime()}`);
const getCodeImg = () => {
  codeImg.value = `${location.origin}/cop/comm/randCodeImage?replayTime=${new Date().getTime()}`;
};
// 验证码定时器
const sendCodeInterval = ref(null);
// 开启验证码定时器
const startInterval = () => {
  if (sendCodeInterval.value) {
    clearInterval(sendCodeInterval.value);
  }
  let num = 0;
  sendCodeInterval.value = setInterval(() => {
    num++;
    if (num >= 60) {
      clearInterval(sendCodeInterval.value);
      sendCodeInterval.value = null;
      phoneVerifyBtnText.value = "重新获取";
    } else {
      phoneVerifyBtnText.value = 60 - num + "后重新获取";
    }
  }, 1000);
};
onUnmounted(() => {
  clearInterval(sendCodeInterval.value);
});
// 获取手机验证码
const getSendcode = async () => {
  if(sendCodeInterval.value) {
    return
  }
  if(!checked.value) {
    notification.error({
      message: "系统消息",
      description: "请勾选协议",
    });
    return
  }
  const userData = toRaw(loginFormm);
  const res = await loginApi.sendcode(
    {
      mobile: userData.mobile,
      randCode: userData.imgCode
    }
  );
  if (res.retCode === "000000") {
    startInterval()
    message.success("发送成功，请注意查收！")
  } else if (res.retCode === "000040") {
    message.error("登录频繁，请60秒后再进行操作")
  } else{
    message.error(res.retMsg)
  } 
};
const loadingLogin = ref(false);
//登录
const onSubmit = () => {
  validate()
    .then(async () => {
      loadingLogin.value = true;
      const userData = toRaw(loginFormm);
      const res = await loginApi.login({
        mobile: userData.mobile,
        smsCode: userData.code,
        randCode: userData.imgCode
      });
      loadingLogin.value = false;
      if (res.retCode === "000000") {
        //登录成功后初始化数据
        localStorage.clear()
        res.data.menuTree = findNodeById(res.data.menuTree,null);
        localStorage.setItem("userData",JSON.stringify(res.data));
        router.push('/homePage')
      } else if (res.retCode === "000011") {
        message.error("系统繁忙，请稍后再试")
      } else if (res.retCode === "100005") {
        message.error("图形验证码错误")
      } else if (res.retCode === "100007") {
        message.error("短信验证码错误")
      } else if (res.retCode === "100006") {
        notification.error({
          message: "系统消息",
          description: "账号被锁定，请稍后重试",
        });
      } else if (res.retCode === "000031") {
        notification.error({
          message: "系统消息",
          description: "无权限登录！请联系管理员",
        });
      } else {
        message.error(res.retMsg)
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
const menuIcon = [
  {url:'../assets/image/aiVideo.png'},
]
// 使用递归函数遍历树形结构，并执行特定操作
function findNodeById(treeData,address) {
  // 遍历当前层级的所有节点
  for (var i = 0; i < treeData.length; i++) {
    var node = treeData[i];
    node.title = node.menuName;
    node.label = node.menuName;
    node.icon = node.address;
    if(!node.parentId || node.children.length === 0) {
      node.address = address?address+'/'+node.address:'/'+node.address;
    } else {
      node.address = address;
    }
    node.key = node.id.toString(); 
    node.id = node.id.toString();
    if(node.children.length === 0) {
      node.children = null;
    }
    // 如果当前节点有子节点，则递归调用当前函数继续查找子节点
    if (node.children && node.children.length > 0) {
      findNodeById(node.children,node.address);
    }
  }
  return treeData;
}
</script>
<style scoped lang='less'>
.login-page {
  height: 100vh;
  background: url(@/assets/image/login-bg.jpg) no-repeat 0 0;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  .login-box {
    width: 440px;
    height: 400px;
    background-color: #fff;
    box-shadow: 0 0 6px 2px rgba(52, 52, 52, 0.1);
    border-radius: 5px;
    box-sizing: border-box;
  }
  .ant-divider-horizontal.ant-divider-with-text {
    min-width: auto;
    width: 280px;
    margin: 0 auto;
    padding-top: 30px;
    border-color: rgba(0, 0, 0, .2);
    :deep(.ant-divider-inner-text) {
      font-size: 24px;
      color: #000;
      font-weight: bold;
      padding: 0 10px;
    }
  }
  h2 {
    font-size: 14px;
    color: #428CD5;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 25px;
  }
  .login-form-box {
    width: 320px;
    margin: 0 auto;
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      height: 38px;
      align-items: center;
      background: #F3F5F8;
      border-radius: 6px;
    }
    .ant-input {
      padding: 4px 0;
      margin-left: 10px;
      background: transparent;
    }
    .icon-mobile {
      background: url(@/assets/image/icon-mobile.png) no-repeat 0 50%;
      
    }
    .icon-img-code {
      background: url(@/assets/image/icon-img-code.png) no-repeat 0 50%;
    }
    .icon-code {
      background: url(@/assets/image/icon-code.png) no-repeat 0 50%;
    }
    .icon-label{
      width: 20px;
      height: 24px;
      display: block;
      margin-left: 10px;
      background-size: contain;
    }
    :deep(.el-input__wrapper) {
      box-shadow: none;
      border-bottom: 1px solid #b3b3b3;
      border-radius: 0;
    }
    .code-input {
      width: 160px;
    }
    .code-image-box {
      text-align: right;
      padding-right: 10px;
      padding-top: 5px;
      cursor: pointer;
      flex: 1;
    }
    .code-image {
      width: 80px;
    }
    .get-code-box {
      text-align: right;
      flex: 1;
    }
    .code-btn {
      background: transparent;
      border: 0;
      padding:0 10px;
      font-size: 14px;
      color: #d9d9d9;
    }
    .act-btn {
      color: #428CD5;
    }
  }
  .submit-box-item{
    margin-bottom: 10px;
    :deep(.ant-form-item-control-input-content) {
      background: transparent;
      height: 45px;
    }
    .submit-btn {
      height: 38px;
      width: 100%;
      border: 0;
      justify-content: center;
      background-image: linear-gradient(90deg, #3B82F6 0%, #2563EB 100%);
      border-radius: 22px;
      padding: 0;    
      font-size: 16px;
    }
  }
  .checked-box{
    text-align: center;
  }
  .ant-form {
    label{
      font-size: 12px;
      line-height: 18px;
    }
  }
}
</style>