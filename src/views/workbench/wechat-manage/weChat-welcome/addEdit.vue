<!-- 欢迎关注语新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-form class="weChat-from">
        <a-form-item label="关联素材" v-bind="validateInfos.templateName">
          <a-input
            :value="!addEditFrom.templateName?addEditFrom.templateName:addEditFrom.templateName+' - '+addEditFrom.msgTypeName"
            placeholder="请选择关联素材"
            disabled
          />
        </a-form-item>
        <a-button type="primary" @click="materialSelectorData" class="submit-btn">
          选择
        </a-button>
      </a-form>
    </a-spin>
  </a-modal>
  <materialTemplate v-if="selectData.modalOpen" :selectData="selectData" />
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { subEdit, subAdd } from "@/api/weChatWelcome";
import materialTemplate from '../material-template/materialTemplate.vue';
import emitter from "@/assets/js/emitter";
const props = defineProps({
  addEditData: Object,
});

const modalLoading = ref(false);
const addEditFrom = props.addEditData.list;
//表单规则验证数据
const addEditRules = reactive({
  templateName: [
    {
      required: true,
      message: "请选择关联素材",
    }
  ]
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//选择素材
const selectData = reactive({
  modalOpen: false,
  weChatId: props.addEditData.weChatId,
  item: '',
});
const materialSelectorData = (item = {}) => {
  selectData.modalOpen = true
};
// 移除scroll监听
onUnmounted(()=>{
  emitter.off("selectArray");
})
let params = {};
emitter.on("selectArray", (value) => {
  params = value
  addEditFrom.templateName = `${value.templateName}-${value.typeName}`
  selectData.modalOpen = false
});
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const { id } = toRaw(addEditFrom);
      let fromData = {
        msgType: params.templateType,
        templateId: params.id,
        templateName: params.templateName
      };
      if(params.templateType === 'post') {
        fromData.msgType = 'news';
        fromData.templateId = params.checkboxs.join(',');
        fromData.templateName = `${params.templateName}{${params.checkboxs.length}}`;
      }else if(params.templateType === 'newsitem') {
        fromData.msgType = 'news';
        fromData.msgTriggerType = 'text';
        fromData.templateId = params.checkboxs.join(',');
        fromData.templateName = `${params.templateName}{${params.checkboxs.length}}`;
      }
      if (!addEditFrom.id) {
        //新增
        res = await subAdd(fromData,selectData.weChatId);
      } else {
        //编辑提交
        fromData.id = id;
        res = await subEdit(fromData,selectData.weChatId);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditWelcome", true);
      } else if (res.retCode === "000030") {
        message.error("请勿添加相同关注语");
      }else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style scoped lang="less">
.ant-modal {
  .weChat-from {
    justify-content: flex-start;
    .ant-btn-primary {
      margin-left: 10px;
    }
  }
}
</style>