<!-- 业务标签新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
    width="680px"
  >
    <a-spin :spinning="modalLoading">
      <a-form>
        <a-form-item label="一类标签" v-bind="validateInfos.name">
          <a-input
            v-model:value="addEditFrom.name"
            placeholder="请输一类标签"
          />
        </a-form-item>
        <a-form-item label="二类标签">
          <template v-for="(tag,index) in state.tags" :key="tag.name">
            <template v-if="!tag.inputVisible">
              <a-tooltip v-if="tag.name.length > 20" :title="tag.name">
                <a-tag closable @close="handleClose(tag)">
                  {{ `${tag.name.slice(0, 20)}...` }}
                  <template #closeIcon>
                    <DeleteFilled />
                  </template>
                </a-tag>
              </a-tooltip>
              <a-tag v-else @click="showInputEdit(index,tag.name)">
                {{ tag.name }}
                <DeleteFilled @click.stop="handleClose(tag)"/>
              </a-tag>
            </template>
            <a-input
              v-else
              :ref="setInputRef"
              v-model:value="state.inputValue"
              type="text"
              size="small"
              class="new-tag"
              @blur="handleInputConfirm(index)"
              @keyup.enter="handleInputConfirm"
            />
          </template>
          <a-input
            ref="inputRef"
            v-if="state.inputVisible"
            v-model:value="state.inputValue"
            type="text"
            size="small"
            class="new-tag"
            @blur="handleInputConfirm('123')"
            @keyup.enter="handleInputConfirm"
          />
          <a-tag
            v-else
            style="background: #fff; border-style: dashed"
            @click="showInput"
          >
            <plus-outlined />
            新增
          </a-tag>
        </a-form-item>
        
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { message, Form } from "ant-design-vue";
import { tagEdit, checkdel } from "@/api/contentTag";
const props = defineProps({
  addEditData: Object,
});
const emit = defineEmits(['addEditTag']);
//初始化二类标签数据
const inputRef = ref();
const state = reactive({
  tags: [],
  inputVisible: false,
  inputVisibleEdit: true,
  inputValue: "",
});
const modalLoading = ref(false);
const addEditFrom = props.addEditData.list;
addEditFrom.name = props.addEditData.list.label
props.addEditData.list.children.forEach(el=>{
  state.tags.push({...el,name:el.label,inputVisible:false})
})
//表单规则验证数据
const addEditRules = reactive({
  name: [
    {
      required: true,
      message: "请输入一类标签名称",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change",
    },
  ],
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);

//删除二类标签
const handleClose = async (removedTag) => {
  if (props.addEditData.list.id && removedTag.id) {
    const res = await checkdel({secondTagId:removedTag.id});
    if (res.retCode === "000000") {
      const tags = state.tags.filter((tag) => tag.name !== removedTag.name);
      state.tags = tags;
    } else if (res.retCode === "990003") {
      message.error("此标签已关联排期，无法删除");
    } else {
      message.error(res.retMsg);
    }
  } else {
    const tags = state.tags.filter((tag) => tag.name !== removedTag.name);
    state.tags = tags;
  }
};
//new tag
const showInput = () => {
  state.inputVisible = true;
  nextTick(() => {
    inputRef.value.focus();
  });
};
// 用于存储元素引用的数组
const inputRefs = ref();
// 设置ref的函数
const setInputRef = (el, index) => {
  // 当元素被挂载后，更新引用数组
  if (el) {
    inputRefs.value = el;
  }
};
//编辑是的inputTag
const showInputEdit = (index,val) => {
  state.tags[index].inputVisible = true;
  state.inputValue = val
  nextTick(() => {
    inputRefs.value.focus();
  });
};
//失去焦点新增/编辑本地二类数据
const handleInputConfirm = (index=null) => {
  const inputValue = state.inputValue;
  let tags = state.tags;
  if(inputValue) {
    if(typeof(index) === 'number') {//编辑
      tags[index].name = inputValue
      tags[index].inputVisible = false;
    } else {
      tags = [...tags, {name:inputValue,inputVisible:false}];
    }
  } else {
    tags.splice(index,1)
  }
  Object.assign(state, {
    tags,
    inputVisible: false,
    inputValue: "",
  });
};
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => { 
      modalLoading.value = true;
      let res = {};
      const { name, children } = toRaw(addEditFrom);
      if (!props.addEditData.list.id) {
       //新增
        const fromData = {
          name,
          children:[]
        };
        state.tags.forEach((el,index) => {
          fromData.children.push({name:el.name})
        });
        res = await props.addEditData.addTag(fromData);
      } else {
        //编辑提交
        const fromData = {
          name,
          children: [],
          id: props.addEditData.list.id
        };
        state.tags.forEach((el,index) => {
          fromData.children.push({name:el.name,id:el.id})
        });
        res = await tagEdit(fromData);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emit('addEditTag')
      } else if (res.retCode === "000030") {
        message.error("已存在相同名称的一类标签");
      } else if (res.retCode === "000011") {
        message.error("请勿输入同名二类标签");
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>

<style lang="less" scoped>
.ant-modal {
  .ant-form {
    flex-direction: column;
  }
  .ant-tag:hover {
    border-color: rgba(253,114,113,.8);
    :deep(.anticon-delete) {
      color: rgba(253,114,113,.8);
    }
  }
  .ant-tag {
    font-size: 14px;
    line-height: 30px;
    color: #475569;
    margin-bottom: 10px;
    :deep(.anticon-delete) {
      position: relative;
      top: 5px;
      right: -6px;
      padding: 2px;
      font-size: 10px;
    }
  }
  .ant-input-sm {
    padding: 4px 7px;
  }
  .new-tag {
    width: 78px;
    margin-bottom: 10px;
    margin-right: 8px;
  }
}

</style>
