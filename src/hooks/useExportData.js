import https from "@/assets/js/request";
import qs from 'qs'
import { notification } from "ant-design-vue";

export default function useExportData(url,loading,exportLoading,params,chkCurWx) {
  loading.value = true;
  exportLoading.value = true;
  params.replayTime = new Date().getTime();
  if(chkCurWx) {
    params.chkCurWx = chkCurWx.value;
  }
  https
    .axios({
      method: "get",
      url: `${url}?${qs.stringify(params)}`,
      responseType: "blob"
    })
    .then((data) => {
      exportLoading.value = false;
      loading.value = false;
      if(!data) {
        return false
      }
      notification.success({
        message: "系统消息",
        description: "正在下载，请稍后!",
      });
      // 截取文件名，这里是后端返回了文件名+后缀，如果没有可以自己拼接
      const fileName = decodeURI(data.headers["content-disposition"])
        .split(";")[1]
        .split("=")[1];
      // 将`blob`对象转化成一个可访问的`url`
      const url = window.URL.createObjectURL(new Blob([data.data]));
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
}