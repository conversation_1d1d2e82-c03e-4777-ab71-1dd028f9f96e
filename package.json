{"name": "content-operate-platform", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"ant-design-vue": "^4.2.3", "antd": "^5.20.6", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "jsencrypt": "^3.3.2", "mitt": "^3.0.1", "qs": "^6.13.0", "vue": "^3.4.29", "vue-router": "^4.3.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "less": "^4.2.0", "less-loader": "^12.2.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.3.1", "vite-plugin-compression": "^0.5.1"}}