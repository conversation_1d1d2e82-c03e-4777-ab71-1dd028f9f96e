<!-- 关键字管理新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-form class="weChat-from">
        <a-form-item label="关键字" v-bind="validateInfos.keyword">
          <a-input
            v-model:value="addEditFrom.keyword"
            placeholder="多个关键字以英文逗号隔开"
          />
        </a-form-item>
        <a-form-item label="匹配类型" v-bind="validateInfos.keywordType">
          <a-select
            v-model:value="addEditFrom.keywordType"
            :options="triggerArry"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择匹配类型"
          >
          </a-select>
        </a-form-item>
        <a-form-item 
          label="关联素材" 
          class="material-selector"
          :key="template.key" 
          v-for="(template,index) in addEditFrom.tmplList" 
          v-bind="validateInfos.templateName">
          <a-input
            :value="!template.templateName?template.templateName:template.templateName+' - '+template.templateLabel"
            placeholder="请选择关联素材"
            disabled
          />
          <a-button type="primary" @click="materialSelectorData(index)" class="submit-btn">
            选择
          </a-button>
          <PlusOutlined v-if="index === 0" @click="addTemplateInput"/>
          <MinusOutlined v-else @click="removeIpInput(template)"/>
        </a-form-item>
      </a-form>
      <div class="notice-list">
        <em style="color: red;">注* </em><br>
        1.关键字回复多条消息，且关联素材选择文章，文章复选框只能选择一个；<br>
        2.关键字回复多条消息，且关联素材选择单图文，单图文复选框只能选择一个；<br>
        3.关键字回复多条消息，关联素材不能选择图文消息；<br>
        4.关键字回复单条消息，无以上限制；<br>
      </div> 
    </a-spin>
  </a-modal>
  <materialTemplate v-if="selectData.modalOpen" :selectData="selectData" />
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { rspEdit, rspAdd, rspDetail } from "@/api/weChatKeyword";
import materialTemplate from '../material-template/materialTemplate.vue';
import emitter from "@/assets/js/emitter";
const props = defineProps({
  addEditData: Object,
});

const modalLoading = ref(false);
const addEditFrom = props.addEditData.list;
//表单规则验证数据
const addEditRules = reactive({
  keyword: [
    {
      required: true,
      message: "请输入关键字",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符",
      trigger: "change",
    },
  ],
  keywordType: [
    {
      required: true,
      message: "请选择匹配类型",
      trigger: "change"
    }
  ],
  templateName: [
    {
      required: true,
      message: "请选择关联素材"
    }
  ]
});
//  获取出发类型
const triggerArry = ref([
  {value: '1',label: '全匹配'},
  {value: '2',label: '模糊匹配'}
])
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//选择素材
const selectData = reactive({
  modalOpen: false,
  weChatId: props.addEditData.weChatId,
  item: ''
});
const addTemplateInput = ()=> {//新增关联模版选择框
  if(addEditFrom.tmplList.length >= 5){
    message.error('最多关联5条素材')
    return
  }
  addEditFrom.tmplList.push(
    {
      key: Date.now(),
      templateId:null,
      templateName:'',
      templateLabel:'',
      msgType:'',
      orderId: addEditFrom.tmplList.length+1
    }
  );
};
const removeIpInput = (item)=> {//移除关联模版选择框
  const index = addEditFrom.tmplList.indexOf(item)
  if (index !== -1) {
    addEditFrom.tmplList.splice(index, 1)
  }
}
let selectorIndex = 0;//选择素材索引
const materialSelectorData = (index) => {
  selectorIndex = index;
  selectData.modalOpen = true;
};
// 移除scroll监听
onUnmounted(()=>{
  emitter.off("selectArray");
})
//emitter选择后赋值
let params = {};
emitter.on("selectArray", (value) => {
  params = value; 
  addEditFrom.templateName = `${value.templateName}-${value.typeName}`;
  addEditFrom.tmplList[selectorIndex] = {
    msgType: params.templateType === 'post' || params.templateType === 'newsitem'?'news':params.templateType,
    orderId: 0,
    templateId: params.templateType === 'post' || params.templateType === 'newsitem'?params.checkboxs.join(','):params.id,
    templateLabel: params.typeName,
    templateName: params.templateName
  };
  selectData.modalOpen = false;
});

//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const { id, keyword, keywordType, tmplList } = toRaw(addEditFrom);
      let fromData = {
        keyword,
        keywordType,
        tmplList
      };
      if (!addEditFrom.id) {
        res = await rspAdd(fromData,selectData.weChatId);
      } else {
        //编辑提交
        fromData.id = id;
        res = await rspEdit(fromData,selectData.weChatId);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditRsp", true);
      } else if (res.retCode === "000030") {
        message.error("请勿添加相同关键字");
      }else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style scoped lang="less">
.ant-modal {
  .weChat-from {
    justify-content: flex-start;
    .ant-btn-primary {
      margin-left: 10px;
    }
  }
  .material-selector {
    ::v-deep(.ant-form-item-control-input-content) {
      display: flex;
    }
    .anticon {
      cursor: pointer;
      margin-left: 10px;
      color: #FE8308;
    }
  }
}
.notice-list {
  font-size: 12px;
  color: #666;
}
</style>