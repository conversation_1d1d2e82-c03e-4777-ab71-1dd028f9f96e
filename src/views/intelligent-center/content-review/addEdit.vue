<!-- 内容审核新增编辑 -->
<template>
  <a-drawer
    :width="1040"
    :closable="false"
    placement="right" 
    class="history-list-drawer"
    :force-render="true"
    :maskClosable="true"
    :open="props.addEditData.modalOpen"
  >
    <div class="drawer-header">
      <p class="drawer-title">{{ props.addEditData.modalTitle }}</p>
      <p class="drawer-closa" @click="props.addEditData.modalOpen = false"><CloseOutlined /></p>
    </div>
    <a-spin :spinning="confirmLoading">
      <a-form class="add-edit-from">
        <div class="platform-title">
          <span>*</span>
          发布平台
        </div>
        <div class="account-form">
          <a-form-item v-for="(item,index) in accounts" 
          :key="item.value"
          :label="item.name">
            <a-select
              v-model:value="addEditFrom.accountsId[index].accounts"
              :options="item.accountList"
              :field-names="{ label: 'name', value: 'id' }"
              allow-clear
              :max-tag-count="1"
              mode="multiple"
              :placeholder="'请选择'+item.name"
            >
            </a-select>
          </a-form-item>
        </div>
        <div class="back-form">
          <div class="title-form">
            <a-form-item label="文章标题" v-bind="validateInfos.title">
              <a-input class="title-input" v-model:value="addEditFrom.title" placeholder="请输入文章标题" />
            </a-form-item>
            <a-form-item label="作者" v-bind="validateInfos.author">
              <a-input v-model:value="addEditFrom.author" placeholder="请输入作者" />
            </a-form-item>
            <a-form-item label="内容上传方式">
              <a-radio-group v-model:value="addEditFrom.uploadMethod" @change="switchupLoadMethod">
                <a-radio-button :value="1">素材上传</a-radio-button>
                <a-radio-button :value="2">文件上传</a-radio-button>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="文章分类">
              <a-select
                v-model:value="addEditFrom.articleCategory"
                :options="articleCategoryOpt"
                :field-names="{ label: 'name', value: 'id' }"
                placeholder="请选择文章分类"
              >
              </a-select>
            </a-form-item>
          </div>
          <div class="upload-form">
            <a-form-item label="文档上传" v-bind="validateInfos.fileList" v-if="addEditFrom.uploadMethod === 2" >
              <a-upload
                v-model:file-list="fileDataList"
                :action="`/cop/audit/upload/file?replayTime=${new Date().getTime()}`"
                :before-upload="fileBeforeUpload"
                :headers="headers"
                :progress="progress"
                @change="fileSuccessUpload"
              >
                <a-button :disabled="fileUploading">
                  <upload-outlined></upload-outlined>
                  上传文档
                </a-button>
                <span class="upload-padding">支持上传格式：doc、docx、pdf、ppt、pptx、xlsx、xls、vtt、txt，(文档中不能包含视频及附件，文档类型字数不超 15W字，单个文件大小不超 300M，文档里面图片最大为 30M，超出则进行压缩) </span>
              </a-upload>
            </a-form-item>
            <a-form-item label="文章内容" v-bind="validateInfos.content" v-if="addEditFrom.uploadMethod === 1">
              <a-textarea
                v-model:value="addEditFrom.content"
                placeholder="请输入文章内容"
                allow-clear
                class="upload-textarea"
                show-count
                :maxlength="6000"
                :auto-size="{ minRows: 8, maxRows: 10 }"
              />
            </a-form-item>
            <a-form-item label="文章图片" v-bind="validateInfos.planDesc">
              <a-upload
                v-model:file-list="articleFileList"
                :action="`/cop/audit/upload/image?replayTime=${new Date().getTime()}`"
                :before-upload="articleBeforeUpload"
                :headers="headers"
                :progress="progress"
                @change="articleSuccessUpload"
              >
                <a-button :disabled="articleUploading">
                  <upload-outlined></upload-outlined>
                  上传图片
                </a-button>
                <span class="upload-padding">支持上传格式：png、jpg、jpeg、gif、bmp、webp，图片大小不超过 10M</span>
              </a-upload>
            </a-form-item>
            <a-form-item label="文章音视频" v-bind="validateInfos.planDesc">
              <a-upload
                v-model:file-list="avFileList"
                :action="`/cop/audit/upload/av?replayTime=${new Date().getTime()}`"
                :before-upload="avBeforeUpload"
                :headers="headers"
                :progress="progress"
                @change="avSuccessUpload"
              >
                <a-button :disabled="avUploading">
                  <upload-outlined></upload-outlined>
                  上传音视频
                </a-button>
                <span class="upload-padding">支持上传格式：mp3、wav、ogg、mp4、webm、mov、flv、avi、rmvb，大小不超过1G</span>
              </a-upload>
            </a-form-item>
            <a-form-item label="视频简介" v-if="addEditFrom.uploadMethod === 2">
              <a-textarea
                v-model:value="addEditFrom.videoIntro"
                placeholder="请输入视频简介"
                allow-clear
                class="upload-textarea"
                show-count
                :maxlength="1000"
                :auto-size="{ minRows: 5, maxRows: 10 }"
              />
            </a-form-item>
            <a-form-item label="网盘地址" v-bind="validateInfos.cloudAddress">
              <a-input class="title-input" v-model:value="addEditFrom.cloudAddress" placeholder="网盘格式:网盘地址/网盘密码（推荐优先使用移动网盘）" />
            </a-form-item>
          </div>
        </div>
      </a-form>
      <div class="submit-box">
        <a-button class="save-btn" :disabled="articleUploading || avUploading || fileUploading" @click="addEditSubmit(true)">保存草稿</a-button>
        <a-button class="submit-btn" :disabled="articleUploading || avUploading || fileUploading" @click="addEditSubmit(false)">提交审核</a-button>
      </div>
    </a-spin>
  </a-drawer>
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { auditAdd, auditEdit, auditSubmitAdd, auditSubmitEdit, accountList } from "@/api/contentReview";
import emitter from "@/assets/js/emitter";
import { encryptionMobile } from "@/assets/js/utils";
const props = defineProps({
  addEditData: Object,
});
const progress = {
  strokeColor: {
    '0%': '#108ee9',
    '100%': '#87d068',
  },
  strokeWidth: 3,
  format: percent => `${parseFloat(percent.toFixed(2))}%`,
  class: 'test',
};
const modalLoading = ref(false);
const confirmLoading = ref(false);
const addEditFrom = props.addEditData.list;
const accounts = ref([
  {name: "微信公众号",value: '1', accountList:[]},
  {name: "小红书",value: '2', accountList:[]},
  {name: "微博",value: '3', accountList:[]},
  {name: "视频号",value: '4', accountList:[]},
  {name: "抖音",value: '5', accountList:[]},
  {name: "头条",value: '6', accountList:[]},
  {name: "快手",value: '7', accountList:[]}
])
const articleCategoryOpt = [
  {name:'图文类',id:1},
  {name:'音视频类',id:2}
]
//表单规则验证数据
const addEditRules = reactive({
  title: [
    {
      required: true,
      message: "请输入标题",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符",
      trigger: "change"
    }
  ],
  author: [
    {
      required: true,
      message: "请输入作者",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  fileList: [
    {
      required: addEditFrom.uploadMethod === 2,
      message: "请上传文档"
    }
  ],
  content: [
  {
      required: addEditFrom.uploadMethod === 1,
      message: "请输入文章内容",
    }
  ]
});
//切换内容上传方式时验证表单项
function switchupLoadMethod() {
  addEditRules
  console.log(addEditFrom.uploadMethod)
  if(addEditFrom.uploadMethod === 2) {
    addEditRules.fileList[0].required = true;
    addEditRules.content[0].required = false;
  } else {
    addEditRules.fileList[0].required = false;
    addEditRules.content[0].required = true;
  }
}
//文章图片上传
const articleFileList = ref([]);
const articleUploading = ref(false);
//编辑时把文件push到该组件上
if(addEditFrom.iamgeList) {
  addEditFrom.iamgeList.forEach((el,i) => {
    articleFileList.value.push({
      uid: i,
      status: 'done',
      name: el.auditFileName,
      url: el.auditFileUrl
    })
  });
}
function isFilenameExist(fileList,filename) {
  return fileList.value.filter(e => e.name === filename).length > 0
}
// 上传前判断格式
const articleBeforeUpload = (file,flist) => {
  return new Promise((resolve)=>{
    if(file.name.indexOf(' ') > -1) {
      message.error("文件名称不能包含空格");
      return false;
    }
    if(file.name.split('.')[0].length > 50) {
      message.error("上传资源名称不能超过50个字符！");
      return false;
    }
    if (isFilenameExist(articleFileList,file.name)) {
      message.error("文件名称不能重复！");   //此处可换成ElMessage.error('文件已存在')
      return false;
    }
    const imgFormat = ['image/jpeg','image/jpg','image/png','image/gif','image/bmp']
    const isJpgOrPng = imgFormat.includes(file.type);
    if (!isJpgOrPng) {
      message.error("上传格式有误！");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 10;
    if (!isLt2M) {
      message.error("图片大小不能超过10M！");
      return false;
    }
    articleFileList.value = [...(articleFileList.value || []), file];
    resolve(true);
    return true;
  });
};
const articleSuccessUpload = info => {
  if (info.file.status === 'uploading') {
    articleUploading.value = true;
  }
  if (info.file.status === 'done') {
    if(JSON.parse(info.file.xhr.response).retCode !== '000000') {
      message.error(JSON.parse(info.file.xhr.response).retMsg);
    }
    articleUploading.value = false;
  } else if (info.file.status === 'error') {
    articleUploading.value = false;
    message.error('服务器繁忙，请稍后再试')
  }
};
const headers = {
  authorization: 'authorization-text',
};
//文章音视频上传
const avFileList = ref([]);
const avUploading = ref(false);
//编辑时把文件push到该组件上
if(addEditFrom.avList) {
  addEditFrom.avList.forEach((el,i) => {
    avFileList.value.push({
      uid: i,
      status: 'done',
      name: el.auditFileName,
      url: el.auditFileUrl
    })
  });
}
// 上传前判断格式
const avBeforeUpload = (file) => {
  return new Promise((resolve)=>{
    if(file.name.indexOf(' ') > -1) {
      message.error("文件名称不能包含空格");
      return false;
    }
    if(file.name.split('.')[0].length > 50) {
      message.error("上传资源名称不能超过50个字符！");
      return false;
    }
    if (isFilenameExist(avFileList,file.name)) {
      message.error("文件名称不能重复！");   //此处可换成ElMessage.error('文件已存在')
      return false;
    }
    const imgFormat = ['audio/mpeg','audio/mp3','audio/wav','audio/ogg','video/mp4','video/webm','video/mov','video/flv','video/avi','video/rmvb']
    const isJpgOrPng = imgFormat.includes(file.type);
    if (!isJpgOrPng) {
      message.error("上传格式有误！");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 1000;
    if (!isLt2M) {
      message.error("资源大小不能超过1G！");
      return false;
    }
    avFileList.value = [...(avFileList.value || []), file];
    resolve(true);
    return true;
  })
};
const avSuccessUpload = info => {
  if (info.file.status === 'uploading') {
    avUploading.value = true;
  }
  if (info.file.status === 'done') {
    if(JSON.parse(info.file.xhr.response).retCode !== '000000') {
      message.error(JSON.parse(info.file.xhr.response).retMsg);
    }
    avUploading.value = false;
  } else if (info.file.status === 'error') {
    message.error('服务器繁忙，请稍后再试');
    avUploading.value = false;
  }
};
//文件上传
const fileDataList = ref([]);
const fileUploading = ref(false);
//编辑时把文件push到该组件上
if(addEditFrom.fileList) {
  addEditFrom.fileList.forEach((el,i) => {
    fileDataList.value.push({
      uid: i,
      status: 'done',
      name: el.auditFileName,
      url: el.auditFileUrl
    })
  });
}
// 上传前判断格式
const fileBeforeUpload = (file) => {
  return new Promise((resolve)=>{
    if(file.name.indexOf(' ') > -1) {
      message.error("文件名称不能包含空格");
      return false;
    }
    if(file.name.split('.')[0].length > 50) {
      message.error("文件名称不可大于50字符");
      return false;
    }
    if (isFilenameExist(fileDataList,file.name)) {
      message.error("文件名称不能重复！");   //此处可换成ElMessage.error('文件已存在')
      return false;
    }
    const imgFormat = ['application/wps-office.docx','application/wps-office.docx','application/wps-office.ppt','text/plain','application/wps-office.xls','application/vnd.openxmlformats-officedocument.wordprocessingml.document','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet','application/pdf','application/vnd.openxmlformats-officedocument.presentationml.presentation','text/vtt']
    const isJpgOrPng = imgFormat.includes(file.type);
    if (!isJpgOrPng) {
      message.error("上传格式有误！");
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 300;
    if (!isLt2M) {
      message.error("资源大小不能超过300M！");
      return false;
    }
    fileDataList.value = [...(fileDataList.value || []), file];
    resolve(true);
    return true;
  })
};
const fileSuccessUpload = info => {
  if (info.file.status === 'uploading') {
    fileUploading.value = true;
  }
  if (info.file.status === 'done') {
    fileUploading.value = false;
    if(JSON.parse(info.file.xhr.response).retCode === '000000') {//上传成功后push到对应对象内，避免form表单必填校验
      getFileList(fileDataList.value,addEditFrom.fileList)
    } else {
      message.error(JSON.parse(info.file.xhr.response).retMsg);
    }
  } else if (info.file.status === 'error') {
    fileUploading.value = false;
    message.error('服务器繁忙，请稍后再试')
  }
};
//获取发布平台(初始化)
const getAccountList = async () => {
  confirmLoading.value = true;
  const res = await accountList();
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    for(let item in res.data) {
      if(accounts.value[item-1].value === item) {
        accounts.value[item-1].accountList = res.data[item]
      }
    }
  } else {
    message.error(res.retMsg);
  }
};
onMounted(() => {
  getAccountList();
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//上传的数据重组
const getFileList = (arr,newArr) =>{
  arr.map((item)=>{
    if(item.xhr) {
      newArr.push(JSON.parse(item.xhr.response).data.url)
    } else {
      newArr.push(item.url)
    }
  })
}
//新增/编辑数据
function addEditSubmit (isDraft=false){
  validate()
    .then(async () => {
      confirmLoading.value = modalLoading.value = true;
      let res = {};
      const fromData = { ...toRaw(addEditFrom)}; 
      let accountsIdresult = fromData.accountsId.filter(item => item.accounts.length > 0).map(arr => arr.accounts.join(','));
      if(accountsIdresult.length === 0) {
        confirmLoading.value = modalLoading.value = false;
        message.error("请至少选择一个平台");
        return false;
      }
      fromData.accountId = accountsIdresult.toString();
      fromData.author = encodeURI(encryptionMobile(fromData.author));
      if(articleFileList.value.length) {
        fromData.iamgeList = [];
        getFileList(articleFileList.value,fromData.iamgeList);
      }
      if(avFileList.value.length) {
        fromData.avList = [];
        getFileList(avFileList.value,fromData.avList)
      }
      if(fileDataList.value.length) {
        fromData.fileList = [];
        getFileList(fileDataList.value,fromData.fileList)
      }
      if(isDraft) {
        if (!addEditFrom.id) { //新增(草稿)
          res = await auditAdd(fromData);
        } else { //编辑(草稿)
          res = await auditEdit(fromData);
        }
      } else {
        if (!addEditFrom.id) { //新增并提交审核
          res = await auditSubmitAdd(fromData);
        } else { //编辑并提交审核
          res = await auditSubmitEdit(fromData);
        }
      }
      
      confirmLoading.value = modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditAudit", true);
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};

</script>
<style lang="less" scoped>
.drawer-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  align-items: center;
  .drawer-title {
    font-weight: bold;
    font-size: 14px;
    color: #1E293B;
  }
  .drawer-closa {
    font-size: 20px;
    color: #000;
    cursor: pointer;
  }
}
.add-edit-from {
  height: calc(100vh - 145px);
  overflow: auto;
}
.platform-title {
  font-size: 14px;
  color: #000;
  font-weight: 400;
  margin-left: 35px;
  margin-bottom: 20px;
  span {
    color: red;
  }
}
.account-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.upload-form {
  :deep(.ant-upload) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  :deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item-container) {
    overflow: initial;
  }
} 

.submit-box {
  width: 1040px;
  display: flex;
  padding: 20px;
  position: fixed;
  align-items: center;
  justify-content: space-evenly;
  right: 0;
  bottom: 0;
  background: #FFF;
  box-shadow: 0 0 10px 0 rgba(15,23,42,0.1);
  z-index: 2;
  .ant-btn {
    background: #FFFFFF;
    border-radius: 4px;
    padding: 6px 15px;
    height: auto;
    font-size: 14px;
  }
  .save-btn {
    color: #FE8308;
    border: 1px solid #FE8308;
  }
  .submit-btn {
    color: #fff;
    background: #FE8308;
    border: #FE8308;
  }
}
.back-form {
  width: 100%;
  padding: 20px 0;
  background: rgba(254, 131, 8, .05);
  padding-right: 20px;
  .title-form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .title-input {
    width: 420px;
  }
  .ant-input-affix-wrapper {
    background-color: transparent;
  }
  :deep(.upload-textarea) {
    width: 100%;
    .ant-input {
      width: 100%;
    }
  }
  .upload-form {
    .ant-input {
      width: 100%;
    }
  }
  .upload-padding {
    padding-left: 20px;
    font-size: 12px;
    color: #1E293B;
  }
  :deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item):hover {
    background: rgba(254, 131, 8, .1);
  }
  :deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item .ant-upload-list-item-actions):hover .anticon {
    color: rgba(0, 0, 0, 0.88);
  }
  :deep(.ant-upload-wrapper .ant-upload-list .ant-upload-list-item):hover .ant-upload-list-item-actions {
    background-color: transparent !important;
  }
}
.ant-drawer .upload-form :deep(.ant-btn-text):not(:disabled):hover {
  background-color: transparent;
  background-image: linear-gradient(90deg, transparent 0%, transparent 100%);
}
</style>
  