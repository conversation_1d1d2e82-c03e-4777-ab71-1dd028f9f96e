<!-- 账号总数分布 -->
<template>
  <a-spin :spinning="spinLoading">
    <div class="account-number" id="funnelBox"></div>
  </a-spin>
</template>

<script setup>
import * as echarts from "echarts/core";
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  LegendComponent,
} from "echarts/components";
import { FunnelChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import emitter from "@/assets/js/emitter";
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  LegendComponent,
  FunnelChart,
  CanvasRenderer,
]);
const spinLoading = ref(true)
//数据传值
emitter.on("initChart", (pageData) => {
  let realdata = reactive([
    // { value: pageData.countyAccountCount || 0, name: "区级账号数量" },
    // { value: pageData.cityAccountCount || 0, name: "市级账号数" },
    // { value: pageData.provinceAccountCount || 0, name: "省级账号数" }
    { value: 45, name: "账号接入数" },
    { value: 17, name: "地市接入数" },
    { value: 8, name: "省专接入数" }
  ]);
  let length = realdata.length;
  let cdata = [];
  let tdata = [];
  realdata.forEach((obj, index) => {
    cdata.push({
      name: obj.name,
      value: 20 * (length - index),
    });
    tdata.push({
      name: obj.name,
      value: 10 * (length - index),
    });
  });
  const setFunnel = () => {
    let myChart = echarts.init(document.getElementById("funnelBox"));
    myChart.setOption({
      tooltip: {
        trigger: "item",
        formatter: function(params) {
            return `${params.name}：${realdata[params.dataIndex].value == null ? '--' : realdata[params.dataIndex].value}`
        },
      },
      legend: {
        bottom: 30,
        // data: ["区级账号数量", "市级账号数", "省级账号数"],
        data: ["账号接入数", "地市接入数", "省专接入数"]
      },
      series: [
        {
          name: "Funnel",
          type: "funnel",
          left: "center",
          bottom: 90,
          width: "90%",
          min: 0,
          max: 60,
          minSize: "0%",
          maxSize: "100%",
          sort: "ascending", //设置漏斗图方向，
          //'ascending-正三角'，'descending-倒三角（默认）'，'none'（表示按 data 顺序）
          gap: 30, // 间隙
          label: {
            show: true,
            normal: {
              position: "inside",
              verticalAlign: "top",
              formatter: function (params) {
                var result;
                if (realdata[params.dataIndex].value == null) {
                  result = params.name + "--";
                } else {
                  result = realdata[params.dataIndex].value;
                }
                return result;
              },
              textStyle: {
                color: "#fff",
              },
            },
          },
          labelLine: {
            // label在外时生效
            length: 10,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
          itemStyle: {
            color: function (params) {
              // 定义渐变色从左(#ff6f3d)到右(#ff6f3d)的渐变
              var colorList = [
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#CCE4FF" },
                  { offset: 1, color: "#93C5FD" },
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#8AF5E4" },
                  { offset: 1, color: "#44DEC5 " },
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#FFE4E6" },
                  { offset: 1, color: "#FDA7B2" },
                ]),
              ];
              return colorList[params.dataIndex];
            },
          },
          emphasis: {
            label: {
              fontSize: 24,
            },
          },
          data: cdata,
        },
      ],
    });
  };
  nextTick(() => {
    setFunnel();
    spinLoading.value = false
  });
});
</script>
<style lang="less" scoped>
.account-number {
  height: 420px;
}
</style>
