import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue';
// 删除列表数据
export default function useDel(url,id,loading,getList,content = '您确定删除此条数据吗？') {
    Modal.confirm({
        title: '系统提示',
        icon: createVNode(ExclamationCircleOutlined),
        content,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
            loading.value = true
            const res = await url({...id});
            loading.value = false;
            getList(res)
        }
    });
}