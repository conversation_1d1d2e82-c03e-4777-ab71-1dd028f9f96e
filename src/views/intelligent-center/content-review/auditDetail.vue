<!-- 内容审核详情 -->
<template>
  <div class="audit-detail">
      <div class="detail-box">
        <h3>审核详情页</h3>
        <a-spin :spinning="detailLoading">
          <div class="detail-content">
            <p>
              <span class="detail-label">文章标题：</span>
              <span class="detail-pl">{{ detailItem.title }}</span>
            </p>
            <p>文章类型：{{ detailItem.articleCategory==1?'图文类':'音视频类' }}</p>
            <p>作者：{{ detailItem.author }}</p>
            <p>提交时间：{{ detailItem.submitTime }}</p>
            <p>
              <span class="detail-label">发布平台：</span>
              <span class="detail-pl">{{ detailItem.platform }}</span>
            </p>
          </div>
          <h3>审核内容</h3>
          <div class="detail-content">
            <p>
              <span class="detail-label">文章正文：</span>
              <span class="detail-pl" v-if="detailItem.uploadMethod === 1">{{ detailItem.content }}</span>
              <span class="detail-pl" v-else>
                <a :href="item.auditFileUrl" target="_blank" v-for="item in detailItem.fileList" :key="item.auditFileName">
                  {{ item.auditFileUrl }}
                </a>
              </span>
            </p>
            <p>
              <span class="detail-label">文章图片：</span>
              <span class="detail-pl">
                <template v-if="detailItem.iamgeList && detailItem.iamgeList.length">
                  <a-image
                    v-for="item in detailItem.iamgeList" :key="item.auditFileName"
                    :width="80"
                    :src="item.auditFileUrl"
                  />
                </template>
                <template v-else>无</template>
              </span>
            </p>
            <p>
              <span class="detail-label">文章视频：</span>
              <span class="detail-pl">
                <template v-if="detailItem.avList && detailItem.avList.length">
                  <a :href="item.auditFileUrl" target="_blank" v-for="item in detailItem.avList" :key="item.auditFileName">
                    {{ item.auditFileName }}
                  </a>
                </template>
                <template v-else>无</template>
              </span>
            </p>
            <p>
              <span class="detail-label">网盘地址：</span>
              <span class="detail-pl">{{ detailItem.cloudAddress || '无' }}</span>
            </p>
          </div>
        </a-spin>
        
      </div>
    <a-spin :spinning="auditLoading">
      <div class="audit-record">
        <h3 class="rec-title">审核记录</h3>
        <div class="audit-content">
          <div class="audit-reject-box" v-for="(item,index) in detailItemRec" :key="index">
            <div class="audit-reject-title">
              <span class="audit-dot" :class="{'audit-reject':index === 0}"></span>
              <h3>审核结果</h3>
            </div>
            <div class="audit-reject-constent">
              <div class="audit-reject-list" 
                v-for="(list,i) in item.resultList"
                :key="list.deliveryTime"
                :class="{'audit-reject':i === 0 && index === 0}">
                <div class="details-list" >
                  <p>审核时间：{{ list.deliveryTime }}</p>
                  <p>审核状态：{{ list.statusFlag === 1?'通过':'驳回' }}</p>
                  <div class="reject-list" 
                  v-for="(rec,index) in list.manualList" 
                  :key="rec.auditSuggestion">
                    <p>问题{{ index+1 }}</p>
                    <p>
                      <span class="detail-label">问题描述：</span>
                      <span class="detail-pl">{{ rec.auditSuggestion }}</span>
                    </p>
                    <p>
                      <span class="detail-label">问题截图：</span>
                      <span class="detail-pl" v-if="!rec.screenshot">无</span>
                      <a-image
                        v-for="recImg in rec.screenshot" :key="recImg"
                        :width="60"
                        :src="recImg"
                      /></p>
                  </div>
                </div>
              </div>
            </div>
          </div> 
        </div>
        <div class="audit-btn">
          <a-button type="primary" class="edit-btn" @click="editItem()" :disabled="props.detailData.item.statusFlag === 1 || props.detailData.item.statusFlag === 3">重新编辑</a-button>
          <a-button type="primary" class="back-page" @click="props.detailData.modalOpen= false">返回列表</a-button>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script setup>
import { message } from "ant-design-vue";
import { auditDetail, auditDetailRec } from "@/api/contentReview";
import emitter from '@/assets/js/emitter';
const props = defineProps({
  detailData: Object
});
const detailLoading = ref(false);
const auditLoading = ref(false);
const detailItem = ref({})
//审核详情
const getDetsil = async (item) => {
  detailLoading.value = true;
  const res = await auditDetail({id:props.detailData.id});
  detailLoading.value = false;
  if (res.retCode === "000000") {
    detailItem.value = res.data;
    detailItem.value.platform = `${detailItem.value.weChatAccountName?detailItem.value.weChatAccountName.join(','):''}${detailItem.value.weiboAccountName?detailItem.value.weiboAccountName.join(','):''}${detailItem.value.redNoteAccountName?detailItem.value.redNoteAccountName.join(','):''}${detailItem.value.touTiAoAccountName?detailItem.value.touTiAoAccountName.join(','):''}${detailItem.value.tikTokAccountName?detailItem.value.tikTokAccountName.join(','):''}${detailItem.value.videoAccountName?detailItem.value.videoAccountName.join(','):''}${detailItem.value.kuAiShouAccountName?detailItem.value.kuAiShouAccountName.join(','):''}`
  }else {
    message.error(res.retMsg);
  }
}
//审核记录
const detailItemRec = ref([])
const getauditRec = async (item) => {
  auditLoading.value = true;
  const res = await auditDetailRec({id:props.detailData.id});
  auditLoading.value = false;
  if (res.retCode === "000000") {
    detailItemRec.value = res.data;
  }else {
    message.error(res.retMsg);
  }
}
//重新编辑
const editItem = () => {
  emitter.emit("anewEditItem", props.detailData.item);
}
onMounted(() => {
  getDetsil();
  getauditRec();
});
</script>
<style lang="less" scoped>
.audit-detail {
  width: 100%;
  height: calc(100vh - 110px);
  background: #fff;
  padding: 25px;
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 8;
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  border-radius: 4px;
  h3{
    font-size: 14px;
    color: #1E293B;
  }
  .detail-box {
    flex: 1;
    font-size: 14px;
    color: #1E293B;
    overflow-y: auto;
  }
  .detail-content {
    padding: 30px 25px;
    p {
      display: flex;
      margin-bottom: 15px;
    }
    .detail-label {
      width: 70px;
      display: inline-block;
    }
    .detail-pl {
      flex: 1;
      line-height: 14px;
      text-align: justify;
      white-space: pre-wrap;
    }
    :deep(.ant-image) {
      margin-right: 20px;
      border: 1px solid #979797;
    }
  }
  .audit-record {
    width: 420px;
    background: rgba(254, 131, 8, .05);
    padding: 20px;
    position: relative;
    .rec-title {
      margin-bottom: 30px;
    }
  }
  .audit-content {
    height: calc(100vh - 242px);
    overflow-y: scroll;
  }
  .audit-reject-box {
    display: flex;
    font-size: 14px;
    color: #1E293B;
    flex-direction: column;
    .audit-reject-list {
      filter: grayscale(100%);
      opacity: .5;
      display: flex;
      margin-bottom: 30px;
    }
    
    p {
      margin-bottom: 8px;
    }
    .details-list {
      margin-left: 20px;
      filter: grayscale(100%);
    }
    :deep(.ant-image) {
      margin-right: 20px;
      border: 1px solid #979797;
    }
    .reject-list {
      padding-top: 20px;
    }
    .audit-reject-title {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      h3 {
        margin-left: 5px;
      }
    }
    .audit-dot {
      width: 15px;
      height: 15px;
      border-radius: 15px;
      display: inline-block;
      filter: grayscale(100%);
      opacity: .5;
      background-color: #5FAF17;
    }
    .audit-reject {
      filter: none;
      opacity: 1;
    }
  }
  .audit-btn {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    position: absolute;
    left: 0;
    bottom: 0;
    background: #fff;
    padding: 20px;  
    padding-bottom: 0;
    .edit-btn {
      background: #FFFFFF;
      border: 1px solid #FE8308;
      font-size: 14px;
      color: #FE8308;
    }
    .ant-btn-primary:disabled {
      opacity: .5;
    }
    .back-page {
      background: #FFFFFF;
      border: 1px solid #D9D9D9;
      font-size: 14px;
      color: #475569;
    }
  }
}
</style>
