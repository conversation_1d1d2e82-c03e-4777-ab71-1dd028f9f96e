<!-- 账号总览 -->
<template>
  <div class="account-overview">
    <topBox></topBox>
    <div class="bottom-box">
      <div class="account-number">
        <accountNumber></accountNumber>
      </div>
      <div class="public-account-top">
        <div class="title-text">
          公众号粉丝数TOP10
          <img src="@/assets/image/wechat-img.png" class="network-img" alt="" />
        </div>
        <a-empty v-if="rankWeChatEmpty" />
        <a-spin v-else :spinning="rankWeChatData.length === 0">
          <div class="echarts-bar-box">
            <div
              class="echarts-bar"
              v-for="item in rankWeChatData"
              :key="item.sortId"
            >
              <div class="echarts-bar-name">
                {{ item.name }}</div>
              <div class="echarts-bar-con">
                <a-tooltip placement="top" v-if="item.sortId === 1" color="#FF8986">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                      trailColor="#F6F7F8"
                      :stroke-color="{
                        '0%': '#F7C598',
                        '100%': '#FF8986',
                      }"
                      :strokeWidth="6.5"
                      :percent="item.percentage"
                      :show-info="false"
                    />
                </a-tooltip>
                <a-tooltip placement="top" v-else-if="item.sortId === 2" color="#44DEC5">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                    trailColor="#F6F7F8"
                    :stroke-color="{
                      '0%': '#8AF5E4',
                      '100%': '#44DEC5',
                    }"
                    :strokeWidth="6.5"
                    :percent="item.percentage"
                    :show-info="false"
                  />
                </a-tooltip>
                <a-tooltip placement="top" v-else-if="item.sortId === 3" color="#39DA8A">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                  trailColor="#F6F7F8"
                  :stroke-color="{
                    '0%': '#AEFFD7',
                    '100%': '#39DA8A',
                  }"
                  :strokeWidth="6.5"
                  :percent="item.percentage"
                  :show-info="false"
                  />
                </a-tooltip>
                <a-tooltip placement="top" v-else color="#93C5FD">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                  trailColor="#F6F7F8"
                  :stroke-color="{
                    '0%': '#CCE4FF',
                    '100%': '#93C5FD',
                  }"
                  :strokeWidth="6.5"
                  :percent="item.percentage"
                  :show-info="false"
                />
                </a-tooltip>
              </div>
              <span class="echarts-bar-ran">{{ item.sortId }}</span>
            </div>
          </div>
        </a-spin>
      </div>
      <div class="dy-account-top">
        <div class="title-text">
          抖音号粉丝排行榜
          <img src="@/assets/image/tiktok-img.png" class="network-img" alt="" />
        </div>
        <a-empty v-if="rankTikTokEmpty" />
        <a-spin v-else :spinning="rankTikTokData.length === 0">
          <div class="echarts-bar-box">
            <div
              class="echarts-bar"
              v-for="item in rankTikTokData"
              :key="item.sortId"
            >
              <div class="echarts-bar-name">{{ item.name }}</div>
              <div class="echarts-bar-con">
                <a-tooltip placement="top" v-if="item.sortId === 1" color="#FF8986">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                      trailColor="#F6F7F8"
                      :stroke-color="{
                        '0%': '#F7C598',
                        '100%': '#FF8986',
                      }"
                      :strokeWidth="6.5"
                      :percent="(item.fansCount / (item.fansCount + 10000)) * 100"
                      :show-info="false"
                    />
                </a-tooltip>
                <a-tooltip placement="top" v-else-if="item.sortId === 2" color="#44DEC5">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                    trailColor="#F6F7F8"
                    :stroke-color="{
                      '0%': '#8AF5E4',
                      '100%': '#44DEC5',
                    }"
                    :strokeWidth="6.5"
                    :percent="(item.fansCount / (item.fansCount + 10000)) * 100"
                    :show-info="false"
                  />
                </a-tooltip>
                <a-tooltip placement="top" v-else-if="item.sortId === 3" color="#39DA8A">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                  trailColor="#F6F7F8"
                  :stroke-color="{
                    '0%': '#AEFFD7',
                    '100%': '#39DA8A',
                  }"
                  :strokeWidth="6.5"
                  :percent="(item.fansCount / (item.fansCount + 10000)) * 100"
                  :show-info="false"
                  />
                </a-tooltip>
                <a-tooltip placement="top" v-else color="#93C5FD">
                  <template #title>
                    {{ item.name }}：{{ item.fansCount }}
                  </template>
                  <a-progress
                  trailColor="#F6F7F8"
                  :stroke-color="{
                    '0%': '#CCE4FF',
                    '100%': '#93C5FD',
                  }"
                  :strokeWidth="6.5"
                  :percent="(item.fansCount / (item.fansCount + 10000)) * 100"
                  :show-info="false"
                />
                </a-tooltip>
              </div>
              <span class="echarts-bar-ran">{{ item.sortId }}</span>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>
<script setup>
import topBox from "./account-overview/topBox.vue";
import accountNumber from "./account-overview/accountNumber.vue";
import { rankWeChat, rankTikTok } from "@/api/accountOverview";
import { message } from 'ant-design-vue';
//公众号粉丝数top10
const rankWeChatData = ref([]);
const rankWeChatEmpty = ref(false);
const getRankWeChat = async () => {
  const res = await rankWeChat();
  if (res.retCode === "000000") {
    if(res.data.length === 0) {
      rankWeChatEmpty.value = true;
      return false
    }
    //取一个中间值来作为百分比计算
    let averageCount = 0;
    if(res.data.length>1){
      averageCount = res.data[0].fansCount+res.data[Math.round(res.data.length/2)].fansCount/2
    } else {
      averageCount = res.data[0].fansCount
    }
    res.data.forEach(val => {
      //最终以第一名加上中位名次的和除以2来作为百分比计算
      let percentage = val.fansCount / averageCount * 100
      val.percentage = Math.floor(percentage * 10) / 10;
    });
    rankWeChatData.value = res.data;
  } else {
    message.error(res.retMsg);
  }
}; 
//抖音粉丝数排行
const rankTikTokData = ref([]);
const rankTikTokEmpty = ref(false);
const getRankTikTok = async () => {
  const res = await rankTikTok();
  if (res.retCode === "000000") {
    if(res.data.length === 0) {
      rankTikTokEmpty.value = true;
      return false
    }
    //取一个中间值来作为百分比计算
    let averageCount = 0;
    if(res.data.length>1){
      averageCount = res.data[0].fansCount+res.data[Math.round(res.data.length/2)].fansCount/2
    } else {
      averageCount = res.data[0].fansCount
    }
    res.data.forEach(val => {
      //最终以第一名加上中位名次的和除以2来作为百分比计算
      let percentage = val.fansCount / averageCount * 100
      val.percentage = Math.floor(percentage * 10) / 10;
    });
    rankTikTokData.value = res.data;
  } else {
    message.error(res.retMsg);
  }
};
onMounted(() => {
  getRankWeChat();
  getRankTikTok(); 
  // emitter.emit("getseleId", '');
});
</script>
<style lang="less" scoped>
.account-overview {
  height: calc(100vh - 130px);
  overflow-y: scroll;
  .title-text {
    font-size: 16px;
    color: #1e293b;
    border-bottom: 1px solid #f1f5f9;
    padding: 15px;
  }
  .bottom-box {
    height: 420px;
    display: flex;
    justify-content: space-between;
  }
  .account-number {
    width: 365px;
  }
  .account-number,
  .public-account-top,
  .dy-account-top {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
    border-radius: 2px;
    .network-img {
      width: 16px;
      position: relative;
      left: 2px;
      top: 2px;
    }
  }
  .public-account-top,
  .dy-account-top {
    flex: 1;
    // width: 350px;
    margin-left: 8px;
  }
  .echarts-bar {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .echarts-bar-name {
    width: 90px;
    font-size: 14px;
    color: #64748b;
    text-align: right;
    padding-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .echarts-bar-con {
    flex: 1;
    position: relative;
    top: -1px;
    cursor: pointer;
    .ant-progress-line {
      margin-bottom: 0;
    }
    :deep(.ant-progress-inner) {
      overflow: inherit;
    }
    :deep(.ant-progress-bg) {
      // height: 20px !important;
      // box-shadow: 0 1px 4px 0 rgba(52, 212, 246, 0.6);
      // background-color: transparent;
    }
  }
  .echarts-bar-ran {
    width: 30px;
    display: inline-block;
    font-size: 14px;
    color: #64748b;
    text-align: center;
  }
  .ant-empty {
    padding: 120px 0;
  }
  .echarts-bar-box {
    height: 370px;
    padding: 12px;
  }
}
.ant-empty {
  padding: 160px 0;
}
.top-operate {
  position: absolute;
  right: 10px;
  top: 0px;
  .ant-select {
    width: 180px;
  }
}
</style>
