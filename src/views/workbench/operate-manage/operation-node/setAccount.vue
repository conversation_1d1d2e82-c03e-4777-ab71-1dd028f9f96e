<!-- 账号授权 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.setData.modalOpen"
    title="适用账号"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="setAccSubmit"
  >
    <a-form>
      <a-form-item label="适用账号" v-bind="validateInfos.accountId">
        <a-select
          mode="multiple"
          v-model:value="addEditFrom.accountId"
          :options="props.setData.deptListDatas"
          :field-names="{ label: 'name', value: 'id' }"
          :max-tag-count="1"
          placeholder="请选择运营账号"
          :filter-option="filterOption"
          allow-clear
        >
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template> 

<script setup>
import { message, Form } from "ant-design-vue";
import { relSave } from "@/api/operationNode";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  setData: Object,
});
//账号树形控件
const addEditFrom = reactive({
  accountId: []
});
const filterOption = (input, option) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
addEditFrom.accountId = props.setData.accountId;
//表单规则验证数据
const addEditRules = reactive({
  accountId: [
    {
      required: true,
      trigger: "change",
      message: "请选择运营账号",
    },
  ],
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//提交数据
const modalLoading = ref(false);
const setAccSubmit = async () => {
  validate()
    .then(async () => {
      const { accountId } = toRaw(addEditFrom);
      console.log(addEditFrom, accountId);
      const fromData = {
        nodeId: props.setData.nodeId,
        accountId: accountId.join(","),
      };
      const res = await relSave(fromData);
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditComplete", true);
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
