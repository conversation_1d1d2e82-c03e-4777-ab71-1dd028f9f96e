import $https from "@/assets/js/request"

// 列表
export const staffPage = (data) => {
    return $https.fetchGet('/cop/wx/back/staff/page',data)
}
// 编辑 
export const staffEdit = (data, chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/staff/edit?chkCurWx=' + chkCurWx, data)
}
// 新增
export const staffAdd = (data, chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/staff/add?chkCurWx=' + chkCurWx, data)
}
// 查询业务大类
export const bizList = (data) => {
  return $https.fetchGet('/cop/manage/tag/first/biz/list', data)
}
// 详情
export const staffDetail = (data) => {
  return $https.fetchGet('/cop/wx/back/staff/detail', data)
}
// 删除
export const staffDel = (data) => {
  return $https.fetchGet('/cop/wx/back/staff/delete',data)
}
