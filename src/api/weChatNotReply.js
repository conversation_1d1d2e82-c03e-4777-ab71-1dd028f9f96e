import $https from "@/assets/js/request"

// 列表
export const ukwList = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/ukw/list',data)
}
//触发类型
export const userSendMsgType = (data) => {
    return $https.fetchGet('/cop/wx/back/common/list/userSendMsgType',data)
}
// 编辑 
export const ukwEdit = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/ukw/edit?chkCurWx='+chkCurWx,data)
}
// 新增
export const ukwAdd = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/ukw/add?chkCurWx='+chkCurWx,data)
}
// 删除
export const ukwDel = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/ukw/del',data)
}