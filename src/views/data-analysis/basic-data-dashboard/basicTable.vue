<!-- 基础看板表格 -->
<!-- 账号列表 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-input
        v-model:value="searchForm.name"
        placeholder="按账号名搜索（模糊）"
      />
      <a-button type="primary" @click="searchFun" class="submit-btn">
        <template #icon>
          <SearchOutlined />
        </template>
        查询
      </a-button>
    </div>
    <div class="top-box">
      <!-- <a-button
        type="primary"
        :loading="exportLoading"
        @click="exportAccountExcle()"
        class="submit-btn"
      >
        <template #icon>
          <DownloadOutlined />
        </template>
        导出数据
      </a-button> -->
    </div>
  </div>
  <div class="basic-list">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      :loading="pageLoading"
      :scroll="{ x: 1400, y: 500, hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'contentCountSevenDayMom'">
          <span class="count-text" :class="{'count-mom':record.contentCountSevenDayMom < 0}">{{ record.contentCountSevenDayMom }}</span>
        </template>
        <template v-if="column.dataIndex === 'readCountSevenDayMom'">
          <span class="count-text" :class="{'count-mom':record.readCountSevenDayMom < 0}">{{ record.readCountSevenDayMom }}</span>
        </template>
        <template v-if="column.dataIndex === 'shareCountSevenDayMom'">
          <span class="count-text" :class="{'count-mom':record.shareCountSevenDayMom < 0}">{{ record.shareCountSevenDayMom }}</span>
        </template>
        <template v-if="column.dataIndex === 'readCountThisMonthMom'">
          <span class="count-text" :class="{'count-mom':record.readCountThisMonthMom < 0}">{{ record.readCountThisMonthMom }}</span>
        </template>
        <template v-if="column.dataIndex === 'shareCountThisMonthMom'">
          <span class="count-text" :class="{'count-mom':record.shareCountThisMonthMom < 0}">{{ record.shareCountThisMonthMom }}</span>
        </template>
      </template>
    </a-table>
  </div>
</template>
  <script setup>
import { message, notification } from "ant-design-vue";
import { boardPage, boardPageExport } from "@/api/basicDataDashboard";
import useGetList from "@/hooks/useGetList";
import https from "@/assets/js/request";
import emitter from "@/assets/js/emitter";
import useExportData from "@/hooks/useExportData";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "账号",
    dataIndex: "accountName",
    width: "120px",
    ellipsis: true,
    fixed: "left"
  },
  {
    title: "7日推文数",
    dataIndex: "contentCountSevenDay"
  },
  {
    title: "7日推文数环比",
    dataIndex: "contentCountSevenDayMom"
  },
  {
    title: "7日阅读量",
    dataIndex: "readCountSevenDay"
  },
  {
    title: "7日阅读量环比",
    dataIndex: "readCountSevenDayMom"
  },
  {
    title: "7日分享数",
    dataIndex: "shareCountSevenDay"
  },
  {
    title: "7日分享数环比",
    dataIndex: "shareCountSevenDayMom"
  },
  {
    title: "本月阅读量",
    dataIndex: "readCountThisMonth"
  },
  {
    title: "本月阅读量环比",
    dataIndex: "readCountThisMonthMom"
  },
  {
    title: "本月分享数",
    dataIndex: "shareCountThisMonth"
  },
  {
    title: "本月分享数环比",
    dataIndex: "shareCountThisMonthMom"
  }
]);
const pageLoading = ref(false);

//查询列表
const searchForm = ref({
  name: "",
  province: null
});
// 分页/筛选
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
// 获取列表数据
onMounted(() => {
  useGetList(boardPage, pageLoading, dataSource, pagination);
});
//搜索
const searchFun = () => {
  useGetList(boardPage, pageLoading, dataSource, pagination, {
    ...searchForm.value,
    pageNum: 1,
  });
};
// 数据导出
const exportLoading = ref(false);
const exportAccountExcle = () => {
  useExportData(boardPageExport,pageLoading,exportLoading,searchForm.value,weChatId)
};
</script>
  
  <style lang="less" scoped>
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10px 0;
  background: #fff;
  .search-box,
  .top-box {
    display: flex;
    .ant-select {
      margin-left: 15px;
    }
  }
  .top-box {
    .ant-btn {
      margin-left: 15px;
    }
  }
}
.basic-list {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 380px);
  .operate-box {
    span {
      color: #FE8308;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .count-text {
    color: #10B981;
  }
  .count-mom {
    color: #F59E0B;
  }
}
</style>
  
