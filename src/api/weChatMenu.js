import $https from "@/assets/js/request"

export const menuList = (data) => {
  return $https.fetchGet('/cop/wx/back/menu/list',data)
}
export const menuDetail = (data) => {
  return $https.fetchGet('/cop/wx/back/menu/detail',data)
}
export const syncMenu = (data) => {
  return $https.fetchGet('/cop/wx/back/menu/syncMenu',data)
}
export const menuDown = (data) => {
  return $https.fetchGet('/cop/wx/back/menu/info/sync/down/first',data)
}
export const menuDel = (data) => { 
  return $https.fetchGet('/cop/wx/back/menu/del',data)
}
export const menuType = (data) => {
  return $https.fetchGet('/cop/wx/back/common/list/menuType',data)
}
// 选中提交
export const menuEdit = (data,chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/menu/edit?chkCurWx='+chkCurWx,data,data)
}
export const menuAdd = (data,chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/menu/add?chkCurWx='+chkCurWx,data,data)
}
