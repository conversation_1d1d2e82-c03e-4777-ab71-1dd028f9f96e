<template>
  <a-spin :spinning="pageLoading">
    <a-layout class="index-page">
      <a-layout-header>
        <div class="header-left">
          <!-- logo平台 -->
          <div class="layout-logo-box" @click="linkIndex">
            <img src="../../assets/image/logo-img.png" alt="" />
            <span>内容集中化运营平台</span>
          </div>
          <!-- 当前省市位置 -->
          <div class="user-box">
            <div class="user-dept-tree">
              <img
                src="../../assets/image/icon-location.png"
                class="icon-location"
                alt=""
              />
              <span class="location-name">当前</span>
              <a-cascader
                class="user-cascader"
                :allowClear="false"
                v-model:value="institutionVal"
                :field-names="{ label: 'label', value: 'id' }"
                :options="userData?.deptTree"
                @change="changeInstitution"
              />
            </div>
          </div>
          <div class="header-menu">
            <a-menu
              v-model:selectedKeys="heaStedK"
              theme="dark"
              mode="horizontal"
            >
              <a-menu-item
                v-for="item in userData.menuTree"
                :key="item.id"
                @click="changMenu(item)"
                >{{ item.menuName }}</a-menu-item
              >
            </a-menu>
          </div>
        </div>
        <div class="header-right">
          <!-- 用户信息 -->
          <div class="header-box">{{ userData.mobile }}，您好</div>
          <!-- 用户头像 -->
          <div class="user-img-box">
            <img
              :src="editData.imgs[editData.img < 4 ? editData.img : 0].url"
              class="user-img"
            />
            <div class="user-edit">
              <EditOutlined class="edit-icon" @click="showModal" />
            </div>
          </div>
          <div class="layout-loginout-box" @click="loginOut">
            <PoweroffOutlined />
            <span>退出</span>
          </div>
        </div>
      </a-layout-header>
      <a-layout-content>
        <a-layout>
          <a-layout-sider
            v-model:collapsed="mneuCollapsed"
            collapsible
            class="layout-menu-sider"
            v-show="heaStedK[0] !== '1'"
            :trigger="null"
            collapsed-width="40"
          >
            <a-menu
            v-model:selectedKeys="leftMenuState.selectedKeys"
            @openChange="onOpenChange"
            :open-keys="leftMenuState.openKeys"
            mode="inline"
          >
          <template v-for="item in currenTmenus" :key="item.id">
            <a-sub-menu v-if="item.children" :key="item.id">
              <template #title>
                <span>
                  {{item.menuName}}
                </span>
              </template>
              <template #icon>
                <img class="menu-icon" :src="getAssetURL(item,true)" alt="" v-if="leftMenuState.openSelectedKeys === item.id">
                <img class="menu-icon" :src="getAssetURL(item)" alt="" v-else>
              </template>
              <template v-for="cItem in item.children" :key="cItem.id">
                <!-- 二级菜单 -->
                <a-sub-menu v-if="cItem.children" :key="cItem.id">
                  <template #title>
                    <span>{{ cItem.menuName }}</span>
                  </template>
                  <!-- 循环显示三级菜单项 -->
                    <a-menu-item v-for="j of cItem.children" :key="j.id" @click="onMenuFun(j)">
                      <span>{{ j.menuName }}</span>
                    </a-menu-item>
                </a-sub-menu>
                <!-- 如果没有三级菜单，则显示二级菜单项 -->
                <template v-else>
                  <a-menu-item
                    :key="cItem.id"
                    @click="onMenuFun(cItem)"
                  >
                    <span>{{ cItem.menuName }}</span>
                  </a-menu-item>
                </template>
              </template>
            </a-sub-menu>
            <a-menu-item :key="item.id" v-else @click="onMenuFun(item)">
              <template #icon >
                <img class="menu-icon" :src="getAssetURL(item,true)" alt="" v-if="leftMenuState.openSelectedKeys === item.id">
                <img class="menu-icon" :src="getAssetURL(item)" alt="" v-else>
              </template>
              <span>
                {{item.menuName}}
              </span>
            </a-menu-item>
          </template>
            </a-menu>

            <!-- <a-menu
              v-model:selectedKeys="leftMenuState.selectedKeys"
              :open-keys="leftMenuState.openKeys"
              @openChange="onOpenChange"
              mode="inline"
              :items="currenTmenus"
              @click="onMenuFun"
            >
            </a-menu> -->
            <div class="switch-off-box" v-if="mneuCollapsed">
              <img 
              @click="() => (mneuCollapsed = !mneuCollapsed)" 
              class="switch-off" src="../../assets/image/switch-off.png" alt="">
            </div>
            <div class="switch-on-box" v-else>
              <img @click="() => (mneuCollapsed = !mneuCollapsed)"
               class="switch-on" 
               src="../../assets/image/switch-on.png" alt="">
            </div>
          </a-layout-sider>
          <a-layout-content
            class="page-content"
            :class="{ 'index-page-content': heaStedK[0] === '1','page-margin': !mneuCollapsed}"
          >
            <!-- 面包屑 -->
            <div class="breadcrumb-box" v-show="heaStedK[0] !== '1'">
              <div v-for="item in breadcrumb" :key="item">
                <CaretUpOutlined />
                <span class="breadcrumb-name">{{ item }}</span>
              </div>
              <CaretUpOutlined />
            </div>
            <!-- 页面内容 -->
            <router-view></router-view>
            <indexContent v-if="heaStedK[0] === '1'" />
          </a-layout-content>
        </a-layout>
      </a-layout-content>
    </a-layout>
  </a-spin>
  <a-config-provider
    :theme="{
      token: {
        colorPrimary: '#FE8308',
        borderRadius: 4,
      },
    }"
  >
  </a-config-provider>
  <eidtImg v-if="editData.modalOpen" :editData="editData" />
</template>
<script setup>
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { createVNode } from "vue";
import { Modal, message } from "ant-design-vue";
import { logout, switchDept } from "@/api/index";
import indexContent from "./indexContent.vue";
import emitter from "@/assets/js/emitter";
import eidtImg from "./upload-img/eidtImg.vue";
import img1 from "@/assets/image/user-img-0.png";
import img2 from "@/assets/image/user-img-1.png";
import img3 from "@/assets/image/user-img-2.png";
import img4 from "@/assets/image/user-img-3.png";
const router = useRouter();
const route = useRoute();
const breadcrumb = ref([]);
const getAssetURL = (image, act = false) => {
  if (act) {
    return new URL(
      `../../assets/image/${image.icon}-activity.png`,
      import.meta.url
    ).href;
  } else {
    return new URL(`../../assets/image/${image.icon}.png`, import.meta.url)
      .href;
  }
};
function linkIndex () {
  if(route.path !== '/homePage') {
    breadcrumb.value = [];
    heaStedK.value = ["1"];
    router.push("/homePage");
  }
}
//菜单数据
const leftMenuState = reactive({
  rootSubmenuKeys: [], //当前左侧菜单数组的父级id
  openKeys: [], //当前展开的 SubMenu 菜单项 key 数组
  selectedKeys: [], //当前选中的菜单项 key 数组
  openSelectedKeys: null, //自定义打开状态的key
});
const heaStedK = ref(["1"]); //header导航选中的菜单项 key 数组
//点击菜单
const currenTmenus = ref([]); //当前左侧菜单数组
const changMenu = (item) => {
  if (!item.children) {
    router.push(item.address);
  } else {
    router.push(item.children[0].address);
  }
};
const mneuCollapsed = ref(true);
const toggleCollapsed = () => {
  mneuCollapsed = !mneuCollapsed.value;
  // leftMenuState.openKeys = mneuCollapsed ? [] : leftMenuState.preOpenKeys;
};
//用户数据
const userData = reactive(JSON.parse(localStorage.getItem("userData")));
//用户选择的机构
const institutionVal = ref([]);
//初始化用户机构
let unseInstitution = ref([]);
const pageLoading = ref(false);
//切换机构
const changeInstitution = (_value, selectedOptions) => {
  pageLoading.value = true;
  unseInstitution.value = selectedOptions.map((o) => o.id);
  //缓存部门数据
  localStorage.setItem(
    "unseInstitution",
    JSON.stringify(unseInstitution.value)
  );
  toggleInstitution();
};
// 使用递归函数遍历树形菜单结构，给菜单添加属性
function findNodeById(treeData, address) {
  // 遍历当前层级的所有节点
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    node.title = node.menuName;
    node.label = node.menuName;
    node.icon = node.address;
    if (!node.parentId || node.children.length === 0) {
      node.address = address
        ? address + "/" + node.address
        : "/" + node.address;
    } else {
      node.address = address;
    }
    node.key = node.id.toString();
    node.id = node.id.toString();
    if (node.children.length === 0) {
      node.children = null;
    }
    // 如果当前节点有子节点，则递归调用当前函数继续查找子节点
    if (node.children && node.children.length > 0) {
      findNodeById(node.children, node.address);
    }
  }
  return treeData;
}
//切换机构后刷新菜单
const toggleInstitution = async () => {
  const params = {
    province: institutionVal.value[0],
    city: institutionVal.value[1],
  };
  const res = await switchDept(params);
  pageLoading.value = false;
  if (res.retCode === "000000") {
    userData.menuTree = res.data;
    //重新缓存用户数据
    // localStorage.clear();
    userData.menuTree = findNodeById(res.data, null);
    localStorage.setItem("userData", JSON.stringify(userData));
    breadcrumb.value = [];
    heaStedK.value = ["1"];
    if (route.path === "/homePage") {
      router.go(0);
    } else {
      router.push("/homePage");
    }
  } else {
    message.error(res.retMsg);
  }
};
onBeforeMount(() => {
  //判断当前省市数据
  if (localStorage.getItem("unseInstitution")) {
    institutionVal.value = JSON.parse(localStorage.getItem("unseInstitution"));
  } else {
    institutionVal.value = [
      userData.deptTree[0].id,
      userData.deptTree[0].children[0].id,
    ];
  }
});
//切换左边菜单(展开/关闭的回调)
const onOpenChange = (openKeys) => {
  const latestOpenKey = openKeys.find(
    (key) => leftMenuState.openKeys.indexOf(key) === -1
  );
  if (leftMenuState.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
    leftMenuState.openKeys = openKeys;
  } else {
    leftMenuState.openKeys = latestOpenKey ? [latestOpenKey] : [];
  }
};
// 递归函数，用于查找某个节点的所有父节点
function getMenuData(tree, nodeUrl, getName, ancestors = []) {
  for (let node of tree) {
    if (node.address.indexOf(nodeUrl) > -1) {
      if (getName === "menuName") {
        ancestors.push(node[getName]);
      }
      if (getName === "id") {
        ancestors.push(node[getName]);
      }
      return ancestors;
    }
    if (node.children && node.children.length > 0) {
      const found = getMenuData(node.children, nodeUrl, getName, [
        ...ancestors,
        node[getName],
      ]);
      if (found) {
        return found;
      }
    }
  }
  return null;
}
// 监听路由更改菜单数据
watch(
  () => route.path,
  (newVal, oldVal) => {
    if (route.path !== "/homePage") {
      let menusId = getMenuData(userData.menuTree, newVal.split("/")[2], "id");
      breadcrumb.value = getMenuData(
        userData.menuTree,
        newVal.split("/")[2],
        "menuName"
      );
      heaStedK.value[0] = menusId[0];
      currenTmenus.value = getMenuData(
        userData.menuTree,
        newVal.split("/")[2],
        "children"
      )[0];
      leftMenuState.rootSubmenuKeys = [];
      currenTmenus.value.forEach((val) => {
        leftMenuState.rootSubmenuKeys.push(val.id);
      });
      leftMenuState.selectedKeys = [menusId[menusId.length - 1]];
      if (menusId.length > 1) {
        //大于2代表是二级菜单以上
        leftMenuState.openSelectedKeys = menusId[1];
      } else {
        leftMenuState.openSelectedKeys = null;
      }
    }
  },
  { immediate: true }
);
const noMenus = ["23", "28", "29", "33"];
const onMenuFun = (item) => {
  if (noMenus.includes(item.id)) {
    message.error("应用正在开发中....");
  } else {
    router.push(item.address);
  }
};

//头像编辑
const editData = reactive({
  modalOpen: false,
  img: userData?.headImg ? parseInt(userData?.headImg.split("/")[3]) : 0,
  imgs: [
    { url: img1, id: 0 },
    { url: img2, id: 1 },
    { url: img3, id: 2 },
    { url: img4, id: 3 },
  ],
});
const showModal = () => {
  editData.modalOpen = true;
};
emitter.on("editImg", (value) => {
  editData.modalOpen = false;
  editData.img = value;
  userData.headImg = `https://mcn.i139.cn/${value}`;
  localStorage.setItem("userData", JSON.stringify(userData));
});
//登出
const loginOut = () => {
  Modal.confirm({
    title: "系统提示",
    icon: createVNode(ExclamationCircleOutlined),
    content: "您确定要退出吗？",
    okText: "确认",
    okType: "danger",
    cancelText: "取消",
    async onOk() {
      const res = await logout();
      if (res.retCode === "000000") {
        router.push("/login");
      } else {
        message.error(res.retMsg);
      }
    },
  });
};
</script>
<style lang="less" scoped>
.ant-layout {
  height: 100vh;
  background: #fdfdfd;
  .page-content {
    padding: 10px 10px 0;
  }
  .index-page-content {
    padding: 0;
  }
  .page-margin {
    padding-left: 20px;
  }
  .ant-layout-header {
    height: auto;
    background: #fff;
    padding-left: 10px;
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    justify-content: space-between;
    padding: 0 20px 0 10px;
    position: relative;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.13);
    z-index: 1;
    .header-left,
    .header-right {
      display: flex;
      align-items: center;
    }
    .header-left {
      flex: 1;
    }
    .header-right {
      width: 175px;
      position: relative;
    }
    .header-box {
      font-weight: bold;
      border-radius: 2px;
      position: relative;
      overflow: hidden;
      font-size: 14px;
      padding-right: 5px;
      color: rgba(1, 23, 56, 0.5);
    }
    .title-img {
      width: 99px;
      position: absolute;
      right: 0;
      top: 16px;
    }
    .user-img-box {
      width: 40px;
      height: 40px;
      position: relative;
      border-radius: 7px;
      overflow: hidden;
    }
    .user-edit {
      width: 40px;
      height: 40px;
      background-color: rgb(0 0 0 / 25%);
      position: absolute;
      top: 0;
      left: 0;
      display: none;
    }
    .edit-icon {
      position: absolute;
      bottom: 3px;
      right: 3px;
      cursor: pointer;
    }
    .user-img-box:hover {
      .user-edit {
        display: block;
      }
    }
    .user-img {
      width: 40px;
      vertical-align: top;
    }
  }
  .header-menu {
    margin-left: 50px;
    .ant-menu-dark.ant-menu-horizontal > :deep(.ant-menu-item) {
      color: #011738;
      font-size: 16px;
      padding: 0;
      padding-right: 20px;
    }
    .ant-menu-dark.ant-menu-horizontal > :deep(.ant-menu-item-selected),
    .ant-menu-dark.ant-menu-horizontal > :deep(.ant-menu-submenu-selected) {
      color: #fe8308;
      background: #fff;
    }
  }
  .layout-logo-box {
    font-size: 16px;
    color: #011738;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-right: 50px;
    cursor: pointer;
    img {
      width: 40px;
    }
    span {
      margin-left: 8px;
    }
  }
  .user-box {
    font-size: 14px;
    color: #011738;

    .user-dept-tree {
      display: flex;
      align-items: center;
      position: relative;
    }
    .user-cascader {
      width: 200px;
    }
    .icon-location {
      width: 24px;
      vertical-align: top;
    }
    .location-name {
      font-size: 14px;
      color: rgba(1, 23, 56, 0.6);
      padding-right: 5px;
    }
  }
  .header-right:hover {
    .layout-loginout-box {
      display: flex;
    }
  }
  .layout-loginout-box {
    width: 100%;
    height: 40px;
    position: absolute;
    bottom: -40px;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #94a3b8;
    background: #fff;
    cursor: pointer;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.13);
    display: none;
    span {
      margin-left: 10px;
    }
    .anticon-poweroff {
      font-size: 18px;
    }
  }

  .layout-menu-sider {
    :deep(.ant-menu-title-content) {
      display: flex;
      align-items: center;
    }
    .menu-icon {
      width: 24px;
      margin-right: 10px;
    }
    .ant-menu {
      height: calc(100vh - 67px);
      overflow-y: auto;
      padding-top: 20px;
    }
    :deep(.ant-menu) {
      .ant-menu-item {
        width: 100%;
        margin-inline: 0;
        color: #011738;
        text-align: center;
        border-radius: 0;
      }
    }
    .switch-off-box {
      width: 16px;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      z-index: 9;
    }
    .switch-off,.switch-on {
      width: 100%;
      vertical-align: top;
      cursor: pointer;
    }
    .switch-on-box {
      width: 16px;
      position: absolute;
      right: -16px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  // .ant-layout-sider {
  //   flex: 0 0 200px !important;
  //   max-width: 200px !important;
  //   min-width: 200px;
  //   width: 200px !important;
  //   box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.13);
  // }
  :deep(.ant-layout-sider-children) {
    background: #fff;
  }
  :deep(.base-icon) {
    font-size: 20px;
    margin-right: 10px;
    cursor: pointer;
  }
  .ant-menu-dark {
    color: rgba(1, 23, 56, 0.5);
    background: #fff;
  }
  
  :deep(.ant-menu-inline) {
    background: #fff;
    .ant-menu-inline {
      background: #fff;
    }
    .ant-menu-item {
      width: 100%;
      margin-inline: 0;
      color: #011738;
    }
    .ant-menu-item-selected {
      background: #fff;
      color: #fe8308;
      background-color: rgba(254, 131, 8, 0.2);
    }
  }
  :deep(.ant-menu-dark.ant-menu-horizontal) > .ant-menu-item-selected::after {
    background: #fe8308;
    height: 4px;
    left: 0;
  }
  :deep(.ant-menu-inline.ant-menu-root) {
    .ant-menu-submenu-title {
      width: 100%;
      margin-inline: 0;
      color: #011738;
    }
  }
  
  :deep(.ant-menu-light) {
    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: #fe8308;
      width: 100%;
      margin-inline: 0;
    }
    .ant-menu-item::after {
      height: 30px;
      top: 5px;
      right: -1px;
      border: 2px solid #fe8308;
    }
    .ant-menu-item-selected {
      background-color: rgba(254, 131, 8, .2);
    }
  }
  :deep(.ant-menu-vertical) {
    .ant-menu-submenu-title {
      width: 100%;
      margin-inline: 0;
      border-radius: 0;
    }
  }
  :deep(.ant-menu-light:not(.ant-menu-horizontal)) {
    .ant-menu-item:not(.ant-menu-item-selected):hover {
      background-color: rgba(254, 131, 8, .2);
    }
  }
  :deep(.ant-menu-light:not(.ant-menu-horizontal)) {
    .ant-menu-submenu-title:hover {
      background-color: rgba(254, 131, 8, .2);
    }
  }
  .ant-menu-light.ant-menu-inline .ant-menu-item::after :deep(.ant-menu-dark) {
    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: rgba(1, 23, 56, 0.5);
      // background-color: rgba(254, 131, 8, .2);
    }
  }
  .breadcrumb-box {
    margin-bottom: 10px;
    display: flex;
    .breadcrumb-name {
      margin-right: 1px;
      color: rgba(1, 23, 56, 0.5);
    }
    .anticon-caret-up {
      transform: rotate(128deg);
      position: relative;
      top: 4px;
      color: rgba(1, 23, 56, 0.5);
    }
    .anticon-caret-up:last-child {
      color: rgba(1, 23, 56, 1);
    }
  }
  :deep(.ant-menu-inline-collapsed) {
    .ant-menu-item-icon {
      vertical-align: middle;
    }
  }
}
</style>
<style lang="less">
.ant-menu-light:not(.ant-menu-horizontal) {
  .ant-menu-submenu-title:hover {
    background-color: rgba(254, 131, 8, .2);
  }
}
.ant-menu-light:not(.ant-menu-horizontal) {
  .ant-menu-item:not(.ant-menu-item-selected):hover {
    background-color: rgba(254, 131, 8, .2);
  }
}
.ant-menu-submenu-placement-rightTop {
  .ant-menu-vertical > .ant-menu-item,
  .ant-menu-vertical >.ant-menu-submenu>.ant-menu-submenu-title {
    width: 100%;
    margin-inline: 0;
    border-radius: 0;
    margin: 0;
  }
}
</style>