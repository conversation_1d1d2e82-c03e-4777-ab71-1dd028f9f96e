<!-- 授权省份菜单 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.setData.modalOpen"
    title="授权菜单"
    okText="确定"
    :confirm-loading="modalLoading"
    @ok="setAccSubmit"
  >
    <a-tree
      v-model:checkedKeys="checkedKeys"
      checkable
      :fieldNames="{ children: 'children', title: 'label', key: 'id' }"
      :tree-data="props.setData.deptListDatas"
    >
    </a-tree>
  </a-modal>
</template> 
  
<script setup>
import { message } from "ant-design-vue";
import { menuAllSave } from "@/api/deptManage";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  setData: Object,
  accountId: Array,
});
//账号树形控件
//这两个值只接受ref的值
const checkedKeys = ref([]); //选中复选框
Object.assign(checkedKeys.value, props.accountId);
//筛选选中的值
function getOnlyLeafKeys(selectedKeys, treeData) {
  const onlyLeafKeys = [];
 
  function traverse(nodes) {
    nodes.forEach(node => {
      if (selectedKeys.includes(node.id) && (!node.children || node.children.length === 0)) {
        onlyLeafKeys.push(node.id);
      }
      if (node.children) {
        traverse(node.children);
      }
    });
  }
  traverse(treeData);
  return onlyLeafKeys;
}

//提交数据
const modalLoading = ref(false);
const setAccSubmit = async () => {
  const checkedIds = getOnlyLeafKeys(checkedKeys.value, props.setData.deptListDatas)
  if (!checkedIds.length) {
    message.error("请选择需要配置的账号");
    return;
  }
  modalLoading.value = true;
  const res = await menuAllSave({
    deptId: props.setData.id,
    menu: checkedIds.join(","),
  });
  modalLoading.value = false;
  if (res.retCode === "000000") {
    message.success("操作成功！");
    emitter.emit("setMenu", true);
    emitter.emit("setMenu", true);
  } else {
    message.error(res.retMsg);
  }
};
</script>
  