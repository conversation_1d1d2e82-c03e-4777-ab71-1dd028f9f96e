<!-- 账号列表编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.auditData.modalOpen"
    title="账号审核"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
    width="680px"
  >
    <a-spin :spinning="modalLoading">
      <a-form>
        <a-form-item label="账号名称" v-bind="validateInfos.name">
          <a-input
            v-model:value="addEditFrom.name"
            placeholder="请输入账号名称"
          />
        </a-form-item>
        <a-form-item label="平台" v-bind="validateInfos.platform">
          <a-select
            v-model:value="addEditFrom.platform"
            :options="platformOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择平台"
            @change="validateUsername"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="认证主体/状态" v-bind="validateInfos.mainBody">
          <a-input
            v-model:value="addEditFrom.mainBody"
            placeholder="请输入认证主体/状态"
          />
        </a-form-item>
        <a-form-item label="融媒体接入" v-bind="validateInfos.mcnFlag" v-if="addEditFrom.platform === 1">
          <a-select
            v-model:value="addEditFrom.mcnFlag"
            :options="mcnFlagOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择平台"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="公众号ID/微信号" v-bind="validateInfos.weChatId" v-if="addEditFrom.platform === 1">
          <a-input
            v-model:value="addEditFrom.weChatId"
            placeholder="请输入公众号ID/微信号"
          />
        </a-form-item>
        <a-form-item label="管理运营归口" v-bind="validateInfos.countyName">
          <a-cascader
            v-model:value="addEditFrom.countyName"
            :options="countyOpt"
            :field-names="{ label: 'label', value: 'id' }"
            placeholder="请选择管理运营归口"
            @change="selectCounty"
          />
        </a-form-item>
        <a-form-item label="运营管理员" v-bind="validateInfos.operateAdmin">
          <a-input
            v-model:value="addEditFrom.operateAdmin"
            placeholder="请输入运营管理员"
          />
        </a-form-item>
        <a-form-item label="运营人员" v-bind="validateInfos.operateUser">
          <a-select
            v-model:value="addEditFrom.operateUser"
            show-search
            placeholder="请选择运营人员"
            :options="operateUserOpt"
            :field-names="{ label: 'name', value: 'id' }"
            :filter-option="filterOption"
          ></a-select>
        </a-form-item>
        <a-form-item label="账号类型" v-bind="validateInfos.accountType">
          <a-select
            v-model:value="addEditFrom.accountType"
            placeholder="请选择账号类型"
          >
            <a-select-option :value="1">营销类</a-select-option>
            <a-select-option :value="2">综合类</a-select-option>
            <a-select-option :value="20">内宣类</a-select-option>
            <a-select-option :value="30">其他类</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="账号级别" v-bind="validateInfos.accountLevel">
          <a-select
            v-model:value="addEditFrom.accountLevel"
            placeholder="请选择账号级别"
          >
            <a-select-option :value="1">省级</a-select-option>
            <a-select-option :value="2">市级</a-select-option>
            <a-select-option :value="3">区级</a-select-option>
            <a-select-option :value="20">集团级</a-select-option>
            <a-select-option :value="30">其他级</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>
    
  <script setup>
import { message, Form } from "ant-design-vue";
import { auditAgree, deptList, allUser, prAll } from "@/api/auditAccount";
import emitter from "@/assets/js/emitter";
import { encryptionMobile } from "@/assets/js/utils";
const props = defineProps({
  auditData: Object,
});
const addEditFrom = props.auditData.item;
const validateUsername = () => {
  if (addEditFrom.platform === 1) {
    addEditRules.weChatId[0].required = true
    addEditRules.mcnFlag[0].required = true
  } else {
    addEditRules.weChatId[0].required = false
    addEditRules.mcnFlag[0].required = false
  }
};
const validateWxId = (_rule, value) => {
  if (value && !/^[^\u4e00-\u9fa5]+$/.test(value)) {
    return Promise.reject('公众号ID不能包含中文字符');
  } else {
    return Promise.resolve();
  }
};
const addEditRules = reactive({
  name: [
    {
      required: true,
      message: "请输入账号名称",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  platform: [
    {
      required: true,
      message: "请选择平台",
      trigger: "change"
    }
  ],
  mainBody: [
    {
      required: true,
      message: "请输入认证主体/状态",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  mcnFlag: [
    {
      required: true,
      message: "请选择融媒体接入",
      trigger: "change"
    },
  ],
  weChatId: [
    {
      required: false,
      message: "请输入公众号ID/微信号"
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change"
    },
    {
      validator: validateWxId,
      trigger: "change"
    }
  ],
  countyName: [
    {
      required: true,
      message: "请选择管理运营归口",
      trigger: "change"
    },
  ],
  operateAdmin: [
    {
      required: true,
      message: "请输入运营管理员",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  operateUser: [
    {
      required: true,
      message: "请选择运营人员",
      trigger: "change"
    },
  ],
  accountType: [
    {
      required: true,
      message: "请选择账号类型",
      trigger: "change"
    },
  ],
  accountLevel: [
    {
      required: true,
      message: "请选择账号级别",
      trigger: "change"
    },
  ],
});
const platformOpt = reactive([
  {name:"公众号",id:1},
  {name:"小红书",id:2},
  {name:"微博",id:3},
  {name:"视频号",id:4},
  {name:"抖音",id:5},
  {name:"头条",id:6},
  {name:"快手",id:7},
  {name:"其他",id:100}
]);
const mcnFlagOpt = ref([
  { id: 0, name: "否" },
  { id: 1, name: "是" },
]);
//管理运营归口下拉
const countyOpt = ref([]);
const getDeptList = async () => {
  const res = await deptList();
  if (res.retCode === "000000") {
    countyOpt.value = res.data;
  } else {
    message.error(res.retMsg);
  }
};

//管理运营归口选择数据处理
const selectCounty = (value) => {
  addEditFrom.province = value[0];
  addEditFrom.city = value[1];
};
//运营人员下拉
const operateUserOpt = ref();
const getAllUser = async () => {
  const res = await allUser();
  if (res.retCode === "000000") {
    operateUserOpt.value = res.data;
  } else {
    message.error(res.retMsg);
  }
};

const filterOption = (input, option) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
onMounted(()=>{
  getDeptList();
  getAllUser();
})
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//审核
const modalLoading = ref(false)
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true
      const fromData = {...toRaw(addEditFrom)};
      fromData.operateAdmin = encodeURI(
        encryptionMobile(fromData.operateAdmin)
      );
      if(fromData.platform !== 1) {
        delete fromData.weChatId
        delete fromData.mcnFlag
      }
      const res = await auditAgree(fromData);
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("audit", true);
      } else if (res.retCode === "990006") {
        message.error("已存在相同名称的账号");
      } else if (res.retCode === "990007") {
        message.error("已存在相同公众号ID的账号");
      } else if (res.retCode === "990008") {
        message.error("公众号ID错误");
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
    
  