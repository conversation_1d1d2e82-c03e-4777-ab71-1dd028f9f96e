<!-- 新媒体账号审核 -->
<template>
  <div class="account-list">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 1800, y: 500, hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'platform'">
          <span>{{ 
            { 
              1: '公众号', 
              2: '小红书', 
              3: '微博', 
              4: '视频号', 
              5: '抖音', 
              6: '头条', 
              7: '快手',
              100: '其他' 
            }[record.platform] || '-' 
            }}</span>
        </template>
        <template v-if="column.dataIndex === 'accountType'">
          <span>{{ 
            { 
              1: '营销类', 
              2: '综合类', 
              20: '内宣类', 
              30: '其他类'
            }[record.accountType] || '-' 
            }}</span>
        </template>
        <template v-if="column.dataIndex === 'mcnFlag'">
          {{ record.mcnFlag === 1 ? "是" : "否" }}
        </template>
        <template v-if="column.dataIndex === 'fansCount'">
          {{ record.fansCount || "-" }}
        </template>
        <template v-if="column.dataIndex === 'contentCount'">
          {{ record.contentCount || "-" }}
        </template>
        <template v-if="column.dataIndex === 'cityName'">
          {{ record.cityName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'auditStatus'">
          {{
            record.auditStatus == 1
              ? "待审核"
              : record.auditStatus == 2
              ? "已通过"
              : "未通过"
          }}
        </template>
        <template v-if="column.dataIndex === 'accountLevel'">
          <span>{{ 
            { 
              1: '省级', 
              2: '市级', 
              2: '区级', 
              20: '集团级', 
              30: '其他级'
            }[record.accountLevel] || '-' 
            }}</span>
        </template>
        <template v-if="column.dataIndex === 'updateFrequency'">
          <span v-if="record.updateFrequency === 1">7日内更新</span>
          <span v-else-if="record.updateFrequency === 2">15日内更新</span>
          <span v-else-if="record.updateFrequency === 3">30日内更新</span>
          <span v-else-if="record.updateFrequency === 4">超过30日未更新</span>
          <span v-else>-</span>
        </template>
        <template v-if="column.dataIndex === 'auditType'">
          <span v-if="record.auditType === 1">新增账号</span>
          <span v-else-if="record.auditType === 2">修改账号</span>
          <span v-else-if="record.auditType === 3">删除账号</span>
          <span v-else>-</span>
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span class="operate-btn" @click="showModal(record)">审核</span>
            <span class="del-btn" @click="delFun(record)">驳回</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <audit v-if="auditData.modalOpen" :auditData="auditData" />
</template>

<script setup>
import { message, notification } from "ant-design-vue";
import audit from "./audit-account/audit.vue";
import { auditPage, prAll, accountAudit } from "@/api/auditAccount";
import useGetList from "@/hooks/useGetList";
import emitter from "@/assets/js/emitter";
import useDel from "@/hooks/useDel";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "账号",
    dataIndex: "name",
    width: "120px",
    ellipsis: true,
    fixed: "left",
  },
  {
    title: "平台",
    dataIndex: "platform"
  },
  {
    title: "认证主体/状态",
    dataIndex: "mainBody",
    ellipsis: true
  },
  {
    title: "运营管理归口",
    dataIndex: "cityName"
  },
  {
    title: "运营管理员",
    dataIndex: "operateAdmin"
  },
  {
    title: "账号类型",
    dataIndex: "accountType"
  },
  {
    title: "账号级别",
    dataIndex: "accountLevel"
  },
  {
    title: "粉丝量",
    dataIndex: "fansCount"
  },
  {
    title: "在网内容（条）",
    dataIndex: "contentCount"
  },
  {
    title: "更新频率",
    dataIndex: "updateFrequency"
  },
  {
    title: "融媒体接入",
    dataIndex: "mcnFlag"
  },
  {
    title: "审核类型",
    dataIndex: "auditType"
  },
  {
    title: "审核状态",
    dataIndex: "auditStatus",
    width: "80px",
    fixed: "right"
  },
  {
    title: "操作",
    dataIndex: "operate",
    width: "120px",
    fixed: "right"
  },
]);
const pageLoading = ref(false);
// 分页/筛选
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
const handleTableChange = (pag, filters, sorter) => {
  useGetList(auditPage, pageLoading, dataSource, pagination, {
    pageNum: pag.current,
    pageSize: pag.pageSize
  });
};
// 获取列表数据
onMounted(() => {
  useGetList(auditPage, pageLoading, dataSource, pagination);
});
//审核数据
const auditData = reactive({
  modalOpen: false,
  item: {
    name: "",
    platform: null,
    mainBody: "",
    mcnFlag: null,
    weChatId: "",
    cityName: null,
    operateAdmin: "",
    operateUser: null,
    accountType: null,
    accountLevel: null,
  },
});
const showModal = (item = {}) => {
  if (item.auditStatus === 2 || item.auditStatus === 3) {
    message.error("已审核，请勿重复操作");
    return;
  } else {
    Object.assign(auditData.item, item);
  }
  auditData.modalOpen = true;
};
//审核后更新数据
emitter.on("audit", (value) => {
  if (value) {
    auditData.modalOpen = false;
    useGetList(auditPage, pageLoading, dataSource, pagination);
  }
});
//组件卸载后销毁emitter
onUnmounted(()=>{
  emitter.off('audit')
})
// 删除数据
const delFun = async (list) => {
  if (list.auditStatus === 2 || list.auditStatus === 3) {
    message.error("已审核，请勿重复操作");
    return;
  }
  useDel(
    accountAudit,
    { auditId: list.id },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        useGetList(auditPage, pageLoading, dataSource, pagination);
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    },
    "您确定驳回此条数据吗？"
  );
};
</script>

<style lang="less" scoped>
.top-operate {
  position: absolute;
  right: 10px;
  top: 0px;
}
.account-list {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 130px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  .operate-box {
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .operate-btn {
      color: #FE8308;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
</style>
