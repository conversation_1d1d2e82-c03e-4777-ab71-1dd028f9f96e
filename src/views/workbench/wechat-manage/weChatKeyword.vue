<!-- 关键字管理 -->
<template>
  <div class="top-operate">
    <div class="search-box">
        <a-input
          v-model:value="searchForm.keyword"
          placeholder="请输入关键字"
        />
        <a-button type="primary" 
          @click="searchFun" 
          class="search-btn">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
      <weChatConfig />
    </div>
    <a-button type="primary" @click="showModal()" class="submit-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增关键字
    </a-button>
  </div>
  <div class="user-content">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'adminFlag'">
          {{ record.adminFlag === 1 ? "是" : "否" }}
        </template>
        <template v-if="column.dataIndex === 'msgTypeName'">
          {{ record.msgTypeName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'accountName'">
          {{ record.accountName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span v-if="record.adminFlag !== 1" @click="showModal(record)"
              >编辑</span
            >
            <span
              class="del-btn"
              v-if="record.adminFlag !== 1"
              @click="delFun(record)"
              >删除</span
            >
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <addEdit v-if="addEditData.modalOpen" :addEditData="addEditData" />
</template>
    
  <script setup>
import { rspList, rspDel, rspDetail } from "@/api/weChatKeyword";
import weChatConfig from "@/components/weChat-config/weChatConfig.vue";
import addEdit from "./weChat-keyword/addEdit.vue";
import useGetList from "@/hooks/useGetList";
import { message } from "ant-design-vue";
import emitter from "@/assets/js/emitter";
import useDel from "@/hooks/useDel";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "关键字",
    dataIndex: "keyword",
  },
  {
    title: "匹配类型",
    dataIndex: "keywordTypeName",
  },
  {
    title: "消息类型",
    dataIndex: "msgType",
  },
  {
    title: "素材名称",
    dataIndex: "templateName",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    width: 160,
  },
  {
    title: "操作",
    dataIndex: "operate",
    width: 120,
    fixed: "right",
  },
]);
const pageLoading = ref(false);
//搜索表单数据
const searchForm = ref({
  keyword: ''
});
//查询列表
const searchFun = () => {
  useGetList(rspList, pageLoading, dataSource, pagination,{ 
    ...searchForm.value,
    pageNo: 1,
    pageSize: 10,
    chkCurWx: weChatId.value,
  });
};
// 获取列表数据
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
const weChatId = ref("");
onMounted(() => {
  emitter.on("initWeChatId", (value) => {
    weChatId.value = value;
    if (value) {
      useGetList(rspList, pageLoading, dataSource, pagination, {
        pageNo: 1,
        pageSize: 10,
        chkCurWx: value,
      });
    }
  });
});
//分页处理
const handleTableChange = (pag) => {
  pagination.pageSize = pag.pageSize;
  useGetList(rspList, pageLoading, dataSource, pagination, {
    pageNo: pag.current,
    pageSize: pag.pageSize,
    chkCurWx: weChatId.value
  });
};
//新增/编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: "新增关键字",
  weChatId: weChatId.value,
  list: {
    keyword: "",
    keywordType: null,
    templateName: "",
    tmplList: [
      {
        key: Date.now(),
        templateId: null,
        templateName: "",
        templateLabel: "",
        msgType: "",
        orderId: 1,
      }
    ],
  },
});
const showModal = async (item) => {
  if (item?.id) {
    pageLoading.value = true;
    const res = await rspDetail({ id: item.id, chkCurWx: weChatId.value });
    pageLoading.value = false;
    if (res.retCode === '000000') {
        addEditData.modalTitle = '编辑关键字';
        Object.assign(addEditData.list, res.data);
        addEditData.list.templateName = res.data.keyword;
        addEditData.list.tmplList.forEach((val,index)=>{
            if(val.msgType == 'text'){
                addEditData.list.tmplList[index].templateLabel = '文本'
            }else if(val.msgType == 'image'){
                addEditData.list.tmplList[index].templateLabel = '图片'
            }else if(val.msgType == 'news' || val.msgType == 'post'){
                addEditData.list.tmplList[index].templateLabel = '图文'
            }else if(val.msgType == 'voice'){
                addEditData.list.tmplList[index].templateLabel = '音频'
            }else if(val.msgType == 'video'){
                addEditData.list.tmplList[index].templateLabel = '视频'
            }
        })
    } else {
        message.error(res.retMsg);
    }
  } else {
    addEditData.modalTitle = '新增关键字';
    addEditData.list = {
      keyword: "",
      keywordType: null,
      templateName: "",
      tmplList: [
        {
          key: Date.now(),
          templateId: null,
          templateName: "",
          templateLabel: "",
          msgType: "",
          orderId: 1,
        }
      ],
    }
  }
  addEditData.modalOpen = true;
  addEditData.weChatId = weChatId.value;
};

//新增/编辑之后更新数据
emitter.on("addEditRsp", (value) => {
  if (value) {
    addEditData.modalOpen = false;
    useGetList(rspList, pageLoading, dataSource, pagination, {
      pageNo: 1,
      pageSize: 10,
      chkCurWx: weChatId.value,
    });
  }
});
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("addEditRsp");
  emitter.off("initWeChatId");
});
// 删除数据
const delFun = async (list) => {
  useDel(
    rspDel,
    { id: list.id, chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        useGetList(rspList, pageLoading, dataSource, pagination, {
          pageNo: 1,
          pageSize: 10,
          chkCurWx: weChatId.value,
        });
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    }
  );
};
</script>
    
  <style lang="less" scoped>
.user-content {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-box {
    display: flex;
  }
  .search-btn {
    margin-right: 20px;
  }
}
</style>
    