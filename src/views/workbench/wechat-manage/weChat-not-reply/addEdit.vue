<!-- 未识别回复语新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-form class="weChat-from">
        <a-form-item label="触发类型" v-bind="validateInfos.msgTriggerType">
          <a-select
            v-model:value="addEditFrom.msgTriggerType"
            :options="triggerArry"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择触发类型"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="关联素材" v-bind="validateInfos.templateName">
          <a-input
            :value="!addEditFrom.templateName?addEditFrom.templateName:addEditFrom.templateName+' - '+addEditFrom.msgTypeName"
            placeholder="请选择关联素材"
            disabled
          />
        </a-form-item>
        <a-button type="primary" @click="materialSelectorData" class="submit-btn">
          选择
        </a-button>
      </a-form>
    </a-spin>
  </a-modal>
  <materialTemplate v-if="selectData.modalOpen" :selectData="selectData" />
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { ukwEdit, ukwAdd, userSendMsgType } from "@/api/weChatNotReply";
import materialTemplate from '../material-template/materialTemplate.vue';
import emitter from "@/assets/js/emitter";
const props = defineProps({
  addEditData: Object,
});

const modalLoading = ref(false);
const addEditFrom = props.addEditData.list;
//表单规则验证数据
const addEditRules = reactive({
  templateName: [
    {
      required: true,
      message: "请选择关联素材",
      trigger: "change"
    }
  ],
  msgTriggerType: [
    {
      required: true,
      message: "请选择触发类型"
    }
  ]
});
//  获取触发类型
const triggerArry = ref([])
async function getUserSendMsgType() {
  modalLoading.value = true;
  const res = await userSendMsgType({chkCurWx: props.addEditData.weChatId});
  modalLoading.value = false;
  if (res.retCode === "000000") {
    for(let k in res.data){
      triggerArry.value.push({
        value: k,
        label: res.data[k]
      })
    }
  } else {
    message.error(res.retMsg);
  }
}
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//选择素材
const selectData = reactive({
  modalOpen: false,
  weChatId: props.addEditData.weChatId,
  item: ''
});

const materialSelectorData = (item = {}) => {
  selectData.modalOpen = true
};
onMounted(()=>{
  if(!triggerArry.value.length) {
    getUserSendMsgType()
  }
})
// 移除scroll监听
onUnmounted(()=>{
  emitter.off("selectArray");
})
let params = {};
emitter.on("selectArray", (value) => {
  params = value
  addEditFrom.templateName = `${value.templateName}-${value.typeName}`;
  selectData.modalOpen = false;
});
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const { id, msgTriggerType } = toRaw(addEditFrom);
      let fromData = {
        msgTriggerType,
        msgType: params.templateType,
        templateId: params.id,
        templateName: params.templateName
      };
      if(params.templateType === 'post') {
        fromData.msgType = 'news';
        fromData.templateId = params.checkboxs.join(',');
        fromData.templateName = `${params.templateName}{${params.checkboxs.length}}`;
      }else if(params.templateType === 'newsitem') {
        fromData.msgType = 'news';
        fromData.templateId = params.checkboxs.join(',');
        fromData.templateName = `${params.templateName}{${params.checkboxs.length}}`;
      }
      if (!addEditFrom.id) {
        res = await ukwAdd(fromData,selectData.weChatId);
      } else {
        //编辑提交
        fromData.id = id;
        res = await ukwEdit(fromData,selectData.weChatId);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditSub", true);
      } else if (res.retCode === "000040") {
        message.error("请勿添加相同触发类型的未识别回复语");
      }else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style scoped lang="less">
.ant-modal {
  .weChat-from {
    justify-content: flex-start;
    .ant-btn-primary {
      margin-left: 10px;
    }
  }
}
</style>