import emitter from "@/assets/js/emitter";
const domRef = ref()
// // 定义滚动函数
const pageNo = ref(1)
const handleScroll = () => {
	const tableContainer = domRef.value.$el.querySelector('.ant-spin-container');
	const scrollPosition = tableContainer.scrollTop;
	const isTop = scrollPosition === 0;
	const isBottom = tableContainer.scrollHeight - scrollPosition === tableContainer.clientHeight;
	if (isBottom) {
        pageNo.value++
        emitter.emit("loadingMore", pageNo.value);
	}
}
// 添加scroll监听
emitter.on("onMounted", (value) => {
    nextTick(()=>{
        domRef.value = value
      if (domRef.value) {
        const tableContainer = domRef.value.$el.querySelector('.ant-spin-container');
        tableContainer.addEventListener('scroll', handleScroll);
      }
    })
});
// 移除scroll监听
emitter.on("onBeforeUnmount", (value) => {
    nextTick(()=>{
		if (domRef.value) {
			const tableContainer = domRef.value.$el.querySelector('.ant-spin-container');
			tableContainer.removeEventListener('scroll', handleScroll);
		}
	})
});