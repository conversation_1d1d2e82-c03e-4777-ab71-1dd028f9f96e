<!-- 单篇最高阅读量（7日内）- 折线图 -->
<template>
  <div class="line-chart" ref="lineChartDom"></div>
</template>

<script setup>
import * as echarts from "echarts/core";
import { GridComponent, MarkPointComponent } from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import emitter from "@/assets/js/emitter";
echarts.use([
  GridComponent,
  MarkPointComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);
const props = defineProps({
    maxReadCountSevenDay: Number,
});
//数据传值
function shuffleArray(array) {//打乱数组
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}
let readData = reactive([props.maxReadCountSevenDay]);
for(let i = 1;i <= 6;i++){
    //制造一些假数据
    readData.push(parseInt(Math.random() * props.maxReadCountSevenDay))
}
shuffleArray(readData);
// 计算最大值
const maxValue = Math.max.apply(null, readData);
const lineChartDom = ref(null);
const setFunnel = () => {
  let myChart = echarts.init(lineChartDom.value);
  myChart.setOption({
    xAxis: {
      type: "category",
      boundaryGap: false,
      show: false,
    },
    yAxis: {
      type: "value",
      show: false,
    },
    series: [
      {
        data: readData.map(function (item) {
          // 根据比例判断是否显示符号
          var showSymbol = item / maxValue > 0.99; // 比例阈值可以自定义
          return {
            value: item,
            symbolSize: showSymbol ? 10 : 0, // 符号大小，0 表示不显示
            symbol: "circle", // 可以是 'circle', 'rect', 'triangle' 等
          };
        }),
        type: "line",
        itemStyle: {
          color: "#76a2f2",
        },
        smooth: 0.5,
        markPoint: {
          data: [
            {
              type: "max",
            },
          ],
          label: {
            fontWeight: "bold",
            fontSize: 14,
            color: "rgba(11, 11, 11, 1)",
          },
          itemStyle: {
            color: "transparent",
          },
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "#9BC9FE",
            },
            {
              offset: 1,
              color: "#FFFFFF",
            },
          ]),
        },
      },
    ],
  });
};
onMounted(() => {
  nextTick(() => {
    setFunnel();
  });
});
</script>

<style lang="less" scoped>
.line-chart {
  height: 240px;
  position: relative;
  top: -20px;
}
</style>
