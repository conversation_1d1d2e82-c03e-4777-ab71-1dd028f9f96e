import { fileURLToPath, URL } from 'node:url'
import path from 'path';

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import ViteCompression from 'vite-plugin-compression'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import AutoComponents from 'unplugin-vue-components/vite'
//自动按需导入 Vue、Vue-Router 等 API
import AutoImport from "unplugin-auto-import/vite"

export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? './' : '/',
  plugins: [
    vue(),
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue\?vue/, /\.vue$/],
      imports: ["vue", "pinia", "vue-router"],
      eslintrc: {
        enabled: true,
        filepath: "./.eslintrc-auto-import.json",
        globalsPropValue: true
      },
      dts: true
    }),
    ViteCompression({
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
    }),
    AutoComponents({
      dirs: ['src/components'],
      resolvers: [
        AntDesignVueResolver({
          resolveIcons: true,
          importStyle: 'less',
        }),
      ],
      include: [
        /\.[tj]sx?/,
        /\.vue\?vue/,
        /\.vue$/,
      ],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0', //ip地址
    port: 8089, //端口
    https: false, //false关闭https，true为开启
    open: true, //自动打开浏览器
    proxy: {
      '/act': {
        target: 'http://**********:10001',
        changeOrigin: true,
        headers: {
          referer: "https://mcn.i139.cn"
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${path.resolve("src/assets/css/base.less")}";`,
        },
        javascriptEnabled: true,
      }
    }
  }
})
