import $https from "@/assets/js/request"

// 微信id数据
export const articleList = (data) => {
    return $https.fetchGet('/cop/wx/back/articleShare/articleConfig/page',data)
}
export const articleDel = (data) => {
  return $https.fetchGet('/cop/wx/back/articleShare/articleConfig/del',data)
}
export const articleSort = (data,chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/articleShare/sort?chkCurWx='+chkCurWx,data)
}
export const articleDetail = (data) => {
  return $https.fetchGet('/cop/wx/back/articleShare/articleConfig/detail',data)
}
export const articleEdit = (data,chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/articleShare/articleConfig/edit?chkCurWx='+chkCurWx,data)
}
export const articleAdd = (data,chkCurWx) => {
  return $https.fetchPost('/cop/wx/back/articleShare/articleConfig/add?chkCurWx='+chkCurWx,data)
}