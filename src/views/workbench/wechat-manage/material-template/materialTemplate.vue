<!-- 素材tabs -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.selectData.modalOpen"
    okText="确定"
    :confirm-loading="modalLoading"
    :wrapClassName="'material-template'"
    width="760px"
  >
    <a-spin :spinning="modalLoading">
      <a-tabs
        v-model:activeKey="activeKey"
        @change="changeMaterial"
        :destroyInactiveTabPane="isTabPane"
      >
        <a-tab-pane key="text" tab="文本">
          <tempText :materialData="materialData" />
        </a-tab-pane>
        <a-tab-pane key="image" tab="图片">
          <tempImage :materialData="materialData" />
        </a-tab-pane>
        <a-tab-pane key="news" tab="图文消息">
          <tempNews :materialData="materialData" />
        </a-tab-pane>
        <a-tab-pane key="newsitem" tab="单图文">
          <tempNewsitem :materialData="materialData" />
        </a-tab-pane>
        <a-tab-pane key="post" tab="文章">
          <tempPost :materialData="materialData" />
        </a-tab-pane>
        <a-tab-pane key="voice" tab="音频">
          <tempVoice :materialData="materialData" />
        </a-tab-pane>
        <a-tab-pane key="video" tab="视频">
          <tempVideo :materialData="materialData" />
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    <template #title>
      <div class="search-box">
        <a-input v-model:value="searchForm.title" placeholder="请输入姓名" />
        <a-button
          type="primary"
          :disabled="!searchForm.title"
          @click="searchFun"
          class="submit-btn"
        >
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
      </div>
    </template>
    <template #footer>
      <div
        class="footer-box"
        v-show="activeKey === 'post' || activeKey === 'newsitem'"
      >
        <a-button @click="selectSubmit" type="primary">确定</a-button>
        <a-button @click="resetCheckboxs">重置</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { message } from "ant-design-vue";
import tempText from "./template/text.vue";
import tempImage from "./template/image.vue";
import tempNews from "./template/news.vue";
import tempNewsitem from "./template/newsitem.vue";
import tempPost from "./template/post.vue";
import tempVoice from "./template/voice.vue";
import tempVideo from "./template/video.vue";
import { listMaterialSelectorData } from "@/api/materialTemplate";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  selectData: Object,
});
const activeKey = ref("text");
const isTabPane = ref(true);
const modalLoading = ref(false);
const materialData = ref([]);
const templateNames = ref([
  { name: "文本", key: "text" },
  { name: "图片", key: "image" },
  { name: "图文消息", key: "news" },
  { name: "单图文", key: "newsitem" },
  { name: "文章", key: "post" },
  { name: "音频", key: "voice" },
  { name: "视频", key: "video" },
]);
//表单数据
const searchForm = reactive({
  title: "",
});
//获取素材资源
const nextPage = ref(1);
async function getMaterial(type, pageNo = 1) {
  if (nextPage.value === 0) {
    message.error("暂无更多数据");
    return false;
  }
  const params = {
    pageNo,
    pageSize: 20,
    chkCurWx: props.selectData.weChatId,
    title: searchForm.title,
    type,
  };
  modalLoading.value = true;
  const res = await listMaterialSelectorData(params);
  modalLoading.value = false;
  nextPage.value = res.data.nextPage;
  if (res.retCode === "000000") {
    materialData.value.push(...res.data.list);
  } else {
    message.error(res.retMsg);
  }
}
onMounted(() => {
  getMaterial("text");
});
emitter.on("loadingMore", (value) => {
  console.log('加载更多！！！！！123-1--12-2-2-3')
  getMaterial(activeKey.value, value);
});
//查询
const searchFun = () => {
  materialData.value = [];
  getMaterial(activeKey.value, 1);
};
//切换tabs
const changeMaterial = (value) => {
  materialData.value = [];
  nextPage.value = 1;
  searchForm.title = "";
  getMaterial(value, 1);
  emitter.emit("toggleMaterial", 1);
  console.log(emitter)
};
// 卸载emitter
onUnmounted(() => {
  emitter.off("selectPost");
  emitter.off("resetCheckboxs");
  emitter.off("loadingMore");
});
let selectData = ref({});
emitter.on("selectPost", (value) => {
  selectData.value = value;
});
//确定选择
const selectSubmit = () => {
  if (!selectData.value?.checkboxs || selectData.value.checkboxs.length === 0) {
    message.error("请选择数据");
    return false;
  }
  emitter.emit("selectArray", selectData.value);
};
//重置
const resetCheckboxs = () => {
  selectData.value.checkboxs = [];
  emitter.emit("resetCheckboxs", selectData.value);
};
</script>
<style lang='less'>
.material-template {
  .ant-modal-body {
    min-height: 446px;
    padding: 0 20px 20px 20px;
  }
  .footer-box {
    display: flex;
  }
  .ant-modal-footer .ant-btn-default {
    display: block;
  }
  .search-box {
    display: flex;
  }
}
</style>
