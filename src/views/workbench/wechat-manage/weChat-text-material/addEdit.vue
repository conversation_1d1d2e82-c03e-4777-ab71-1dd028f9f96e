<!-- 文本素材新增编辑 -->
<template>
  <a-modal
    :width="380"
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-form class="weChat-from">
        <a-form-item label="素材名称" v-bind="validateInfos.templateName">
          <a-input
            v-model:value="addEditFrom.templateName"
            placeholder="请输入文本素材名称"
          />
        </a-form-item>
        <a-form-item label="素材内容" v-bind="validateInfos.templateContent">
          <a-textarea
            v-model:value="addEditFrom.templateContent"
            placeholder="请输入文本素材内容"
            :auto-size="{ minRows: 6, maxRows: 15 }"
          />
          <div class="add-link" @click="addLinkTxt">插入超链</div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { txtEdit, txtAdd } from "@/api/weChatTextMaterial";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  addEditData: Object,
});

const modalLoading = ref(false);
const addEditFrom = props.addEditData.item;
//表单规则验证数据
const addEditRules = reactive({
  templateName: [
    {
      required: true,
      message: "请输入文本素材名称",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符",
      trigger: "change",
    },
  ],
  templateContent: [
    {
      required: true,
      message: "请输入文本素材内容",
    },
    {
      min: 1,
      max: 600,
      message: "长度在1 到 600个字符",
      trigger: "change",
    },
  ],
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
function addLinkTxt() {
  addEditFrom.templateContent+="<a href='https://超链接地址'>链接说明</a>"
}
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const { id, templateName, templateContent } = toRaw(addEditFrom);
      let fromData = {
        templateName,
        templateContent
      };
      if (!addEditFrom.id) {
        //新增
        res = await txtAdd(fromData,props.addEditData.weChatId);
      } else {
        //编辑提交
        fromData.id = id;
        res = await txtEdit(fromData,props.addEditData.weChatId);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditTxt", true);
      } else if (res.retCode === "000030") {
        message.error("请勿添加同名文本素材");
      }else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style scoped lang="less">
.ant-modal {
  .weChat-from {
    justify-content: flex-start;
    .ant-btn-primary {
      margin-left: 10px;
    }
  }
  .add-link {
    color: #1989fa;
    font-size: 12px;
    cursor: pointer;
    padding-top: 5px;
  }
}
</style>