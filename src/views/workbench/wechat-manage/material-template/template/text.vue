<!-- 文本 -->
<template>
  <a-list ref="listItemRef" size="small" bordered :data-source="materialData">
    <template #renderItem="{ item }">
      <a-list-item @click="selectMaterial(item)">{{ item.templateName }}</a-list-item>
    </template>
  </a-list>
</template>
<script setup>
import emitter from "@/assets/js/emitter";
import "@/hooks/useLoadMore";
const props = defineProps({
    materialData: Object
});
const listItemRef = ref();
// 添加scroll监听
onMounted(()=>{
    nextTick(()=>{
        emitter.emit("onMounted", listItemRef.value);
    })
})
// 移除scroll监听
onUnmounted(()=>{
    emitter.off("onBeforeUnmount");
})
//选择的素材
const selectMaterial = (item) => {
    item.typeName = '文本';
    item.templateType = 'text';
    emitter.emit("selectArray", item);
}
</script>
<style lang="less" scoped>
.ant-list-item:hover {
    cursor: pointer;
    background-color: #f5f7fa;
}
:deep(.ant-spin-container) {
    max-height: 360px;
    overflow-y: scroll;
}
</style>
