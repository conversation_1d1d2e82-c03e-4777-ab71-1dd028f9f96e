<!-- 账号权限管理 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-input
        v-model:value="searchForm.name"
        placeholder="请输入姓名"
      /> 
      <a-input
        v-model:value="searchForm.mobile"
        placeholder="请输入手机号码"
      />
      <a-button 
      type="primary" 
      @click="searchFun" class="submit-btn">
        <template #icon>
          <SearchOutlined />
        </template>
        查询
      </a-button>
    </div>
    <a-button type="primary" @click="showModal()" class="submit-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增用户
    </a-button>
  </div>
  <div class="user-content">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'adminFlag'">
          {{ record.adminFlag===1?"是":"否" }}
        </template>
        <template v-if="column.dataIndex === 'accountName'">
          {{ record.accountName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span @click="showModaAccount(record)">授权账号</span>
            <span @click="showModaMenu(record)">授权菜单</span>
            <span v-if="record.adminFlag!==1" @click="showModal(record)">编辑</span>
            <span class="del-btn" v-if="record.adminFlag!==1" @click="delFun(record)">删除</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <addEdit v-if="addEditData.modalOpen" :addEditData="addEditData" />
  <setAccount v-if="setData.modalOpen" :accountId="accountId" :setData="setData" />
  <accreditMenu v-if="setMenuData.modalOpen" :setMenuData="setMenuData"/>
</template>

<script setup>
import { userPage, userDel, relTree, cityAllTree, userProvince, menuDeptTree } from '@/api/userManage';
import addEdit from './user-manage/addEdit.vue';
import setAccount from './user-manage/setAccount.vue';
import accreditMenu from './user-manage/accreditMenu.vue';
import useGetList from '@/hooks/useGetList';
import { message } from 'ant-design-vue';
import emitter from '@/assets/js/emitter';
import useDel from '@/hooks/useDel';
import { encryptionMobile } from "@/assets/js/utils";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: '姓名',
    dataIndex: 'name',
    width: 120
  },
  {
    title: '手机号码',
    dataIndex: 'mobile',
    width: 120
  },
  {
    title: '管理员',
    dataIndex: 'adminFlag',
    width: 120
  },
  {
    title: '权限账号',
    dataIndex: 'accountName',
    width:120,
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'operate',
    width: "240px",
    fixed: "right"
  },
]);
const pageLoading = ref(false);
//表单数据
const searchForm = reactive({
  name: '',
  mobile: '',
});
// 获取列表数据
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
})

onMounted(() => {
  useGetList(userPage, pageLoading, dataSource, pagination);
});
const handleTableChange = (pag) => { // 点击分页处理
  let params = {
    name: searchForm.name?encodeURI(encryptionMobile(searchForm.name)):'',
    mobile: searchForm.mobile?encodeURI(encryptionMobile(searchForm.mobile)):''
  }
  useGetList(userPage, pageLoading, dataSource, pagination,{ ...params ,pageNum: pag.current,pageSize: pag.pageSize });
};
//查询列表
const searchFun = () => { 
  let params = {
    name: searchForm.name?encodeURI(encryptionMobile(searchForm.name)):'',
    mobile: searchForm.mobile?encodeURI(encryptionMobile(searchForm.mobile)):''
  }
  useGetList(userPage, pageLoading, dataSource, pagination,{ ...params,pageNum: 1 });
};
//分配账号
const setData = reactive({
  modalOpen: false,
  userId: null,
  deptListDatas: []
})
const accountId = ref([])
const showModaAccount = async (item) => {
  pageLoading.value = true;
  const res = await relTree({userId:item.id});
  pageLoading.value = false;
  if (res.retCode === "000000") {
    setData.modalOpen = true;
    setData.userId = item.id;
    accountId.value = []
    if(item.accountId) {
      item.accountId.split(',').forEach((el,i) => {
        accountId.value.push(parseInt(el))
      });
    }
    let treeData = res.data
    treeData.forEach((el,i) => {
      treeData[i].id = (i+1) * 100000
    });
    setData.deptListDatas = []
    Object.assign(setData.deptListDatas,res.data)
  } else {
    message.error(res.retMsg);
  }
};
//新增/编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: '新增用户',
  cityAllTree: [],
  list: {name:'',mobile:'',dept:[]}
});
async function getCityAllTree () {
  pageLoading.value = true;
  const res = await cityAllTree();
  pageLoading.value = false;
  if (res.retCode === "000000") {
    addEditData.cityAllTree = [];
    Object.assign(addEditData.cityAllTree,res.data)
    addEditData.modalOpen = true;
  } else {
    message.error(res.retMsg);
  }
}
const showModal = (item = {name:'',mobile:'',id: null,dept:[]}) => {
  getCityAllTree()
  addEditData.list.dept = [];
  Object.assign(addEditData.list,item);
  if (item.id) {
    addEditData.modalTitle = '编辑用户';
    addEditData.list.dept = item.deptId.split(',')
  }else {
    addEditData.modalTitle = '新增用户';
  }
  
};
//授权菜单数据
const setMenuData = reactive({
  modalOpen: false,
  provinceTree: [],
  menuTree: [],
  list: {id:'',deptId:null,menu:[]}
});
async function getMenuAllTree (userId) {
  pageLoading.value = true;
  // 获取机构树结构
  const provinceRes = await userProvince({userId});
  pageLoading.value = false;
  if (provinceRes.retCode === "000000") {
    setMenuData.provinceTree = [];
    Object.assign(setMenuData.provinceTree,provinceRes.data)
    setMenuData.modalOpen = true;
  } else {
    message.error(provinceRes.retMsg);
  }
}
const showModaMenu = (item) => {
  getMenuAllTree(item.id)
  setMenuData.list.id = item.id
  setMenuData.list.deptId = null
  setMenuData.list.menu = []
};
//新增/编辑之后更新数据
emitter.on('addEditUser',(value)=>{
  if(value){
    addEditData.modalOpen = false;
    setData.modalOpen = false;
    useGetList(userPage, pageLoading, dataSource, pagination);
  }
}) 
//组件卸载后销毁emitter
onUnmounted(()=>{
  emitter.off('addEditUser')
})
// 删除数据
const delFun = async (list) => {
  useDel(userDel,{id:list.id}, pageLoading, (res)=>{
    if (res.retCode === '000000') {
      useGetList(userPage, pageLoading, dataSource, pagination);
      message.success('操作成功！')
    } else {
      message.error(res.retMsg);
    }
  });
};
</script>

<style lang="less" scoped>
.user-content {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-box {
    display: flex;
  }
}
</style>
