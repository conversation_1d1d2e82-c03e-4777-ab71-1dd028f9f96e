<!-- 创作 -->
<template>
  <div class="write-tab-content">
    <div class="write-form">
      <a-form>
        <a-form-item label="发布平台" class="platform-form" v-bind="validateInfos.platform">
          <a-radio-group v-model:value="writeForm.platform">
            <a-radio-button 
            v-for="item in platformOpt" 
            :value="item.id"
            :key="item.id"
          >
          {{ item.name }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="创作要求" v-bind="validateInfos.require">
          <a-textarea
            v-model:value="writeForm.require"
            placeholder="请输入您的创作要求，如文章主题、背景、内容等"
            allow-clear
            show-count 
            :maxlength="2000"
            :auto-size="{ minRows: 6, maxRows: 8 }"
          />
        </a-form-item>
        <a-form-item label="语言风格" v-bind="validateInfos.style">
          <a-select
            v-model:value="writeForm.style"
            :options="styleOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择语言风格"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="文案类型" v-bind="validateInfos.type">
          <a-select
            v-model:value="writeForm.type"
            allow-clear
            :options="typeOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择文案类型"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="文案字数" v-bind="validateInfos.number">
          <a-input
            v-model:value="writeForm.number"
            placeholder="请输入文案字数;默认2000;字数上限为5000"
          />
        </a-form-item>
      </a-form>
      <p class="history-btn">
          <span @click="emit('emitHistory')" v-if="props.historyData.isEmpty">查看历史文章</span>
      </p>
      <div class="button-box">
        <a-button 
        :disabled="writeForm.require === ''?true:false || formDisabled"
        type="primary" 
        :class="{'write-loading':reasoningMarkdown}"
        @click="addEditSubmit">{{submitText}}</a-button>
      </div>
    </div>
    <div class="write-result">
      <div class="write-banner" v-if="writeEmpty">
        <p class="write-banner-title">文章创作</p>
        <p class="write-banner-desc">填写左侧设置项，即可生成文案</p>
        <img src="../../../assets/image/write-banner.png" alt="">
      </div>
      <div class="result-box" ref="scrollContainer" v-else>
        <p class="result-reasoning" v-html="reasoningMarkdown"></p>
        <div class="result-content" v-html="compiledMarkdown"></div>
      </div>
      <div class="copy-box" v-show="compiledMarkdown">
        <span>内容由AI生成，请注意甄别真实性</span>
        <span class="copy-btn" @click="copyWrite"><CopyFilled />复制全文</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { marked }from 'marked';
import emitter from '@/assets/js/emitter';
import { fetchEventSource } from '@microsoft/fetch-event-source';
const props = defineProps({
  historyData: Object
});
//查看历史创建文章
const emit = defineEmits(['emitHistory']);
const platformOpt = [
  {name:'公众号',id:'1',desc:'你擅长写公众号文章，现在你需要根据以下需求写公众号文章：'},
  {name:'微博',id:'2',desc:'你擅长写微博文章，现在你需要根据以下需求写微博文章：'},
  {name:'抖音',id:'3',desc:'你擅长写抖音爆款文案，现在你需要根据以下需求写抖音爆款文案：'},
  {name:'视频号',id:'4',desc:'你擅长写视频号短视频文案，现在你需要根据以下需求写爆款短视频文案：'},
  {name:'小红书',id:'5',desc:'你擅长写小红书种草笔记，现在你需要根据以下需求写小红书笔记：'}
]
const styleOpt = [
  {name:'正式',id:'1'},
  {name:'简洁',id:'2'},
  {name:'口语化',id:'3'},
  {name:'热情',id:'4'},
  {name:'礼貌',id:'5'}
]
const typeOpt = [
  {name:'知识科普',id:'1'},
  {name:'品牌宣传',id:'2'},
  {name:'品牌推广',id:'3'},
  {name:'资讯信息',id:'4'},
  {name:'新闻稿件故事类',id:'5'},
  {name:'活动宣传',id:'6'},
  {name:'演讲稿',id:'7'}
]
let submitText = ref('文案生成')
//表单规则验证数据
const writeForm = reactive({
  platform: '1',
  require: '',
  style: '1',
  type: null,
  number: null
})
const validateNumber = async (_rule, value) => {
  if (value && !/^[1-9]\d*$/.test(value)) {
      return Promise.reject('请输入正整数');
  } else if (parseInt(value) > 5000) {
      return Promise.reject('字数不能超过最大上限5000');
  } else {
      return Promise.resolve();
  }
};
const addEditRules = reactive({
  require: [
    {
      required: true,
      message: "请输入您的创作主题",
    },
    {
      min: 1,
      max: 2000,
      message: "长度在1 到 2000个字符",
      trigger: "change"
    }
  ],
  number: [
    {
      required: false,
      validator: validateNumber,
      trigger: "blur",
    }
  ],
  platform: [
    {
      required: true,
      trigger: "change",
      message: "请选择平台"
    }
  ],
  style: [
    {
      required: true,
      trigger: "change",
      message: "请选择语言风格"
    }
  ],
  type: [
    {
      required: false,
      trigger: "change",
      message: "请选择文案类型"
    }
  ]
});
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  writeForm,
  addEditRules
);
const formDisabled = ref(false);
const writeEmpty = ref(true);
watch(()=>writeForm.require,(newValue,oldValue)=>{
  if(!newValue) {
    formDisabled.value = false;
  }
})
const writeData = reactive(
  {
    reasoning: '',
    content: ''
  }
)
//点击历史记录回显数据
emitter.on('historyTag',(value)=>{
  writeData.content = value.content;
  writeData.reasoning = '';
  writeEmpty.value = false;
  Object.assign(writeForm,value.userParam);
  if(value.userParam === 2000) {
    writeForm.number = null
  }
})
// 使用marked将Markdown转换为HTML
const compiledMarkdown = computed(() => {
  return marked(writeData.content);
});
// 使用marked将Markdown转换为HTML
const reasoningMarkdown = computed(() => {
  return marked(writeData.reasoning);
});
function copyWrite() {
  navigator.clipboard
    .writeText(JSON.stringify(compiledMarkdown.value))
    .then(() => {
      message.success("已复制到剪贴板");
    })
    .catch((error) => {
      message.success("系统繁忙，请稍后再试！");
    });
}
const scrollContainer = ref(null);
onMounted(()=> {
  if(localStorage.getItem('topicRequire')) {
    let topicRequire = JSON.parse(localStorage.getItem('topicRequire'));
    writeForm.require = topicRequire.title + '\n' + topicRequire.summary;
    localStorage.removeItem('topicRequire');
  }
})
const addEditSubmit = () => {
  validate()
    .then(async () => {
      writeData.reasoning = '';
      writeData.content = '';
      const fromData = {...toRaw(writeForm)};
      let sseStatus = '',
      styleName = '',
      typeName = '',
      platformDesc = '';
      if(fromData.platform) {
        platformOpt.forEach(val => {
          if(val.id === fromData.platform) {
            platformDesc = val.desc
          }
        });
      }
      if(fromData.style) {
        styleOpt.forEach(val => {
          if(val.id === fromData.style) {
            styleName = val.name
          }
        });
      }
      if(fromData.type) {
        typeOpt.forEach(val => {
          if(val.id === fromData.type) {
            typeName = val.name
          }
        });
      }
      let params = {
        writeFlag: '1',
        prompt: `${platformDesc}${fromData.require}，${styleName?`语言风格为${styleName}，`:''}${typeName?`文章类型为${typeName}，`:''}字数为${fromData.number?`${fromData.number}`:2000}。`,
        userParam: JSON.stringify(fromData).toString().replace(new RegExp("\\\\\"","gm"),"\"")
      }
      fetchEventSource(`/cop/ai/write/ali/dp/r?replayTime=${new Date().getTime()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        },
        openWhenHidden: true,
        body: `writeFlag=${params.writeFlag}&prompt=${params.prompt}&userParam=${encodeURIComponent(params.userParam)}`,
        onmessage(ev) {
          writeEmpty.value = false;
          let writeContent = JSON.parse(ev.data);
          // console.log(writeContent)
            // 这里可以根据接收到的流式数据更新前端界面
          if(writeContent.content === 'reasoning') {//推理状态
            submitText.value = '文案生成中...';
            sseStatus = writeContent.content;
            formDisabled.value = true;
          }else if (writeContent.content === 'content') {//内容状态
            sseStatus = writeContent.content;
          }else if (writeContent.content === 'COPSSEEND'){//关闭
            submitText.value = '文案生成';
            formDisabled.value = false;
          }else if(writeContent.content === 'COP_SSE_DATA_INSPECTION_FAILED') {
            submitText.value = '文案生成';
            writeData.reasoning = '您的创作要求含有敏感词汇，请修改后再重新生成';
            formDisabled.value = false;
          }else if(writeContent.content === 'COPSSEERROR') {
            writeData.reasoning = '系统繁忙，请稍后再试';
            submitText.value = '文案生成';
            writeData.content = '';
            formDisabled.value = false;
          }
          if (sseStatus === 'reasoning' && !writeContent.content.includes('reasoning')) {
            writeData.reasoning += writeContent.content;
          }else if(sseStatus === 'content' && !writeContent.content.includes('content') && !writeContent.content.includes('COPSSEEND')) {
            writeData.content += writeContent.content;
          }
          //滚动条置底
          if(scrollContainer.value && scrollContainer.value.scrollHeight > scrollContainer.value.getBoundingClientRect().height) {
            scrollContainer.value.scrollTo({
              top: scrollContainer.value.scrollHeight,
              behavior: "smooth"
            })
          }
        },
        onerror(err) {
          // console.log(err)
          writeData.reasoning = '系统繁忙，请稍后再试';
          writeData.content = '';
          writeEmpty.value = false;
          submitText.value = '文案生成';
          throw err;
        },
      });
    })
};
</script>
<style lang="less" scoped>
.write-tab-content {
  height: calc(100vh - 170px);
  display: flex;
  overflow-y: auto;
  .write-form {
    width: 465px;
    padding:20px;
    border-right: 1px solid #E2E8F0;
    .ant-form-item {
      margin-bottom: 20px;
    }
  }
  .write-result {
    flex: 1;
    padding: 30px;
  }
  .write-banner {
    display: flex;
    height: 100%;
    min-width: 600px;
    max-width: 1200px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img{
      width: 367px;
    }
  }
  .write-banner-title {
    font-size: 18px;
    font-weight: bold;
    color: #1E293B;
  }
  .write-banner-desc {
    font-size: 14px;
    color: #666;
  }
  .result-box {
    // height: 100%;
    height: calc(100vh - 260px);
    min-width: 640px;
    max-width: 1200px;
    margin: 0 auto;
    text-align: justify;
    overflow-y: auto;
  }
  .copy-box {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(0, 0, 0, .3);
    .anticon-copy {
      margin-right: 3px;
    }
  }
  .copy-btn {
    cursor: pointer;
    font-size: 14px;
    color: rgba(0, 0, 0, .5);
  }
  .result-reasoning {
    color: #8b8b8b;
  }
  .result-content {
    color: #011738;
  }
  :deep(.ant-form-item-label) {
    width: 80px;
  } 
  .ant-form {
    min-height: 395px;
    margin-bottom: 20px;
    border: 1px solid transparent;
  }
  .platform-form {
    :deep(.ant-form-item-control) {
      flex: 1;
    }
    .ant-radio-button-wrapper {
      width: 90px;
      padding: 0;
      border-inline-start-width: 1px;
      border-radius: 4px;
      font-size: 14px;
      color: #1E293B;
      text-align: center;
      margin-right: 10px;
    }
    .ant-radio-button-wrapper:first-child {
      margin-bottom: 10px;
    }
    .ant-radio-button-wrapper-checked {
      background: #FFA31C;
      border-radius: 4px;
      color: #fff;
    }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before,
    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;    
    }
  }
  .history-btn {
    height: 20px;
    text-align: right;
    font-size: 12px;
    color: #1E293B;
    margin-bottom: 5px;
    span {
      cursor: pointer;
    }
  }
  .button-box {
    position: relative;
    button { 
      width: 130px;
      position: absolute;
      right: 0;
      top: 0;
      border: 0;
    }
    :deep(span) {
      margin: 0 auto;
    }
    .ant-btn-primary:disabled {
      border: 0;
      color: #fff;
      background-image: linear-gradient(90deg, #CACACA 0%, #CACACA 100%);
    }
    .write-loading:disabled {
      background-image: linear-gradient(90deg, rgba(254, 115, 9, .7) 0%, rgba(254, 115, 9, .7) 100%);
    }
  }
}

</style>
