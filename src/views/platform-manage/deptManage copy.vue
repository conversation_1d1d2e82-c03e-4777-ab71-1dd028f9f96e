<!-- 机构管理 -->
<template>
  <div ref="pageContent" class="dept-content page-content">
    <a-table 
    :columns="columns" 
    :data-source="dataSource" 
    borderedsize="small"
    :pagination="false"
    bordered
    :loading="pageLoading"
    :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'county'">
          {{ record.county }}
          <MenuOutlined @click="showModaAccount(record)"/>
      </template>
      </template>
    </a-table>
  </div>
  <setMenu v-if="setData.modalOpen" :accountId="accountId" :setData="setData" />
</template>
<script setup>
import { deptList, menuAllTree, menuAllDetail } from "@/api/deptManage";
import setMenu from './dept-manage/setMenu.vue';
import emitter from '@/assets/js/emitter';

const dataSource = ref([]);
const countyfilters = ref([])
const pageLoading = ref(false)
//定义合并的时期变量
const rowCountyArr = ref([]);
const roadCityArr = ref([]);
//合并行
const getRowspan = (dataScroce, filed) => {
  let spanArr = [];
  let position = 0;
  dataScroce.forEach((item, index) => {
    if (index === 0) {
      spanArr.push(1);
      position = 0;
    } else {
      //需要合并的地方判断
      if (dataScroce[index][filed] === dataScroce[index - 1][filed]) {
        spanArr[position] += 1;
        spanArr.push(0);
      } else {
        spanArr.push(1);
        position = index;
      }
    }
  });
  return spanArr;
};
// 数据处理的方法
const toDataSource = (resData) => {
  let arr = [];
  resData.map((responseDataItem) => {
    countyfilters.value.push({
      text: responseDataItem.label,
      value: responseDataItem.label
    })
    responseDataItem.children.map((item) => {
      arr = [
        ...arr,
        {
          county: responseDataItem.label,
          city: item.label,
          id: responseDataItem.id
          // channel: it.label,
        },
      ];
      // item.children.map((it) => {
      //   console.log(it)
      //   arr = [
      //     ...arr,
      //     {
      //       county: responseDataItem.label,
      //       city: item.label,
      //       id: responseDataItem.id,
      //       channel: it.label,
      //     },
      //   ];
      // });
    });
  });
  dataSource.value = arr;
  
  rowCountyArr.value = getRowspan(dataSource.value, "county");
  roadCityArr.value = getRowspan(dataSource.value, "city");
};
// 获取列表数据
const getDeptList = async () => {
  pageLoading.value = true
  const res = await deptList();
  pageLoading.value = false
  if (res.retCode === "000000") {
    toDataSource(res.data);
  } else {
    message.error(res.retMsg);
  }
};
// useMousePosition()
onMounted(()=>{
  getDeptList();
})
const columns = [
  {
    title: "省级部门",
    dataIndex: "county",
    customCell: (record, rowIndex, column) => {
      return {
        rowSpan: rowCountyArr.value[rowIndex],
        style: {
          "text-align": "center", // 单元格文本居中
          "vertical-align": "middle", // 单元格内容垂直居中
        },
      };
    },
    filterMultiple: false,
    onFilter: (value, record) => record.county.indexOf(value) === 0,
    filters: countyfilters.value
  },
  {
    title: "市级部门",
    dataIndex: "city"
    // customCell: (record, rowIndex, column) => {
    //   return {
    //     rowSpan: roadCityArr.value[rowIndex],
    //     style: {
    //       "text-align": "center", // 单元格文本居中
    //       "vertical-align": "middle", // 单元格内容垂直居中
    //     },
    //   };
    // },
  }
  // ,
  // {
  //   title: "区级部门",
  //   dataIndex: "channel",
  // }
];

//分配账号
const setData = reactive({
  modalOpen: false,
  id: null,
  deptListDatas: []
})

const showModaAccount = async (item) => {
  pageLoading.value = true;
  const res = await menuAllTree();
  if (res.retCode === "000000") {
    setData.id = item.id;
    // let treeData = res.data
    // treeData.forEach((el,i) => {
    //   treeData[i].id = (i+1) * 100000
    // });
    Object.assign(setData.deptListDatas,res.data)
    getMenuAllDetail(item.id)
  } else {
    message.error(res.retMsg);
  }
};
//获取省份关联菜单详情
const accountId = ref([])
const getMenuAllDetail = async (deptId) => {
  const res = await menuAllDetail({deptId});
  if (res.retCode === "000000") {
    pageLoading.value = false;
    accountId.value = res.data || [];
    setData.modalOpen = true;
  } else {
    message.error(res.retMsg);
  }
}
//设置菜单后更新数据
emitter.on('setMenu',()=>{
  setData.modalOpen = false;
}) 
//组件卸载后销毁emitter
onUnmounted(()=>{
  emitter.off('setMenu')
})
</script>
<style lang="less" scoped>
.dept-content {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 120px);
  .anticon-menu {
    font-size: 12px;
    color: #FE8308;
  }
}
</style>


