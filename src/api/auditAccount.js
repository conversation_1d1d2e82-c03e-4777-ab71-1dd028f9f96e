import $https from "@/assets/js/request"

// 省级
export const prAll = (data) => {
    return $https.fetchGet('/cop/manage/dept/pr/all',data)
}
// 列表
export const auditPage = (data) => {
    return $https.fetchGet('/cop/manage/account/audit/page',data)
}
// 完成并通过审核-运营归口数据 
export const deptList = (data) => {
    return $https.fetchGet('/cop/manage/dept/city/pr/tree',data)
}
// 完成并通过审核-运营人员下拉数据
export const allUser = (data) => {
    return $https.fetchGet('/cop/manage/user/all/select',data)
}
// 完成并通过审核
export const auditAgree = (data) => {
    return $https.fetchPost('/cop/manage/account/audit/agree',data)
}
//驳回
export const accountAudit = (data) => {
    return $https.fetchPost('/cop/manage/account/audit/reject',data)
}