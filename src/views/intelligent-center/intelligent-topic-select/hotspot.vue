<!-- 热点结合选择 -->
<template>
  <div class="hotspot-page">
    <a-drawer
      :width="620"
      placement="right"
      :open="props.hotspotDrawerData.drawerOpen"
      :get-container="false"
      @close="onClose"
      :closable="false"
      class="history-list-drawer"
      :force-render="true"
    >
    <div class="drawer-title">
      选择热点发散话题
    </div>
    <a-spin :spinning="modalLoading">
      <a-tabs
        @change="tabsToggle"
        v-model:activeKey="activeKey"
        destroyInactiveTabPane
      >
        <a-tab-pane key="1" tab="百度">
          <div class="scroll-box" ref="tabsRef">
            <div class="hotspot-list-box" :class="{'hotspot-list-activity':item.title===props.hotspotDrawerData.hotspot.title}" v-for="(item,index) in hotspotData" :key="item.id">
              <div class="hotspot-title-box">
                <p class="hotspot-title">
                  <img v-if="index===0" src="../../../assets/image/no.1.png" alt="">
                  <img v-else-if="index===1" src="../../../assets/image/no.2.png" alt="">
                  <img v-else-if="index===2" src="../../../assets/image/no.3.png" alt="">
                  <span v-else>{{index+1}}.</span>
                  {{ item.title }}
                </p>
                <div class="hotspot-btn">
                  <a-button type="primary" v-if="item.title!==props.hotspotDrawerData.hotspot.title" class="hotspot-write" @click="selectHotspot(item)">结合该热点，去创作</a-button>
                  <span :class="{'hotspot-hot':item.title!==props.hotspotDrawerData.hotspot.title}">{{ hotNum(item.hot) }}</span>
                </div>
              </div>
              <!-- <p class="hotspot-desc">雷军建议优化新能源车牌颜色，拓展智能化功能。</p> -->
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="微博">
          <div class="scroll-box" ref="tabsRef">
            <div class="hotspot-list-box" :class="{'hotspot-list-activity':item.title===props.hotspotDrawerData.hotspot.title}" v-for="(item,index) in hotspotData" :key="item.id">
              <div class="hotspot-title-box">
                <p class="hotspot-title">
                  <img v-if="index===0" src="../../../assets/image/no.1.png" alt="">
                  <img v-else-if="index===1" src="../../../assets/image/no.2.png" alt="">
                  <img v-else-if="index===2" src="../../../assets/image/no.3.png" alt="">
                  <span v-else>{{index+1}}.</span>
                  {{ item.title }}
                </p>
                <div class="hotspot-btn">
                  <a-button type="primary" v-if="item.title!==props.hotspotDrawerData.hotspot.title" class="hotspot-write" @click="selectHotspot(item)">结合该热点，去创作</a-button>
                  <span :class="{'hotspot-hot':item.title!==props.hotspotDrawerData.hotspot.title}">{{ hotNum(item.hot) }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" tab="腾讯">
          <div class="scroll-box" ref="tabsRef">
            <div class="hotspot-list-box" :class="{'hotspot-list-activity':item.title===props.hotspotDrawerData.hotspot.title}" v-for="(item,index) in hotspotData" :key="item.id">
              <div class="hotspot-title-box">
                <p class="hotspot-title">
                  <img v-if="index===0" src="../../../assets/image/no.1.png" alt="">
                  <img v-else-if="index===1" src="../../../assets/image/no.2.png" alt="">
                  <img v-else-if="index===2" src="../../../assets/image/no.3.png" alt="">
                  <span v-else>{{index+1}}.</span>
                  {{ item.title }}
                </p>
                <div class="hotspot-btn">
                  <a-button type="primary" class="hotspot-write" @click="selectHotspot(item)">结合该热点，去创作</a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="抖音">
          <div class="scroll-box" ref="tabsRef">
            <div class="hotspot-list-box" :class="{'hotspot-list-activity':item.title===props.hotspotDrawerData.hotspot.title}" v-for="(item,index) in hotspotData" :key="item.id">
              <div class="hotspot-title-box">
                <p class="hotspot-title">
                  <img v-if="index===0" src="../../../assets/image/no.1.png" alt="">
                  <img v-else-if="index===1" src="../../../assets/image/no.2.png" alt="">
                  <img v-else-if="index===2" src="../../../assets/image/no.3.png" alt="">
                  <span v-else>{{index+1}}.</span>
                  {{ item.title }}
                </p>
                <div class="hotspot-btn">
                  <a-button type="primary" v-if="item.title!==props.hotspotDrawerData.hotspot.title" class="hotspot-write" @click="selectHotspot(item)">结合该热点，去创作</a-button>
                  <span :class="{'hotspot-hot':item.title!==props.hotspotDrawerData.hotspot.title}">{{ hotNum(item.hot) }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" tab="头条">
          <div class="scroll-box" ref="tabsRef">
            <div class="hotspot-list-box" :class="{'hotspot-list-activity':item.title===props.hotspotDrawerData.hotspot.title}" v-for="(item,index) in hotspotData" :key="item.id">
              <div class="hotspot-title-box">
                <p class="hotspot-title">
                  <img v-if="index===0" src="../../../assets/image/no.1.png" alt="">
                  <img v-else-if="index===1" src="../../../assets/image/no.2.png" alt="">
                  <img v-else-if="index===2" src="../../../assets/image/no.3.png" alt="">
                  <span v-else>{{index+1}}.</span>
                  {{ item.title }}
                </p>
                <div class="hotspot-btn">
                  <a-button type="primary" v-if="item.title!==props.hotspotDrawerData.hotspot.title" class="hotspot-write" @click="selectHotspot(item)">结合该热点，去创作</a-button>
                  <span :class="{'hotspot-hot':item.title!==props.hotspotDrawerData.hotspot.title}">{{ hotNum(item.hot) }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="6" tab="最近节日">
          <div class="calendar-titlr">
            <span class="calendar-text">日历</span>
            <span class="current-month" @click="scrollTopMonth">回到本月</span>
          </div>
          <div class="scroll-box calendar-scroll-box" ref="tabsRef">
            <div class="calendar-box" v-for="item in hotspotData" :key="item.month">
              <p class="calendar-month">{{ item.month }}月</p>
                <div class="hotspot-festival" >
                  <div class="festival-list" 
                    :class="{'festival-list-activity':festInfo.festivalDate===props.hotspotDrawerData.festival.festivalDate}" 
                    v-for="festInfo in item.festivalInfo" :key="festInfo.festivalDate">
                    <p class="festival-top">
                      <span>{{ festInfo.festivalDate }}</span>
                      <span class="bold-text">{{ festInfo.name }}</span>
                    </p>
                    <p class="select-festival" v-if="festInfo.festivalDate!==props.hotspotDrawerData.festival.festivalDate">
                      <span @click="selectFestival(festInfo)">选择该节日</span>
                    </p>
                    <p>
                      <span class="keyword-bold-text">关键词：</span>
                      <br>
                      <span>{{ festInfo.keyword }}</span>
                    </p>
                  </div>
                  <div class="festival-list festival-none" v-if="item.festivalInfo.length % 2"></div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    </a-drawer>
  </div>
</template>
<script setup>
import { message } from "ant-design-vue";
import { getHotspot, getFestival } from "@/api/intelligentTopicSelect";
import emitter from "@/assets/js/emitter";
const tabsRef = ref();
const props = defineProps({
  hotspotDrawerData: Object,
});
console.log(props)
const modalLoading = ref(false);
const activeKey = ref('1');
const onClose = () => {
  props.hotspotDrawerData.drawerOpen = false;
};
//获取热榜
const params = {
  pageNum: 1,
  pageSize: '20',
  platform: '1'
}
const hotspotData = ref([]);
const getHotspotList = async ()=> {
  if(params.pageNum === 0) {
    return false
  }
  let res = {};
  modalLoading.value = true;
  if(activeKey.value === '6') {
    res = await getFestival(params);
  } else {
    res = await getHotspot(params);
  }
  modalLoading.value = false;
  if (res.retCode === "000000") {
    params.pageNum = res.data.nextPage;
    hotspotData.value.push(...res.data.list);
    if(activeKey.value === '6') {
      hotspotData.value.forEach((el,i) => {
        if(i > 0 && el.month === hotspotData.value[i-1].month){
          hotspotData.value[i-1].festivalInfo.push(...el.festivalInfo)
          delete hotspotData.value.splice(i, 1)
        }
      });
    }
    nextTick(()=>{
      if (tabsRef.value) {
        tabsRef.value.addEventListener('scroll', handleScroll);
      }
    })
  } else {
    message.error(res.retMsg);
  }
};
// 定义滚动函数
const handleScroll = () => {
	const tableContainer = tabsRef.value;
	const scrollPosition = tableContainer.scrollTop;
	const isTop = scrollPosition === 0;
	const isBottom = tableContainer.scrollHeight - scrollPosition === tableContainer.clientHeight;
	if (isBottom) {
    getHotspotList();
	}
}
// 移除scroll监听
onUnmounted(()=>{
	nextTick(()=>{
		if (tabsRef.value) {
			tabsRef.value.addEventListener('scroll', handleScroll);
		}
	})
})
// 切换tabs
const tabsToggle = (value)=> {
  params.pageNum = 1;
  hotspotData.value = [];
  params.platform = value;
  getHotspotList()
}
//选择热点
const selectHotspot = (item) => {
  emitter.emit("selectHotspot", item);
}
//选择节日
const selectFestival = (item) => {
  emitter.emit("selectFestival", item);
}
//回到本月
const scrollTopMonth = ()=> {
  nextTick(()=>{
    if (tabsRef.value) {
      tabsRef.value.scrollTo({ top: 0, behavior: 'smooth' });
    }
  })
} 
function hotNum(hot) {
    if (hot === " ") {
      return null;
    } else {
        const i = /\d+/g, t = hot.match(i).map(Number)[0];
        return t >= 10000 ? (t / 10000).toFixed(0).replace(/\.0$/, "") + "万" : t
    }
}
onMounted(()=>{
  getHotspotList()
})
</script>

<style lang="less" scoped>
.hotspot-page {
  .drawer-title {
    font-size: 14px;
    color: #1E293B;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .scroll-box {
    height: calc(100vh - 125px);
    overflow-y: auto;
  }
  .calendar-scroll-box {
    height: calc(100vh - 150px);
  }
  .hotspot-list-box {
    background: #FFF;
    border: 1px solid #E2E8F0;
    border-radius: 8px;
    padding: 5px 10px;
    margin-bottom: 20px;
  }
  .hotspot-list-activity {
    background: #FE8308;
    border: 1px solid #FE8308;
    .hotspot-title,.hotspot-btn {
      color: #fff;
      span {
        color: #fff;
      }
    }
  }
  .hotspot-title-box {
    height: 26px;
    display: flex;
    align-items: center;
    // margin-bottom: 5px;
  }
  .hotspot-btn {
    width: 160px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .hotspot-write {
      display: none;
      align-items: center;
    }
    .hotspot-hot {
      display: block;
    }
  }
  .hotspot-list-box:hover {
    border: 1px solid #FE8308;
    .hotspot-write {
      display: flex;
    }
    .hotspot-hot {
      display: none;
    }
  }
  .hotspot-details {
    width: 70px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    color: #1E293B;
    background: #FFF;
    border: 1px solid #E2E8F0;
    border-radius: 15px;
    padding: 0;
    display: flex;
    justify-content: center;
    box-shadow: none;
    cursor: pointer;
  }
  .hotspot-write {
    line-height: 24px;
    height: 24px;
    color: #fff;
    font-size: 12px;
    background: #FE8308;
    border-radius: 15px;
    margin-left: 10px;
    box-shadow: none;
    cursor: pointer;
  }
  .hotspot-title {
    flex: 1;
    font-weight: bold;
    font-size: 14px;
    color: #1E293B;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    span {
      width: 25px;
      display: inline-block;
      color: #FE8308;
      text-align: center;
    }
    img {
      width: 20px;
      vertical-align: bottom;
      margin-right: 5px;
    }
  }
  .hotspot-desc {
    font-size: 12px;
    color: #1E293B;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  :deep(.ant-drawer-body) {
    padding: 20px;
  }
  :deep(.ant-tabs-top >.ant-tabs-nav::before) {
    border: 0;
  }
  :deep(.ant-tabs .ant-tabs-ink-bar) {
    background-color: transparent;
  }
  :deep(.ant-tabs .ant-tabs-tab+.ant-tabs-tab) {
    margin: 0;
    border: 0;
  }
  :deep(.ant-tabs .ant-tabs-tab) {
    padding: 5px 20px;
    background: #F8F4F1;
    border-radius: 4px 4px 0 0;
  }
  :deep(.ant-tabs .ant-tabs-tab-active) {
    background: #FE8308;
  }
  :deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    color: #fff;
    text-shadow:none;
  }
  .calendar-titlr {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #1E293B;
  }
  .calendar-text {

  }
  .current-month {
    padding: 2px 10px;
    background: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 17px;
    cursor: pointer;
  }
  .calendar-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
  }
  .calendar-month {
    font-weight: bold;
    font-size: 14px;
    color: #1E293B;
    margin-bottom: 10px;
  }
  .festival-list {
    width: 238px;
    height: 110px;
    padding: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: rgba(254, 131, 8, .05);
    border-radius: 8px;
    font-size: 12px;
    color: #1E293B;
    margin-bottom: 20px;
  }
  .festival-list-activity {
    background: #FE8308;
    color: #fff;
  }
  .festival-none {
    visibility: hidden;
  }
  
  .hotspot-festival {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
  }

  .festival-top {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
  .bold-text {
    font-weight: bold;
    padding-left: 10px;
    text-align: justify;
  }
  .keyword-bold-text {
    font-weight: bold;
  }
  .select-festival {
    position: absolute;
    right: 10px;
    top: 35px;
    span {
      display: none;
      padding: 5px 10px;
      border-radius: 17px;
      cursor: pointer;
      background: #FE8308;
      color: #fff;
    }
  }
  .festival-list:hover {
    .select-festival {
      span {
        display: block;
      }
    }
  }
}
</style>
