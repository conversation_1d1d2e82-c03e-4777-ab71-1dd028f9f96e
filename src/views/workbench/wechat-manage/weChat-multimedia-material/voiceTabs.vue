<!-- 音频 -->
<!-- 图文 -->
<template>
  <div class="tabs-content">
    <a-table
      :dataSource="props.initTabs.dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="props.initTabs.pagination"
      @change="handleTableChange"
      :loading="props.initTabs.pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span @click="previewAudio(record, index)">试听</span>
            <span @click="copyMaterial(record.localUrl)">复制</span>
            <a
              class="icon-download"
              title="下载"
              :href="record.localUrl"
              :download="record.templateName"
            >
              <span>下载</span>
            </a>
            <span class="del-btn" @click="deleteMaterial(record.id)">删除</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <!-- 音频预览弹窗 -->
  <audio
    src="https://freetyst.nf.migu.cn/public%2Fproduct9th%2Fproduct45%2F2021%2F12%2F0713%2F2021%E5%B9%B408%E6%9C%8802%E6%97%A501%E7%82%B914%E5%88%86%E7%B4%A7%E6%80%A5%E5%86%85%E5%AE%B9%E5%87%86%E5%85%A5%E5%8D%8E%E7%BA%B312%E9%A6%96634254%2F%E5%85%A8%E6%9B%B2%E8%AF%95%E5%90%AC%2FMp3_64_22_16%2F6005752RJFG134920.mp3?Key=45950b9748f1ba3d&Tim=1736839393258&channelid=01&msisdn=609113c39bd348aea99c75db36d218ba"
    class="musicBox"
    ref="musicBox"
  ></audio>
  <section class="audioPreview" v-if="audioPreViewPop.state">
    <div class="box">
      <div class="top">
        <!-- ../../../../assets/image/closeBtn.png -->
        <img
          @click="hidePreview"
          src="../../../../assets/image/closeBtn.png"
          alt=""
          class="closeBtn"
        />
        <img src="../../../../assets/image/needle.png" class="needle" alt="" />
        <img src="../../../../assets/image/record.png" alt="" class="record" />
      </div>
      <div class="progress">
        <h3 class="singerName">
          {{ props.initTabs.dataSource[audioPreViewPop.index].templateName }}
        </h3>
        <div class="progressBox">
          <div class="lineBox">
            <div class="line" ref="audioLine"><span class="bar"></span></div>
          </div>
          <span class="currentTime">{{ audioPreViewPop.currentTime }}</span>
          <span class="duration">{{ audioPreViewPop.duration }}</span>
        </div>
      </div>

      <div class="control">
        <em @click="audioPreBtn" class="icon-preBtn"></em>
        <em
          @click="audioPlayBtn"
          ref="audioPlay"
          :class="{
            'icon-playBtn': audioPreViewPop.audioPlayClass,
            'icon-pauseBtn': !audioPreViewPop.audioPlayClass,
          }"
        ></em>
        <em @click="audioNextBtn" class="icon-nextBtn"></em>
      </div>
    </div>
  </section>
</template>

<script setup>
import emitter from "@/assets/js/emitter";
const props = defineProps({
  initTabs: Object,
});
const tableColumns = reactive([
  {
    title: "名称",
    dataIndex: "templateName",
  },
  {
    title: "创建时间",
    dataIndex: "updateTime",
  },
  {
    title: "操作",
    dataIndex: "operate",
    width: 180,
    fixed: "right",
  },
]);
const handleTableChange = (pageNo, pageSize) => {
  // 点击分页处理
  props.initTabs.pageLoading = true;
  emitter.emit("handleTableChange", { pageNo, pageSize });
};
//删除
const deleteMaterial = (id) => {
  emitter.emit("deleteMaterial", id);
};
//复制
const copyMaterial = (text) => {
  emitter.emit("copyMaterial", text);
};
// 音频预览弹窗
let audioPreViewPop = reactive({
  state: false,
  audioPlayClass: true,
  index: 0,
  currentTime: "00:00",
  duration: "00:00",
});
let avTimer = reactive(null);
const musicBox = ref();
const audioLine = ref();
// 隐藏预览
function hidePreview() {
  audioPreViewPop.state = false;
  audioPreViewPop.audioPlayClass = true;
  clearInterval(avTimer);
}
// 音频预览
function previewAudio(item, _index) {
  audioPreViewPop.index = _index;
  audioPreViewPop.state = true;
  // musicBox.value.src = item.localUrl;
  console.log(musicBox.value.src);
}
// 音频上一首
function audioPreBtn() {
  if (audioPreViewPop.index <= 1) {
    return false;
  }
  audioPreViewPop.audioPlayClass = false;
  audioPreViewPop.index--;
  songSwitch();
}
// 音乐下一首
function audioNextBtn() {
  if (audioPreViewPop.index < props.initTabs.dataSource.length) {
    audioPreViewPop.audioPlayClass = false;
    audioPreViewPop.index++;
    songSwitch();
  }
}
// 歌曲切换数据初始化
function songSwitch() {
  clearInterval(avTimer);
  musicBox.value.src =
    props.initTabs.dataSource[audioPreViewPop.index].localUrl;
  musicBox.value.play();
  audioTimerHandel();
  audioPreViewPop.duration = "00:00:00";
  audioPreViewPop.currentTime = "00:00:00";
  audioLine.value.style.width = "0%";
}
// 音乐播放暂停
function audioPlayBtn() {
  if (musicBox.value.paused) {
    musicBox.value.play();
    audioPreViewPop.audioPlayClass = false;
    audioTimerHandel();
  } else {
    musicBox.value.pause();
    audioPreViewPop.audioPlayClass = true;
    clearInterval(avTimer);
  }
}
// 音频实时 时间
function audioTimerHandel() {
  clearInterval(avTimer);
  avTimer = setInterval(() => {
    audioPreViewPop.duration = formatTime(parseInt(musicBox.value.duration));
    audioPreViewPop.currentTime = formatTime(
      parseInt(musicBox.value.currentTime)
    );
    audioLine.value.style.width =
      ((musicBox.value.currentTime / musicBox.value.duration) * 100).toFixed(
        3
      ) + "%";
  }, 500);
}
// 时间转换
function formatTime(time, state) {
  let hh, mm, ss;
  //传入的时间为空或小于0
  if (time == null || time < 0 || isNaN(time)) {
    clearInterval(this.avTimer);
    return "00:00:00";
  }
  //得到小时
  hh = (time / 3600) | 0;
  time = parseInt(time) - hh * 3600;
  if (parseInt(hh) < 10) {
    hh = "0" + hh;
  }
  //得到分
  mm = (time / 60) | 0;
  //得到秒
  ss = parseInt(time) - mm * 60;
  if (parseInt(mm) < 10) {
    mm = "0" + mm;
  }
  if (ss < 10) {
    ss = "0" + ss;
  }
  return hh + ":" + mm + ":" + ss;
}
</script>
<style lang="less" scoped>
.tabs-content {
  height: calc(100vh - 190px);
  overflow-y: scroll;
  background: #fff;
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .tabs-list {
    display: flex;
    padding: 10px;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .list-item {
    width: 200px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    ::deep(.ant-image) {
      background: red;
    }
  }
  ::deep(.ant-image-img) {
    width: 200px;
  }
  .tabs-pagination {
    display: flex;
    justify-content: flex-end;
    padding-right: 10px;
    padding-bottom: 10px;
  }
  .operate-box,.icon-download {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
.audioPreview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
  .box {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 315px;
    background: #fff;
    border-radius: 5px;
    transform: translate(-50%, -50%);

    .closeBtn {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 20px;
      height: 20px;
      cursor: pointer;
      pointer-events: auto;
    }

    .needle {
      position: relative;
      z-index: 4;
      width: 121px;
      margin: 12px 0 0 12px;
    }

    .record {
      position: relative;
      z-index: 3;
      width: 190px;
      margin: -44px 0 0 62px;
    }

    .progress {
      h3 {
        font-size: 14px;
        color: #636363;
        text-align: center;
      }

      .progressBox {
        overflow: hidden;
        position: relative;
        margin-top: 16px;
        padding: 0 18px 4px 18px;

        .lineBox {
          position: relative;
          width: 280px;
          height: 4px;
          margin: 0 auto;
          border-radius: 100px;
          background: #d8d8d8;

          .line {
            position: absolute;
            left: 0;
            top: 0;
            width: 0%;
            height: 100%;
            background: #fe8308;
            border-radius: 100px;

            .bar {
              position: absolute;
              left: 100%;
              top: 2px;
              display: inline-block;
              width: 8px;
              height: 8px;
              margin-left: -8px;
              margin-top: -4px;
              background: #ffffff;
              border: 1px solid #c6c6c6;
              border-radius: 100%;
            }
          }
        }

        .currentTime {
          font-size: 12px;
          color: #9c9c9c;
        }

        .duration {
          float: right;
          font-size: 12px;
          color: #9c9c9c;
        }
      }
    }

    .control {
      position: relative;
      top: 1px;
      background: #262f3e;
      line-height: 54px;
      height: 54px;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      text-align: center;

      .icon-preBtn {
        display: inline-block;
        width: 19px;
        height: 19px;
        background: url(../../../../assets/image/icon-pre.png) no-repeat;
        background-size: 100% 100%;
        vertical-align: middle;
        cursor: pointer;
        pointer-events: auto;
      }

      .icon-playBtn {
        display: inline-block;
        width: 16px;
        height: 24px;
        background: url(../../../../assets/image/icon-play.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 58px;
        vertical-align: middle;
        cursor: pointer;
        pointer-events: auto;
      }

      .icon-pauseBtn {
        display: inline-block;
        width: 16px;
        height: 24px;
        background: url(../../../../assets/image/icon-pause.png) no-repeat;
        background-size: 100% 100%;
        margin: 0 58px;
        vertical-align: middle;
        cursor: pointer;
        pointer-events: auto;
      }

      .icon-nextBtn {
        display: inline-block;
        width: 19px;
        height: 19px;
        background: url(../../../../assets/image/icon-next.png) no-repeat;
        background-size: 100% 100%;
        vertical-align: middle;
        cursor: pointer;
        pointer-events: auto;
      }
    }
  }
}
</style>
