<!-- 排期 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-date-picker
        :allowClear="false"
        picker="month"
        placeholder="请选择时间"
        v-model:value="searchForm.searchDate"
      />
      <a-button
        type="primary"
        :disabled="pageLoading"
        @click="searchFun(true)"
        class="submit-btn"
      >
        <template #icon>
          <SearchOutlined />
        </template>
        查询
      </a-button>
    </div>
  </div>
  <div class="work-list">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      :pagination="false"
      ref="tableRef"
      size="small"
      bordered
      :loading="pageLoading"
      :scroll="{ y: 500, hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'scheduleDateName'">
          <div class="schedule-date-name">
            {{ record.scheduleDateName }} <br />
            <template v-if="record.nodeName">
              <span class="node-name">{{ record.nodeName }}</span>
            </template>
            <template v-else>
              <span v-if="record.scheduleWeek === 1">星期日</span>
              <span v-if="record.scheduleWeek === 2">星期一</span>
              <span v-if="record.scheduleWeek === 3">星期二</span>
              <span v-if="record.scheduleWeek === 4">星期三</span>
              <span v-if="record.scheduleWeek === 5">星期四</span>
              <span v-if="record.scheduleWeek === 6">星期五</span>
              <span v-if="record.scheduleWeek === 7">星期六</span>
            </template>
          </div>
        </template>
        <template
          v-if="column.dataIndex === 'businessName' && record.scheduleInfo"
        >
          <div
            class="td-list pd-list"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            {{ item.businessName }}
          </div>
        </template>
        <template
          v-if="column.dataIndex === 'secondTagName' && record.scheduleInfo"
        >
          <div
            class="td-list pd-list"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            {{ item.secondTagName }}
          </div>
        </template>
        <template
          v-if="column.dataIndex === 'demandPort' && record.scheduleInfo"
        >
          <div
            class="td-list pd-list"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            {{ item.demandPort }}
          </div>
        </template>
        <template
          v-if="
            column.dataIndex === 'businessLiaisonPerson' && record.scheduleInfo
          "
        >
          <div
            class="td-list pd-list"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            {{ item.businessLiaisonPerson }}
          </div>
        </template>
        <template
          v-if="column.dataIndex === 'dutyEditor' && record.scheduleInfo"
        >
          <div
            class="td-list pd-list"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            {{ item.dutyEditor }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'platform' && record.scheduleInfo">
          <div
            class="td-list platform-icon"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            <a-tooltip placement="top" v-if="item.weChatAccountId" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.weChatAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-wechat.png" alt="" />
            </a-tooltip>
            <a-tooltip placement="top" v-if="item.weiboAccountId" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.weiboAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-weibo.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="item.redNoteAccountId"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.redNoteAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-xiaohongshu.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="item.touTiAoAccountId"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.touTiAoAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-toutiao.png" alt="" />
            </a-tooltip>
            <a-tooltip placement="top" v-if="item.tikTokAccountId" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.tikTokAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-douyin.png" alt="" />
            </a-tooltip>
            <a-tooltip placement="top" v-if="item.videoAccountId" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.videoAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-video.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="item.kuAiShouAccountId"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.kuAiShouAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-kuaishou.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="item.otherAccountId"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{
                  item.otherAccountName.join(",")
                }}</span>
              </template>
              <img src="../../../assets/image/icon-other.png" alt="" />
            </a-tooltip>
          </div>
        </template>
        <template
          v-if="column.dataIndex === 'auditTrail' && record.scheduleInfo"
        >
          <div
            class="audit-img-box td-list"
            :class="{ 'audit-img-dot': item.finalAuditUrl }"
            v-for="item in record.scheduleInfo"
            :key="item.id"
          >
            <a-steps
              :current="
                item.finalAuditUrl
                  ? 2
                  : item.secondAuditUrl
                  ? 1
                  : item.firstAuditUrl
                  ? 0
                  : null
              "
              :items="[
                {
                  title: '',
                },
                {
                  title: '',
                },
                {
                  title: '',
                },
              ]"
            >
              <template #progressDot="{ index, status, prefixCls }">
                <a-popover>
                  <template #content>
                    <span v-if="index === 0">初审</span>
                    <span v-if="index === 1">二审</span>
                    <span v-if="index === 2">终审</span>
                  </template>
                  <span
                    :class="`${prefixCls}-icon-dot`"
                    @click="showUploadImgModal(item, index, status)"
                  />
                </a-popover>
              </template>
            </a-steps>
            <a-button
              class="upload-audit-btn"
              v-if="!item.finalAuditUrl"
              @click="showUploadImgModal(item, null, 'process')"
            >
              <upload-outlined></upload-outlined>
              上传图片
            </a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'operate' && record.scheduleInfo">
          <div
            class="operate-box td-list pd-list"
            v-for="(item, index) in record.scheduleInfo"
            :key="item.id"
          >
            <span @click="showScheduleModal(item, record)">编辑</span>
            <span class="del-btn" v-if="item.id" @click="delFun(item)"
              >删除</span
            >
            <PlusOutlined
              @click="showScheduleModal(null, record)"
              v-if="index == 0"
            />
          </div>
        </template>
        <template
          v-else-if="column.dataIndex === 'operate' && !record.scheduleInfo"
        >
          <div class="operate-box">
            <span @click="showScheduleModal(null, record)">排期</span>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <scheduleForm v-if="scheduleData.modalOpen" :scheduleData="scheduleData" />
  <uploadImg v-if="uploadImgData.modalOpen" :uploadImgData="uploadImgData" />
</template>
<script setup>
import { message } from "ant-design-vue";
import * as workUrl from "@/api/conOprWork";
import emitter from "@/assets/js/emitter";
import useDel from "@/hooks/useDel";
import scheduleForm from "./scheduleForm.vue";
import uploadImg from "./uploadImg.vue";
import dayjs from "dayjs";
const props = defineProps({
  scheduleData: Object,
});
const tableRef = ref();
const pageLoading = ref(false);
const tableColumns = reactive([
  {
    title: "日期",
    dataIndex: "scheduleDateName",
    ellipsis: true,
    width: 80,
    fixed: "left",
  },
  {
    title: "内容描述",
    dataIndex: "businessName",
    ellipsis: true,
    width: 220,
  },
  {
    title: "内容标签",
    dataIndex: "secondTagName",
    width: 100,
  },
  {
    title: "需求口",
    dataIndex: "demandPort",
    width: 100,
  },
  {
    title: "对接人",
    dataIndex: "businessLiaisonPerson",
    width: 80,
  },
  {
    title: "责任编辑",
    dataIndex: "dutyEditor",
    width: 80,
  },
  {
    title: "审核留痕",
    dataIndex: "auditTrail",
    width: 300,
  },
  {
    title: "发布平台",
    dataIndex: "platform",
    width: 240,
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    width: 130,
    fixed: "right",
  },
]);
let dataSource = ref([]);
//搜索数据
const searchForm = reactive({
  searchDate: "",
});
// 获取列表数据
const getScheduleList = async (data, type = "false", addEditItem = null) => {
  pageLoading.value = true;
  const res = await workUrl.scheduleList(data);
  pageLoading.value = false;
  if (res.retCode === "000000") {
    if (type === "loadM") {
      dataSource.value.push(...res.data);
    } else if (type === "addEdit") {
      res.data.forEach((el, i) => {
        if (el.scheduleDate === addEditItem.item.scheduleDate) {
          dataSource.value.forEach((item, j) => {
            if (item.scheduleDate === el.scheduleDate) {
              dataSource.value[j].scheduleInfo = el.scheduleInfo;
            }
          });
        }
      });
    } else {
      dataSource.value = res.data;
      nextTick(() => {
        if (tableRef.value) {
          const tableContainer =
            tableRef.value.$el.querySelector(".ant-table-body");
          tableContainer.addEventListener("scroll", handleScroll);
        }
      });
    }
  } else {
    message.error(res.retMsg);
  }
};
//初始化页面底层数据
let params = {}; //列表查询参数
const getScheduleInit = async () => {
  pageLoading.value = true;
  const res = await workUrl.scheduleInit();
  pageLoading.value = false;
  if (res.retCode === "000000") {
    const monthStr =
      res.data.month < 10 ? "0" + res.data.month : res.data.month.toString();
    searchForm.searchDate = dayjs(res.data.year + "-" + monthStr, "YYYY-MM");
    params = {
      year: res.data.year,
      month: res.data.month,
    };
    localStorage.setItem("searchDate", JSON.stringify(searchForm.searchDate));
    getScheduleList(params);
  } else {
    message.error(res.retMsg);
  }
};
// 搜索
let loadM = 1;
function searchFun(isSearch) {
  loadM = 1;
  if (localStorage.getItem("searchDate") && !isSearch) {
    let storageDate = JSON.parse(localStorage.getItem("searchDate"));
    searchForm.searchDate = dayjs(storageDate);
    params = {
      year: parseInt(dayjs(storageDate).format("YYYY-MM").split("-")[0]),
      month: parseInt(dayjs(storageDate).format("YYYY-MM").split("-")[1]),
    };
  } else {
    params = {
      year: parseInt(searchForm.searchDate.format("YYYY-MM").split("-")[0]),
      month: parseInt(searchForm.searchDate.format("YYYY-MM").split("-")[1]),
    };
    localStorage.setItem("searchDate", JSON.stringify(searchForm.searchDate));
  }
  nextTick(() => {
    if (tableRef.value) {
      const tableContainer =
        tableRef.value.$el.querySelector(".ant-table-body");
      tableContainer.scrollTop = 0;
      getScheduleList(params);
    }
  });
}

// 定义滚动函数
const handleScroll = () => {
  const tableContainer = tableRef.value.$el.querySelector(".ant-table-body");
  const scrollPosition = tableContainer.scrollTop;
  const isTop = scrollPosition === 0;
  const isBottom =
    tableContainer.scrollHeight - scrollPosition ===
    tableContainer.clientHeight;
  // if (isTop) {
  // 	console.log('重新加载');
  // }
  if (isBottom) {
    //是否超过三年
    loadM++;
    if (loadM > 36) {
      message.error("已达加载数据上限，请重选日期！");
      return false;
    }
    pageLoading.value = true;
    //超过一年自然月赋值为1月；否则加1
    if (params.month === 12) {
      params.month = 1;
      params.year += 1;
      message.warning(`已跨年！当前为${params.year}年`);
    } else {
      params.month++;
    }
    getScheduleList(params, "loadM");
  }
};
//审核图片上传
const uploadImgData = reactive({
  modalOpen: false,
  title: "初审",
  item: {},
});
const showUploadImgModal = (item, num, status) => {
  if (status === "wait") {
    return false;
  }
  let index = null;
  if (num !== null) {
    index = num;
  } else {
    index = !item.firstAuditUrl
      ? 0
      : !item.secondAuditUrl
      ? 1
      : !item.finalAuditUrl
      ? 2
      : null;
  }
  const auditIndex = [
    { url: "/cop/manage/schedule/upload/audit/first", title: "初审" },
    { url: "/cop/manage/schedule/upload/audit/second", title: "二审" },
    { url: "/cop/manage/schedule/upload/audit/final", title: "终审" },
  ];
  if (item.id) {
    uploadImgData.modalOpen = true;
    uploadImgData.title = auditIndex[index].title;
    item.uploadUrl = auditIndex[index].url;
    Object.assign(uploadImgData.item, item);
  } else {
    message.error("请先排期!");
  }
};
//排期/编辑
const scheduleData = reactive({
  modalOpen: false,
  isSchedule: true, //是否为排期
  item: {},
});
const msgData = reactive({
  modalOpen: false,
  item: {},
  scheduleData,
});

const showScheduleModal = (item, record) => {
  scheduleData.modalOpen = true;
  scheduleData.isSchedule = true;
  if (item) {
    Object.assign(scheduleData.item, item);
    scheduleData.item.nodeFlag = record.nodeFlag;
    scheduleData.item.scheduleWeek = record.scheduleWeek;
    scheduleData.item.scheduleDate = record.scheduleDate;
    scheduleData.item.publicAot = item.weChatAccountId || [];
    scheduleData.item.videoAot = item.videoAccountId || [];
    scheduleData.item.weiboAot = item.weiboAccountId || [];
    scheduleData.item.douyinAot = item.tikTokAccountId || [];
    scheduleData.item.xiaohongshuAot = item.redNoteAccountId || [];
    scheduleData.item.kuaishouAot = item.kuAiShouAccountId || [];
    scheduleData.item.toutiaoAot = item.touTiAoAccountId || [];
    scheduleData.item.otherAot = item.otherAccountId || [];
  } else {
    scheduleData.item = {
      secondTag: null,
      businessLiaisonPerson: "",
      dutyEditor: "",
      businessName: "",
      demandPort: "",
      planDesc: "",
      firstTag: null,
      accountId: [],
      publicAot: [],
      videoAot: [],
      weiboAot: [],
      douyinAot: [],
      xiaohongshuAot: [],
      kuaishouAot: [],
      toutiaoAot: [],
      otherAot: []
    };
    scheduleData.item.nodeFlag = record.nodeFlag;
    scheduleData.item.scheduleWeek = record.scheduleWeek;
    scheduleData.item.scheduleDate = record.scheduleDate;
  }
};
//新增/编辑/排期之后更新数据
emitter.on("refreshWork", (value) => {
  uploadImgData.modalOpen = false;

  const params = {
    year: parseInt(scheduleData.item.scheduleDate.split("-")[0]),
    month: parseInt(scheduleData.item.scheduleDate.split("-")[1]),
  };
  getScheduleList(params, "addEdit", scheduleData);
});
// 删除数据
const delFun = async (list) => {
  if (list.id) {
    useDel(workUrl.scheduleDel, { scheduleId: list.id }, pageLoading, (res) => {
      if (res.retCode === "000000") {
        searchFun();
        console.log(list);
        // getScheduleList(params,'addEdit',list)
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    });
  } else {
    message.error("请先排期!");
  }
};

onMounted(() => {
  if (localStorage.getItem("searchDate")) {
    searchFun();
  } else {
    getScheduleInit();
  }
});
// 移除scroll监听
onUnmounted(() => {
  nextTick(() => {
    if (tableRef.value) {
      const tableContainer =
        tableRef.value.$el.querySelector(".ant-table-body");
      tableContainer.removeEventListener("scroll", handleScroll);
    }
  });
});
</script>
<style lang="less" scoped>
.top-operate {
  display: flex;
  margin-bottom: 0;
  position: absolute;
  left: 0;
  top: -38px;
  .search-box,
  .top-box {
    display: flex;
  }
}

.work-list {
  background: #fff;
  height: calc(100vh - 170px);
  border: 1px solid #e2e8f0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .pd-list {
    padding-bottom: 10px;
  }
  .td-list:not(:last-child) {
    margin-bottom: 10px;
  }
  .platform-icon {
    height: 32px;
    img {
      width: 20px;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .audit-img-box {
    position: relative;
    top: -15px;
    left: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .audit-img-dot {
    left: 0;
    padding-bottom: 10px;
    .ant-steps {
      width: 100%;
    }
    :deep(.ant-steps.ant-steps-label-vertical .ant-steps-item) {
      width: 130px;
    }
  }
  .schedule-date-name {
    span {
      color: #1e293b;
      opacity: 0.5;
      font-size: 12px;
    }
    .node-name {
      color: #fe8308;
      opacity: 1;
    }
    .schedule-year {
      position: absolute;
      top: 0px;
      left: 2px;
      color: #fe8308;
      opacity: 0.5;
      font-size: 10px;
    }
  }
  .audit-name,
  .anticon-plus {
    color: #fe8308;
    cursor: pointer;
  }
  .operate-box {
    color: #fe8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
  .ant-steps {
    position: relative;
    left: -65px;
    width: 50%;
  }
  .upload-audit-btn {
    position: relative;
    left: 20px;
  }
  .ant-steps-icon-dot {
    width: 20px !important;
    height: 20px !important;
  }
  .ant-steps :deep(.ant-steps-item .ant-steps-item-icon > .ant-steps-icon) {
    width: 20px;
    height: 20px;
    display: block;
    z-index: 9;
  }
  .ant-steps
    .ant-steps-item-process
    .ant-steps-item-icon
    > .ant-steps-icon
    .ant-steps-icon-dot,
  .ant-steps
    .ant-steps-item-finish
    .ant-steps-item-icon
    > .ant-steps-icon
    .ant-steps-icon-dot {
    background: transparent;
    background-image: linear-gradient(
      270deg,
      #20d1f6 0%,
      rgba(32, 209, 246, 0.5) 99%
    );
    cursor: pointer;
  }
  :deep(.ant-steps.ant-steps-label-vertical .ant-steps-item) {
    width: 60px;
    flex: none;
  }
  :deep(.ant-steps.ant-steps-dot .ant-steps-item-tail) {
    top: 9px;
    left: 10px;
    width: 60%;
  }
  :deep(
      .ant-steps
        .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-tail
    )::after {
    background: transparent;
    background-image: linear-gradient(
      270deg,
      #20d1f6 0%,
      rgba(32, 209, 246, 0.5) 99%
    );
  }
  :deep(
      .ant-steps
        .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-tail
    )::after {
    background-color: rgba(0, 0, 0, 0.15);
  }
  :deep(.ant-steps.ant-steps-dot .ant-steps-item-tail)::after {
    width: calc(100% - 25px);
    height: 1px;
    margin-inline-start: 24px;
  }
}
</style>
<style lang="less">
.ant-tooltip .ant-tooltip-inner {
  .ant-work-tooltip {
    color: #222;
  }
}
</style>