import { message } from "ant-design-vue";
import $https from "@/assets/js/request"
// 获取页面表格数据
export default async function useUpload(file, loading, callback, uploadUrl='/cop/common/file/upload') {
  loading.value = true;
  const uploadForm = new FormData;
  uploadForm.append("file", file);
  const p = {
    headers: {
      "Content-Type": "multipart/form-data; charset=utf-8"
    }
  }, E = $https.axios.create({
    withCredentials: true
  });
    loading.value = false;
    E.post(`${uploadUrl}?type=image&replayTime=${new Date().getTime()}`, uploadForm, p).then(w => {
        w.data.retCode === "000000" ? typeof callback == "function" && callback(w) : message.error(w.data.retMsg)
      }
    )
}