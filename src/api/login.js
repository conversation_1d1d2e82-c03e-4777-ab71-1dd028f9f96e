import $https from "@/assets/js/request"
import { encryptionMobile } from "@/assets/js/utils";
// 是否登录
export const isLogin = () => {
    return $https.fetchGet('/cop/syt/isLogin')
}
// 获取验证码
export const sendcode = ({mobile,randCode}) => {
    return $https.fetchPost(
        `/cop/basic/comm/scyd/login/sendcode`,
        {
            platformId: "ContentOperationPlatform",
            mobile: encodeURI(encryptionMobile(mobile)),
            rmRc: 0,
            randCode: encodeURI(encryptionMobile(randCode))
        }
    )
}
// 登录
export const login = ({mobile,smsCode,randCode}) => {
    return $https.fetchPost(
        `/cop/syt/login`,
        {
            platformId: "ContentOperationPlatform",
            mobile: encodeURI(encryptionMobile(mobile)),
            smsCode: encodeURI(encryptionMobile(smsCode)),
            randCode: encodeURI(encryptionMobile(randCode))
        }
    )
}