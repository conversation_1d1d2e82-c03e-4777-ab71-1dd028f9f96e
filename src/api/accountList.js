import $https from "@/assets/js/request"

// 列表
export const accountPage = (data) => {
    return $https.fetchGet('/cop/manage/account/page',data)
}
// 省级部门下拉数据
export const userProvince = (data) => {
    return $https.fetchGet('/cop/manage/user/province',data)
}
// 新增
export const accountAdd = (data) => {
    return $https.fetchPost('/cop/manage/account/add',data)
}
// 编辑
export const accountEdit = (data) => {
    return $https.fetchPost('/cop/manage/account/edit',data)
}
// 运营归口数据
export const deptList = (data) => {
    return $https.fetchGet('/cop/manage/dept/list',data)
}
// 运营人员下拉
export const allUser = (data) => {
    return $https.fetchGet('/cop/manage/user/all/select',data)
}
// 信息维护
export const editOpr = (data) => {
    return $https.fetchPost('/cop/manage/account/edit/opr',data)
}
// 信息维护-获取粉丝数
export const fansCount = (data) => {
    return $https.fetchGet('/cop/manage/account/edit/fansCount',data)
}
// 信息维护-获取在网内容数
export const contentCount = (data) => {
    return $https.fetchGet('/cop/manage/account/edit/contentCount',data)
}
// 信息维护-获取更新频率
export const editHz = (data) => {
    return $https.fetchGet('/cop/manage/account/edit/hz',data)
}
// 导出数据
export const accountExport = (data) => {
    return $https.fetchGet('/cop/manage/account/page/export',data,{responseType: 'blob'})
}