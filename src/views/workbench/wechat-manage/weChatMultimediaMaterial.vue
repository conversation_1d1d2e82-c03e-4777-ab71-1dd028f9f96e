<!-- 多媒体素材 -->
<template>
  <div class="content-tag">
    <a-tabs
      @change="getNewsList"
      v-model:activeKey="activeKey"
      destroyInactiveTabPane>
      <a-tab-pane key="image" tab="图片">
        <imageTabs :initTabs="initTabs" :pagination="pagination"/>
      </a-tab-pane>
      <a-tab-pane key="news" tab="图文">
        <newsTabs :initTabs="initTabs" />
      </a-tab-pane>
      <a-tab-pane key="voice" tab="音频">
        <voiceTabs :initTabs="initTabs" />
      </a-tab-pane>
      <a-tab-pane key="video" tab="视频">
        <videoTabs :initTabs="initTabs" />
      </a-tab-pane>
      <template #rightExtra>
        <div class="top-operate">
      <div class="search-box">
          <a-input
            v-model:value="searchForm.templateName"
            placeholder="请输入素材名称"
          />
          <a-button type="primary" 
            @click="searchFun" 
            class="search-btn">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
        <weChatConfig />
      </div>
      <a-button :disabled="!isSyncMateial" type="primary" @click="syncMateial()" class="add-btn">
        <template #icon>
          <SyncOutlined v-if="isSyncMateial" :style="{ fontSize: '16px'}" />
          <LoadingOutlined v-else :style="{ fontSize: '16px'}"/>
        </template>
        同步素材到本地
      </a-button>
      <a-button type="primary" @click="showModal()" class="add-btn">
        <template #icon>
          <PlusOutlined />
        </template>
        新增素材
      </a-button>
    </div>
      </template>
    </a-tabs>
  </div>
  <addMaterial v-if="addEditData.modalOpen" :addEditData="addEditData" />
</template>
<script setup>
import { newsList, ukwDel, syncMaterial  } from "@/api/weChatMultimediaMaterial";
import addMaterial from "./weChat-multimedia-material/add.vue";
import weChatConfig from "@/components/weChat-config/weChatConfig.vue";
import useGetList from "@/hooks/useGetList";
import useDel from "@/hooks/useDel";
import { message } from "ant-design-vue";
import imageTabs from "./weChat-multimedia-material/imageTabs.vue";
import newsTabs from "./weChat-multimedia-material/newsTabs.vue";
import voiceTabs from "./weChat-multimedia-material/voiceTabs.vue";
import videoTabs from "./weChat-multimedia-material/videoTabs.vue";
import emitter from "@/assets/js/emitter";
const activeKey = ref(null);
let dataSource = ref([]);
//业务标签数据
const initTabs = ref({
  dataSource: [],
  addMultimedia: "新增多媒体素材",
  editMultimedia: "编辑多媒体素材",
  pageLoading: false,
  pagination: {}
});
const pageLoading = ref(false);
const weChatId = ref("");
//搜索表单数据
const searchForm = ref({
  templateName: ''
});
// 分页参数
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
async function getNewsList(value) {
  useGetList(newsList, pageLoading, dataSource, pagination, {
    chkCurWx: weChatId.value,
    pageNo:1,
    pageSize:10,
    templateType: value
  },()=>{
    initTabs.value.dataSource = dataSource.value;
    initTabs.value.pagination = pagination;
    initTabs.value.pageLoading = false;
  });
}
//同步素材
const isSyncMateial = ref(true)
async function syncMateial() {
  isSyncMateial.value = false;
  const res = await syncMaterial({},weChatId.value);
  isSyncMateial.value = true;
  if (res.retCode === "000000") {
    message.success(res.retMsg);
  } else {
    message.error(res.retMsg);
  }
}
//查询列表
const searchFun = () => {
  useGetList(newsList, pageLoading, dataSource, pagination,{ 
    ...searchForm.value,
    pageNo:1,
    pageSize:10,
    templateType: activeKey.value,
    chkCurWx: weChatId.value,
  },()=>{
    initTabs.value.dataSource = dataSource.value;
    initTabs.value.pagination = pagination;
    initTabs.value.pageLoading = false;
  });
};
onMounted(() => {
  emitter.on("initWeChatId", (value) => {
    weChatId.value = value;
    if (value) {
      useGetList(newsList, pageLoading, dataSource, pagination, {
        chkCurWx: value,
        pageNo:1,
        pageSize:10,
        templateType: 'image'
      },()=>{
        initTabs.value.dataSource = dataSource.value;
        initTabs.value.pagination = pagination;
        initTabs.value.pageLoading = false;
        activeKey.value = 'image';
      });
    }
  });
});
emitter.on("handleTableChange", (value) => {
  useGetList(newsList, pageLoading, dataSource, pagination, {
    chkCurWx: weChatId.value,
    ...value,
    templateType: activeKey.value
  },()=>{
    initTabs.value.dataSource = dataSource.value;
    initTabs.value.pagination = pagination;
    initTabs.value.pageLoading = false;
  });
});
//复制路径
emitter.on("copyMaterial", (value) => {
  navigator.clipboard
    .writeText(value)
    .then(() => {
      message.success("已复制到剪贴板");
    })
    .catch((error) => {
      message.success("系统繁忙，请稍后再试！");
    });
});
//删除素材
emitter.on("deleteMaterial", (value) => {
  useDel(
    ukwDel,
    { id: value, chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        searchFun()
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    }
  );
});

//新增数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: "新增素材",
  weChatId: weChatId.value,
  item: { 
    multipartFile: [],
    materialName: '', 
    materialType: null,
    videoTitle: '',
    videoIntroduction: ''
  },
});
const showModal = () => {
  addEditData.modalTitle = "新增素材";
  addEditData.modalOpen = true;
  addEditData.weChatId = weChatId.value;
};
//新增成功
emitter.on("ukwAdd", () => {
  searchForm.value.templateName = ''
  addEditData.modalOpen = false;
  searchFun()
})
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("initWeChatId");
  emitter.off('copyMaterial');
  emitter.off('deleteMaterial')
  emitter.off('handleTableChange');
  emitter.off('ukwAdd');
});
</script>
<style lang="less" scoped>
::deep(.ant-tabs) {
  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    font-size: 14px;
    color: #011738;
  }
}
.content-tag {
  position: relative;
}
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .search-box {
    display: flex;
  }
  .search-btn {
    margin-right: 20px;
  }
  .add-btn {
    margin-left: 20px;
  }
}
</style>
