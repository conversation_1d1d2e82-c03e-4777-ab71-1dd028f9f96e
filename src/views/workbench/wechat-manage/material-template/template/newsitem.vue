<!-- 单图文 -->
<template>
  <a-checkbox-group v-model:value="checkboxs" @change="setNum">
    <a-list ref="listItemRef" size="small" bordered :data-source="materialData">
      <template #renderItem="{ item }">
        <a-list-item>
          <a-checkbox :value="item.id">
            <span class="serial-number">{{
              item?.num >= 1 ? item?.num : ""
            }}</span>
            {{ item.title }}
          </a-checkbox>
        </a-list-item>
      </template>
    </a-list>
  </a-checkbox-group>
</template>
  <script setup>
import emitter from "@/assets/js/emitter";
import "@/hooks/useLoadMore";

const props = defineProps({
  materialData: Object,
});
const listItemRef = ref();
const checkboxs = ref([]);
// 添加scroll监听
onMounted(() => {
  nextTick(() => {
    emitter.emit("onMounted", listItemRef.value);
  });
});
const setNum = (data) => {
  // 取消选中清除序号
  props.materialData.forEach((i) => {
    i.num = 0;
  });
  let selectItem = {}
  data.forEach((item, index) => {
    props.materialData.forEach((i) => {
      if (item == i.id) {
        i.num = index + 1;
        i.templateName = i.title;
        selectItem = i
      }
    });
  });
  selectItem.checkboxs = data;
  selectItem.typeName = "单图文";
  selectItem.templateType = 'newsitem';
  emitter.emit("selectPost", selectItem);
};
emitter.on("resetCheckboxs", () => {
  checkboxs.value = [];
  props.materialData.forEach((i) => {
    i.num = 0;
  });
});
// 移除scroll监听
onUnmounted(() => {
  emitter.off("onBeforeUnmount");
});
</script>
  <style lang="less" scoped>
.ant-checkbox-group,
.ant-list {
  width: 100%;
}
:deep(.ant-spin-container) {
  max-height: 360px;
  overflow-y: scroll;
}
:deep(.ant-list-items) {
  display: flex;
  flex-direction: column;
}
.ant-list-bordered.ant-list-sm :deep(.ant-list-item) {
  padding: 5px 15px;
}
.ant-checkbox-wrapper {
  position: relative;
}
.serial-number {
  position: absolute;
  left: -12px;
  top: 3px;
  font-size: 12px;
  color: #FE8308;
}
</style>
  
  