import $https from "@/assets/js/request"
//--------业务标签--------
// 列表
export const bizPage = (data) => {
    return $https.fetchGet('/cop/manage/tag/biz/page',data)
}
// 新增
export const bizAdd = (data) => {
    return $https.fetchPost('/cop/manage/tag/biz/add',data,{'headers': {'Content-Type':'application/json'}})
}
//--------综合标签--------
// 列表
export const synthesisPage = (data) => {
    return $https.fetchGet('/cop/manage/tag/synthesis/page',data)
}
// 新增
export const synthesisAdd = (data) => {
    return $https.fetchPost('/cop/manage/tag/synthesis/add',data,{'headers': {'Content-Type':'application/json'}})
}

//-------------
// 编辑
export const tagEdit = (data) => {
    return $https.fetchPost('/cop/manage/tag/edit',data,{'headers': {'Content-Type':'application/json'}})
}
// 是否可以删除
export const checkdel = (data) => {
    return $https.fetchGet('/cop/manage/tag/second/del/check',data)
}
// 删除
export const tagDel = (data) => {
    return $https.fetchPost('/cop/manage/tag/first/del',data)
}
