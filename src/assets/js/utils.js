// import { JSEncrypt } from 'jsencrypt';
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'
    // 号码加密
export const encryptionMobile = (obj) => {
    const PUBLIC_KEY = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8HMr2CBpoZPm3t9tCVlrKtTmI4jNJc7/HhxjIEiDjC8czP4PV+44LjXvLYcSV0fwi6nE4LH2c5PBPEnPfqp0g8TZeX+bYGvd70cXee9d8wHgBqi4k0J0X33c0ZnW7JruftPyvJo9OelYSofBXQTcwI+3uIl/YvrgQRv6A5mW01QIDAQAB';
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey('-----BEGIN PUBLIC KEY-----' + PU<PERSON><PERSON>_KEY + '-----END PUBLIC KEY-----');
    const encrypted = encrypt.encrypt(obj);
    return encrypted;
}