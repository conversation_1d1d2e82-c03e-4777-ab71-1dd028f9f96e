<!-- 头像上传 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.editData.modalOpen"
    title="修改头像"
    okText="确定"
    :confirm-loading="modalLoading"
    @ok="editSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-carousel arrows :after-change="onChange">
        <template #prevArrow>
          <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
            <left-circle-outlined class="outlined-icon" />
          </div>
        </template>
        <template #nextArrow>
          <div class="custom-slick-arrow" style="right: 10px">
            <right-circle-outlined class="outlined-icon" />
          </div>
        </template>
        <div v-for="item in editData.imgs" :key="item.id">
          <img :src="item.url" alt="" />
        </div>
      </a-carousel>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { userHeadImg } from "@/api/index";
import emitter from "@/assets/js/emitter";
import { message } from "ant-design-vue";
const props = defineProps({
  editData: Object,
});
//获取当前焦点
let currentNum = ref(0);
const onChange = (current) => {
  currentNum.value = current;
};

// 提交确认
const modalLoading = ref(false); // 提交loading
const editSubmit = async () => {
  console.log(1);
  modalLoading.value = true;
  const res = await userHeadImg({
    url: `https://mcn.i139.cn/${currentNum.value}`,
  });
  if (res.retCode === "000000") {
    message.success("操作成功！");
    emitter.emit("editImg", currentNum.value);
  } else {
    message.error(res.retMsg);
  }
};
</script>

<style lang="less" scoped>
.ant-carousel {
  width: 320px;
  margin: 0 auto;
}
.outlined-icon {
  color: #FE8308;
  font-size: 22px;
}
</style>
