<!-- 信息补充/编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.msgData.modalOpen"
    title="编辑"
    okText="确定"
    :afterClose="resetFields"
    @ok="msgSubmit"
    :confirm-loading="modalLoading"
    width="760px"
  >
    <a-spin :spinning="confirmLoading">
      <scheduleForm ref="scheduleChild" :scheduleData="props.msgData.scheduleData" />
      <a-divider>信息补充</a-divider>
      <a-form class="msg-supplement">
        <div class="platform-list" v-for="(item,index) in msgForm.scheduleDataAcctInfo" :key="item.dataId">
          <div class="platform-name">{{ item.platformName }}：{{ item.accountName }}</div>
          <div class="platform-form-item">
            <a-form-item label="标题" v-bind="validateInfos.title">
              <a-select
                v-model:value="item.title"
                placeholder="请选择或新增标题"
                style="width: 420px"
                :options="titleOpt[index]?.map(item => ({ value: item }))"
            >
                <template #dropdownRender="{ menuNode: menu }">
                <v-nodes :vnodes="menu" />
                <a-divider style="margin: 4px 0" />
                <a-space style="padding: 4px 8px">
                    <a-input ref="inputRef" 
                    style="width: 260px"
                    v-model:value="nameTitle"
                    placeholder="新增标题" />
                    <a-button type="text" @click="addTitleItem($event,index)">
                    <template #icon>
                        <plus-outlined />
                    </template>
                    新增标题
                    </a-button>
                </a-space>
                </template>
            </a-select>
            </a-form-item>
            <a-form-item label="点赞" v-bind="validateInfos.likeCount">
              <a-input
                v-model:value="item.likeCount"
                placeholder="请输入点赞数"
                @blur="validate('likeCount', { trigger: 'blur' }).catch(() => {})"
              />
            </a-form-item>
            <a-form-item label="分享数" v-bind="validateInfos.shareCount">
              <a-input
                v-model:value="item.shareCount"
                placeholder="请输入分享数"
                @blur="validate('shareCount', { trigger: 'blur' }).catch(() => {})"
              />
              <div class="get-data" :class="item.title?'':'disb-class'" @click="getShareCount(index,item.title)">手动获取</div>
            </a-form-item>
            <a-form-item label="评论" v-bind="validateInfos.commentCount">
              <a-input
                v-model:value="item.commentCount"
                placeholder="请输入评论数"
                @blur="validate('commentCount', { trigger: 'blur' }).catch(() => {})"
              />
            </a-form-item>
            <a-form-item label="url" v-bind="validateInfos.url">
              <a-input 
              v-model:value="item.url" 
              @blur="validate('url', { trigger: 'blur' }).catch(() => {})"
              placeholder="请输入url" />
              <div class="get-data" :class="item.title?'':'disb-class'" @click="getPublishUrl(index,item.title)">手动获取</div>
            </a-form-item>
            <a-form-item label="在看" v-bind="validateInfos.watchCount">
              <a-input
                v-model:value="item.watchCount"
                placeholder="请输入在看数"
                @blur="validate('watchCount', { trigger: 'blur' }).catch(() => {})"
              />
            </a-form-item>
            <a-form-item label="阅读量" v-bind="validateInfos.readCount">
              <a-input
                v-model:value="item.readCount"
                placeholder="请输入阅读量"
                @blur="validate('readCount', { trigger: 'blur' }).catch(() => {})"
              />
              <div class="get-data" :class="item.title?'':'disb-class'" @click="getReadCount(index,item.title)">手动获取</div>
            </a-form-item>
          </div>
        </div>
      </a-form>
    </a-spin>
  </a-modal>
</template>
      
<script setup>
import scheduleForm from "./scheduleForm.vue";
import { message, Form } from "ant-design-vue";
import * as workUrl from "@/api/conOprWork";
import emitter from "@/assets/js/emitter";
import {validateNumber, validateUrl} from "@/assets/js/formValidator";
const props = defineProps({
  msgData: Object,
});
const confirmLoading = ref(false);
const modalLoading = ref(false);
const msgForm = props.msgData.item;
const msgRules = reactive({
  title: [
    {
      required: true,
      message: "请选择标题",
      trigger: "change",
    },
  ],
  url: [
    {
      required: true,
      validator: validateUrl,
      trigger: "blur",
    }
  ],
  readCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    }
  ],
  shareCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    }
  ],
  likeCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    }
  ],
  watchCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    }
  ],
  commentCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    }
  ],
});
//标题下拉
const titleOpt = ref([]);
const getTitleList = async (dataId) => {
  confirmLoading.value = true;
  const res = await workUrl.publishTitle({dataId});
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    titleOpt.value.push(res.data)
  } else {
    message.error(res.retMsg);
  }
};
const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});
let iTitle = 0;
const inputRef = ref();
const nameTitle = ref();
//新增标题
const addTitleItem = (event,index) => {
  event.preventDefault();
  if(!nameTitle.value) {
    message.error("标题不能为空");
    return false
  } else if(nameTitle.value.length > 100) {
    message.error("标题字符长度不能超过100");
    return false
  }
  titleOpt.value[index].push(nameTitle.value || `New item ${(iTitle += 1)}`);
  nameTitle.value = '';
};
onMounted(() => {
  msgForm.scheduleDataAcctInfo.forEach(el=>{
    getTitleList(el.dataId);
  })
});
//手动获取url
const getPublishUrl = async (index,title) => {
  if(!title) {
    return false
  }
  confirmLoading.value = true;
  const res = await workUrl.publishUrl({dataId:msgForm.scheduleDataAcctInfo[index].dataId,title});
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    msgForm.scheduleDataAcctInfo[index].url = res.data
  } else if(res.retCode === '990009') {
    message.error("接入融媒体的公众号才能获取此数据");
  } else if(res.retCode === '990013') {
    message.error("获取失败，请手动输入");
  } else {
    message.error(res.retMsg);
  }
};
//手动获取阅读量
const getReadCount = async (index,title) => {
  if(!title) {
    return false
  }
  confirmLoading.value = true;
  const res = await workUrl.readCount({dataId:msgForm.scheduleDataAcctInfo[index].dataId,title});
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    msgForm.scheduleDataAcctInfo[index].readCount = res.data
  } else if(res.retCode === '990009') {
    message.error("接入融媒体的公众号才能获取此数据");
  } else {
    message.error(res.retMsg);
  }
};
//手动获取分享数
const getShareCount = async (index,title) => {
  if(!title) {
    return false
  }
  confirmLoading.value = true;
  const res = await workUrl.shareCount({dataId:msgForm.scheduleDataAcctInfo[index].dataId,title});
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    msgForm.scheduleDataAcctInfo[index].shareCount = res.data
  } else if(res.retCode === '990009') {
    message.error("接入融媒体的公众号才能获取此数据");
  } else {
    message.error(res.retMsg);
  }
};
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  msgForm.scheduleDataAcctInfo,
  msgRules
);
//补充信息/编辑排期数据
let scheduleChild = ref()
const msgSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      confirmLoading.value = true;
      const fromData = { ...toRaw(msgForm) };
      //赋值排期数据和信息补充数据
      const res = await workUrl.dataEdit(fromData.scheduleDataAcctInfo);
      modalLoading.value = false;
      confirmLoading.value = false;
      if (res.retCode === "000000") {
        props.msgData.modalOpen = false;
        message.success("操作成功！");
        emitter.emit("msgWork", "publishEdit");
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style lang="less" scoped>
.msg-supplement {
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
  .get-data {
    font-size: 12px;
    color: #FE8308;
    cursor: pointer;
    margin-left: 5px;
  }
  .disb-class {
    color: #ccc;
  }
  .platform-list {

  }
  .platform-name {
    padding-left: 50px;
    margin-bottom: 10px;
  }
  .platform-form-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}
</style>