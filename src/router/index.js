import { createRouter, createWebHashHistory } from 'vue-router'
const index = () => import('../views/index/index.vue')
const router = createRouter({
    history: createWebHashHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: "/homePage",
            name: "index",
            component: index
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('../views/login.vue')
        },
        , {
            path: "/workbench",
            name: "workbench",
            component: index,
            redirect: "workbench/contentOperateWorkbench",
            children: [{
                path: "contentOperateWorkbench",
                name: "contentOperateWorkbench",
                component: () => import("@/views/workbench/conOprWork.vue"),
                meta: {
                    title: "运营日历",
                    permission: []
                }
            }, {
                path: "contentTag",
                name: "contentTag",
                component: () => import("@/views/workbench/operate-manage/contentTag.vue"),
                meta: {
                    title: "内容标签配置",
                    permission: []
                }
            }, {
                path: "operateNode",
                name: "operateNode",
                component: () => import("@/views/workbench/operate-manage/operationNode.vue"),
                meta: {
                    title: "运营节点配置",
                    permission: []
                }
            }, {
                path: "weChatWelcome",
                name: "weChatWelcome",
                component: () => import("@/views/workbench/wechat-manage/weChatWelcome.vue"),
                meta: {
                    title: "欢迎关注语",
                    permission: []
                }
            }, {
                path: "weChatNotReply",
                name: "weChatNotReply",
                component: () => import("@/views/workbench/wechat-manage/weChatNotReply.vue"),
                meta: {
                    title: "未识别回复",
                    permission: []
                }
            }, {
                path: "weChatKeyword",
                name: "weChatKeyword",
                component: () => import("@/views/workbench/wechat-manage/weChatKeyword.vue"),
                meta: {
                    title: "关键字管理",
                    permission: []
                }
            }, {
                path: "weChatMenu",
                name: "weChatMenu",
                component: () => import("@/views/workbench/wechat-manage/weChatMenu.vue"),
                meta: {
                    title: "菜单管理",
                    permission: []
                }
            }, {
                path: "weChatTextMaterial",
                name: "weChatTextMaterial",
                component: () => import("@/views/workbench/wechat-manage/weChatTextMaterial.vue"),
                meta: {
                    title: "文本素材",
                    permission: []
                }
            }, {
                path: "weChatMultimediaMaterial",
                name: "weChatMultimediaMaterial",
                component: () => import("@/views/workbench/wechat-manage/weChatMultimediaMaterial.vue"),
                meta: {
                    title: "多媒体素材",
                    permission: []
                }
            }, {
                path: "articleShareArticleConfig",
                name: "articleShareArticleConfig",
                component: () => import("@/views/workbench/functional-component/article-share/articleConfig.vue"),
                meta: {
                    title: "文章配置",
                    permission: []
                }
            }, {
                path: "articleShareDetail",
                name: "articleShareDetail",
                component: () => import("@/views/workbench/functional-component/article-share/articleShareDetail.vue"),
                meta: {
                    title: "转发明细",
                    permission: []
                }
            }, {
                path: "businessLink",
                name: "businessLink",
                component: () => import("@/views/workbench/functional-component/businessLink.vue"),
                meta: {
                    title: "业务链接",
                    permission: []
                }
            },{
                path: "weChatStaffManage",
                name: "weChatStaffManage",
                component: () => import("@/views/workbench/functional-component/weChatStaffManage.vue"),
                meta: {
                    title: "员工管理",
                    permission: []
                }
            }
            ]
        }, {
            path: "/dataAnalysis",
            component: index,
            name: "dataAnalysis",
            meta: {
                title: "数据分析",
                icon: "form"
            },
            children: [{
                path: "accountOverview",
                name: "accountOverview",
                component: () => import("@/views/data-analysis/accountOverview.vue"),
                meta: {
                    title: "新媒体账号总览",
                    permission: []
                }
            }, {
                path: "accountDetails",
                name: "accountDetails",
                component: () => import("@/views/data-analysis/accountList.vue"),
                meta: {
                    title: "新媒体账号明细",
                    permission: []
                }
            }, {
                path: "basicDataAnalysis",
                name: "basicDataAnalysis",
                component: () => import("@/views/data-analysis/basicDataDashboard.vue"),
                meta: {
                    title: "我的运营数据",
                    permission: []
                }
            }]
        }, {
            path: "/intelligentCenter",
            component: index,
            name: "intelligentCenter",
            meta: {
                title: "智能中心",
                icon: "form"
            },
            children: [{
                path: "intelligentWrite",
                name: "intelligentWrite",
                component: () => import("@/views/intelligent-center/intelligentWrite.vue"),
                meta: {
                    title: "智能写作",
                    permission: []
                }
            }, {
                path: "intelligentTopicSelect",
                name: "intelligentTopicSelect",
                component: () => import("@/views/intelligent-center/intelligentTopicSelect.vue"),
                meta: {
                    title: "智能选题",
                    permission: []
                }
            }, {
                path: "contentReview",
                name: "contentReview",
                component: () => import("@/views/intelligent-center/contentReview.vue"),
                meta: {
                    title: "内容审核",
                    permission: []
                }
            }, {
                path: "documentWrite",
                name: "documentWrite",
                component: () => import("@/views/intelligent-center/intelligentWrite.vue"),
                meta: {
                    title: "公文写作",
                    permission: []
                }
            }, {
                path: "aiVideo",
                name: "aiVideo",
                component: () => import("@/views/intelligent-center/intelligentWrite.vue"),
                meta: {
                    title: "AI视频",
                    permission: []
                }
            }]
        }, {
            path: "/platformManage",
            component: index,
            name: "platformManage",
            meta: {
                title: "权限设置",
                icon: "form"
            },
            children: [{
                path: "deptManage",
                name: "deptManage",
                component: () => import("@/views/platform-manage/deptManage.vue"),
                meta: {
                    title: "智能写作",
                    permission: []
                }
            }, {
                path: "userManage",
                name: "userManage",
                component: () => import("@/views/platform-manage/userManage.vue"),
                meta: {
                    title: "账号管理",
                    permission: []
                }
            }, {
                path: "auditAccount",
                name: "auditAccount",
                component: () => import("@/views/platform-manage/auditAccount.vue"),
                meta: {
                    title: "新媒体账号审核",
                    permission: []
                }
            }]
        }
    ]
})

export default router
