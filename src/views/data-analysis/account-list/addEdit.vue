<!-- 账号列表编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
    width="680px"
  > 
  <a-spin :spinning="modalLoading">
    <a-form>
      <a-form-item label="账号名称" v-bind="validateInfos.name">
        <a-input
          v-model:value="addEditFrom.name"
          @blur="validate('name', { trigger: 'blur' }).catch(() => {})"
          placeholder="请输入账号名称"
        />
      </a-form-item>
      <a-form-item label="平台" v-bind="validateInfos.platform">
        <a-select
          v-model:value="addEditFrom.platform"
          :options="platformOpt"
          :field-names="{ label: 'name', value: 'id' }"
          placeholder="请选择平台"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="认证主体/状态" v-bind="validateInfos.mainBody">
        <a-input
          v-model:value="addEditFrom.mainBody"
          placeholder="请输入认证主体/状态"
        />
      </a-form-item>
      <a-form-item label="运营管理员" v-bind="validateInfos.operateAdmin">
        <a-input
          v-model:value="addEditFrom.operateAdmin"
          @blur="validate('operateAdmin', { trigger: 'blur' }).catch(() => {})"
          placeholder="请输入运营管理员"
        />
      </a-form-item>
      <a-form-item label="融媒体接入" v-bind="validateInfos.mcnFlag" v-if="addEditFrom.platform === 1">
        <a-select
          v-model:value="addEditFrom.mcnFlag"
          :options="mcnFlagOpt"
          :field-names="{ label: 'name', value: 'id' }"
          placeholder="请选择平台"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="公众号ID/微信号" v-bind="validateInfos.weChatId" v-if="addEditFrom.platform === 1">
        <a-input
          v-model:value="addEditFrom.weChatId"
          @blur="validate('weChatId', { trigger: 'blur' }).catch(() => {})"
          placeholder="请输入公众号ID/微信号"
        />
      </a-form-item>
    </a-form>
  </a-spin>
  </a-modal>
</template>
  
<script setup>
import { message, Form } from "ant-design-vue";
import { accountAdd, accountEdit } from "@/api/accountList";
import emitter from "@/assets/js/emitter";
import { encryptionMobile } from "@/assets/js/utils";
const props = defineProps({
  addEditData: Object,
});
const modalLoading = ref(false);
const addEditFrom = props.addEditData.item;
// 动态验证公众号ID/微信号
watch(()=>addEditFrom.platform,(newVal,oldVal)=>{
  if (newVal === 1) {
    addEditRules.weChatId[0].required = true
    addEditRules.mcnFlag[0].required = true
  } else {
    addEditRules.weChatId[0].required = false
    addEditRules.mcnFlag[0].required = false
  }
})
const validateWxId = (_rule, value) => {
  if (value && !/^[^\u4e00-\u9fa5]+$/.test(value)) {
    return Promise.reject('不能包含中文字符');
  } else {
    return Promise.resolve();
  }
};
const addEditRules = reactive({
  name: [
    {
      required: true,
      message: "请输入账号名称",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  platform: [
    {
      required: true,
      message: "请选择平台",
      trigger: "change"
    },
  ],
  mainBody: [
    {
      required: true,
      message: "请输入认证主体/状态",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 50个字符",
      trigger: "change",
    }
  ],
  mcnFlag: [
    {
      required: true,
      message: "请选择融媒体接入",
      trigger: "change"
    }
  ],
  weChatId: [
    {
      required: true,
      message: "请输入公众号ID/微信号",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "blur",
    },
    {
      validator: validateWxId,
      trigger: "blur",
    }
  ],
  operateAdmin: [
    {
      required: true,
      message: "请输入运营管理员",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "blur",
    }
  ]
});
const platformOpt = reactive([
  {name:"公众号",id:1},
  {name:"小红书",id:2},
  {name:"微博",id:3},
  {name:"视频号",id:4},
  {name:"抖音",id:5},
  {name:"头条",id:6},
  {name:"快手",id:7},
  {name:"其他",id:100}
]);
const mcnFlagOpt = ref([
  { id: 0, name: "否" },
  { id: 1, name: "是" },
]);
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const fromData = {...toRaw(addEditFrom)};
      fromData.operateAdmin = encodeURI(
        encryptionMobile(fromData.operateAdmin)
      );
      if(fromData.platform !== 1) {
        delete fromData.weChatId
        delete fromData.mcnFlag
      }
      if (!addEditFrom?.id) {
        //新增
        res = await accountAdd(fromData);
      } else {
        //编辑提交
        res = await accountEdit(fromData);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("refreshAccount", "addEdit");
      } else if (res.retCode === "990006") {
        message.error("已存在相同名称的账号");
      } else if (res.retCode === "990007") {
        message.error("已存在相同公众号ID/微信号的账号");
      } else if (res.retCode === "990008") {
        message.error("公众号ID/微信号错误");
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
