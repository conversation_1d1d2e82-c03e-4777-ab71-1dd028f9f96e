html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-family: "思源黑体" !important;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
*{
    box-sizing:border-box;
    outline:none;
}

html, body {
  width: 100%;
  height: 100%;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 14PX;
  height: 14PX;
}

::-webkit-scrollbar-track {
  border-radius: 999px;
  border: 5px solid transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 999px;
  border: 5px solid transparent;
}
::-webkit-scrollbar-thumb {
  min-height: 20px;
  background-clip: content-box;
  box-shadow: 0 0 0 5px rgba(0, 0, 0, .4) inset;
  border-radius: 100px;
}
// 表格样式统一
.ant-table-wrapper .ant-table-thead >tr>th, 
.ant-table-wrapper .ant-table-thead >tr>td {
  font-size: 14px;
  background: #F8FAFC;
  color: #94A3B8;
  font-weight: 400;
}
.ant-table-wrapper .ant-table {
  font-size: 14px;
  color: #475569;
}
.ant-table-wrapper .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before, 
.ant-table-wrapper .ant-table-thead >tr>td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  width: 0;
}
[class^="ant-table"] [class^="ant-table"],
[class*=" ant-table"] [class^="ant-table"], 
[class^="ant-table"] [class*=" ant-table"], 
[class*=" ant-table"] [class*=" ant-table"]{
  font-size: 14px;
  color: #1E293B;
}
.ant-table-wrapper .ant-table.ant-table-small .ant-table-title, 
.ant-table-wrapper .ant-table.ant-table-small .ant-table-footer, 
.ant-table-wrapper .ant-table.ant-table-small .ant-table-thead>tr>th, 
.ant-table-wrapper .ant-table.ant-table-small .ant-table-tbody>tr>td, 
.ant-table-wrapper .ant-table.ant-table-small tfoot>tr>th, 
.ant-table-wrapper .ant-table.ant-table-small tfoot>tr>td {
  padding:15px 8px;
}
.ant-table-wrapper {
  height: 100%;
  .ant-spin-nested-loading {
    height: 100%;
  }
  .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    .ant-table {
      flex: 1;
      height: 0;
    }
    .ant-pagination {
      flex-shrink: 0;
    }
  }
  .ant-table-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    > .ant-table-header {
      flex-shrink: 0;
    }
    > .ant-table-body {
      flex: 1;
      height: 0;
      max-height: none !important;
    }
  }
  
}
.ant-table-wrapper .ant-spin-container .ant-pagination {
  margin-bottom: 0;
}
//按钮样式
.ant-btn-primary {
  display: flex;
  align-items: center;
  background-image: linear-gradient(90deg, #FE7209 0%, #FE8409 100%);
  //按钮内的svg icon样式
  .anticon  {
    font-size: 16px;
    color: #fff;
  }
}
.ant-btn-primary:disabled {
  color: #fff;
  border-color: rgba(254, 115, 9, 0.7);
  background-image: linear-gradient(90deg, rgba(254, 115, 9, 0.7) 0%, rgba(254, 115, 9, 0.7) 100%);
}
.ant-btn:not(:disabled):focus-visible {
  outline: 0;
}
.ant-btn-default.ant-btn-dangerous,.ant-btn-default.ant-btn-dangerous:not(:disabled):hover {
  color: #fe8308;
  border-color: #fe8308;
}
//搜索栏样式
.top-operate {
  .anticon-plus {
    font-size: 18px;
    color: #fff;
  }
}
.search-box,.top-operate {
  .ant-input,.ant-picker,.ant-select {
    width: 200px;
    margin-right: 15px;
  }
  .top-btn {
    min-width: 82px;
    margin-right: 15px;
  }
}
//在全局设置
input[aria-hidden=true]{
  display: none !important;
}

//弹窗定义
.ant-modal,.ant-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .ant-input,.ant-picker,.ant-select {
    width: 200px;
  }
  .ant-form-item-label {
    width: 118px;
    text-align: right;
    display: inline-block;
  }
  .ant-modal-content {
    padding: 0;
  }
  .ant-modal-close {
    top: 12px;
  }
  .ant-modal-title {
    font-weight: 450;
    padding: 12px 24px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  }
  .ant-modal-header {
    margin: 0;
  }
  .ant-modal-body {
    max-height: calc(100vh - 150px);
    overflow-y: auto;
    padding: 20px;
    font-size: 14px;
    color: #1E293B;
  }
  .ant-modal-footer {
    display: flex;
    justify-content: center;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    margin-top: 0;
    padding: 13px;
    .ant-btn-default {
      display: none;
    }
  }
  .ant-btn-text:not(:disabled):hover {
    color: #fff;
    background-color: rgba(0, 0, 0, 0.06);
    background-image: linear-gradient(90deg, #3B82F6 0%, #2563EB 100%);
  }
}
// .material-template {
//   .ant-modal-body {
//       padding: 0 20px;
//     }
// }