<!-- 排期form -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.scheduleData.modalOpen"
    title="排期"
    okText="确定"
    :afterClose="resetFields"
    @ok="scheduleSubmit"
    :confirm-loading="modalLoading"
    width="700px"
    v-if="props.scheduleData.isSchedule"
  >
    <a-spin :spinning="confirmLoading">
      <a-form>
        <a-form-item label="星期" v-bind="validateInfos.scheduleWeek">
          <a-select
            v-model:value="scheduleFrom.scheduleWeek"
            disabled
            :options="scheduleWeekOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择星期"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="是否节点" v-bind="validateInfos.nodeFlag">
          <a-select
            v-model:value="scheduleFrom.nodeFlag"
            disabled
            :options="nodeFlagOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择是否节点"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="一类标签" v-bind="validateInfos.bigCategory">
          <a-cascader
            @change="changegbBig(scheduleFrom.bigCategory)"
            v-model:value="scheduleFrom.bigCategory"
            :options="bigCategoryOpt"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择一类标签"
          />
        </a-form-item>
        <a-form-item label="二类标签" v-bind="validateInfos.secondTag">
          <a-select
            v-model:value="scheduleFrom.secondTag"
            :options="smallCategoryOpt"
            :field-names="{ label: 'name', value: 'id' }"
            :placeholder="
              scheduleFrom.bigCategory ? '请先选择二类标签' : '请先选择一类标签'
            "
            allow-clear
          >
          </a-select>
        </a-form-item>
        <a-form-item label="内容描述" v-bind="validateInfos.businessName">
          <a-input
            v-model:value="scheduleFrom.businessName"
            placeholder="请输入内容描述"
          />
        </a-form-item>
        <a-form-item label="内容对接人" v-bind="validateInfos.businessLiaisonPerson">
          <a-input
            v-model:value="scheduleFrom.businessLiaisonPerson"
            placeholder="请输入内容对接人"
        />
        </a-form-item>
        <a-form-item label="需求口" v-bind="validateInfos.demandPort">
          <a-input
            v-model:value="scheduleFrom.demandPort"
            placeholder="请输入需求口"
          />
        </a-form-item>
        <a-form-item label="责任编辑" v-bind="validateInfos.dutyEditor">
          <a-input
            v-model:value="scheduleFrom.dutyEditor"
            placeholder="请输入责任编辑"
          />
        </a-form-item>
        <a-form-item label="策划描述" v-bind="validateInfos.planDesc">
          <a-textarea
            v-model:value="scheduleFrom.planDesc"
            placeholder="请输入策划描述"
            allow-clear
          />
        </a-form-item>
        <a-divider>发布平台</a-divider>
        <a-form-item label="公众号" v-bind="validateInfos.publicAot">
          <a-select
            v-model:value="scheduleFrom.publicAot"
            :options="accountListOpt.weChatAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择公众号"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="视频号" v-bind="validateInfos.videoAot">
          <a-select
            v-model:value="scheduleFrom.videoAot"
            :options="accountListOpt.videoAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择视频号"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="微博" v-bind="validateInfos.weiboAot">
          <a-select
            v-model:value="scheduleFrom.weiboAot"
            :options="accountListOpt.weiboAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择微博"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="抖音" v-bind="validateInfos.douyinAot">
          <a-select
            v-model:value="scheduleFrom.douyinAot"
            :options="accountListOpt.tikTokAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择抖音"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="小红书" v-bind="validateInfos.xiaohongshuAot">
          <a-select
            v-model:value="scheduleFrom.xiaohongshuAot"
            :options="accountListOpt.redNoteAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择小红书"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="快手" v-bind="validateInfos.kuaishouAot">
          <a-select
            v-model:value="scheduleFrom.kuaishouAot"
            :options="accountListOpt.kuAiShouAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择快手"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="头条" v-bind="validateInfos.toutiaoAot">
          <a-select
            v-model:value="scheduleFrom.toutiaoAot"
            :options="accountListOpt.touTiAoAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择头条"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="其他平台" v-bind="validateInfos.otherAot">
          <a-select
            v-model:value="scheduleFrom.otherAot"
            :options="accountListOpt.otherAoAccount"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择其他平台"
            allow-clear
            :max-tag-count="1"
            mode="multiple"
          >
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
  <a-form v-else>
    <a-form-item label="星期" v-bind="validateInfos.scheduleWeek">
      <a-select
        v-model:value="scheduleFrom.scheduleWeek"
        disabled
        :options="scheduleWeekOpt"
        :field-names="{ label: 'name', value: 'id' }"
        placeholder="请选择星期"
      >
      </a-select>
    </a-form-item>
    <a-form-item label="是否节点" v-bind="validateInfos.nodeFlag">
      <a-select
        v-model:value="scheduleFrom.nodeFlag"
        disabled
        :options="nodeFlagOpt"
        :field-names="{ label: 'name', value: 'id' }"
        placeholder="请选择是否节点"
      >
      </a-select>
    </a-form-item>
    <a-form-item label="一类标签" v-bind="validateInfos.bigCategory">
      <a-cascader
        @change="changegbBig(scheduleFrom.bigCategory)"
        v-model:value="scheduleFrom.bigCategory"
        :options="bigCategoryOpt"
        disabled
        :field-names="{ label: 'label', value: 'value' }"
        placeholder="请选择一类标签"
      />
    </a-form-item>
    <a-form-item label="二类标签" v-bind="validateInfos.secondTag">
      <a-select
        v-model:value="scheduleFrom.secondTag"
        :options="smallCategoryOpt"
        disabled
        :field-names="{ label: 'name', value: 'id' }"
        :placeholder="
          scheduleFrom.bigCategory ? '请先选择二类标签' : '请先选择一类标签'
        "
      >
      </a-select>
    </a-form-item>
    <a-form-item label="内容描述" v-bind="validateInfos.businessName">
      <a-input
        v-model:value="scheduleFrom.businessName"
        placeholder="请输入内容描述"
        disabled
      />
    </a-form-item>
    <a-form-item label="内容对接人" v-bind="validateInfos.businessLiaisonPerson">
      <a-input
        v-model:value="scheduleFrom.businessLiaisonPerson"
        placeholder="请输入内容对接人"
        disabled
    />
    </a-form-item>
    <a-form-item label="需求口" v-bind="validateInfos.demandPort">
      <a-input
        v-model:value="scheduleFrom.demandPort"
        placeholder="请输入需求口"
        disabled
      />
    </a-form-item>
    <a-form-item label="责任编辑" v-bind="validateInfos.dutyEditor">
      <a-input
        v-model:value="scheduleFrom.dutyEditor"
        placeholder="请输入责任编辑"
        disabled
      />
    </a-form-item>
    <a-form-item label="策划描述" v-bind="validateInfos.planDesc">
      <a-textarea
        v-model:value="scheduleFrom.planDesc"
        placeholder="请输入策划描述"
        allow-clear
        disabled
      />
    </a-form-item>
  </a-form>
</template>
<script setup> 
import { message, Form } from "ant-design-vue";
import * as workUrl from "@/api/conOprWork";
import emitter from "@/assets/js/emitter";
import { encryptionMobile } from "@/assets/js/utils";
const props = defineProps({
  scheduleData: Object,
});
const confirmLoading = ref(false);
const modalLoading = ref(false);
const scheduleFrom = props.scheduleData.item;
const scheduleRules = reactive({
  scheduleWeek: [
    {
      required: true,
      message: "请选择星期"
    },
  ],
  nodeFlag: [
    {
      required: true,
      message: "请选择是否节点"
    },
  ],
  businessName: [
    {
      required: true,
      message: "请输入内容描述",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符"
    },
  ],
  bigCategory: [
    {
      required: true,
      message: "请选择一类标签"
    },
  ],
  secondTag: [
    {
      required: true,
      message: "请选择二类标签"
    },
  ],
  demandPort: [
    {
      required: true,
      message: "请输入需求口"
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符"
    },
  ],
  businessLiaisonPerson: [
    {
      required: true,
      message: "请输入内容对接人",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符"
    },
  ],
  dutyEditor: [
    {
      required: true,
      message: "请输入责任编辑"
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符"
    },
  ],
  planDesc: [
    {
      required: scheduleFrom.nodeFlag,
      message: "请输入策划描述",
    },
    {
      min: 1,
      max: 1000,
      message: "长度在1 到 1000个字符"
    },
  ],
});
const scheduleWeekOpt = reactive([
  { id: 2, name: "星期一" },
  { id: 3, name: "星期二" },
  { id: 4, name: "星期三" },
  { id: 5, name: "星期四" },
  { id: 6, name: "星期五" },
  { id: 7, name: "星期六" },
  { id: 1, name: "星期日" }
]);
const nodeFlagOpt = ref([
  { id: 0, name: "否" },
  { id: 1, name: "是" }
]);
//一类标签
const bigCategoryOpt = ref([]);
const getFirstTree = async () => {
  confirmLoading.value = true;
  const res = await workUrl.firstTree();
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    bigCategoryOpt.value = res.data;
    if (props.scheduleData.item.firstTag) {
      scheduleFrom.bigCategory = [
        props.scheduleData.item.tagType.toString(),
        props.scheduleData.item.firstTag.toString(),
      ];
      getSecondList(scheduleFrom.bigCategory);
      scheduleFrom.secondTag.toString();
    } else {
      scheduleFrom.bigCategory = []
    }
  } else {
    message.error(res.retMsg);
  }
};
//二类标签
const smallCategoryOpt = ref();
const getSecondList = async (id) => {
  if (!id) {
    scheduleFrom.secondTag = "";
    return false;
  }
  confirmLoading.value = true;
  const params = {
    firstTagId: id[1]
  };
  const res = await workUrl.secondList(params);
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    smallCategoryOpt.value = res.data;
  } else {
    message.error(res.retMsg);
  }
};
//获取平台账号
console.log(123)
const accountListOpt = reactive({});
const platforms = ['weChatAccount','redNoteAccount','weiboAccount','videoAccount','tikTokAccount','touTiAoAccount','kuAiShouAccount','otherAoAccount']
const getAccountList = async () => {
  confirmLoading.value = true;
  const res = await workUrl.accountList();
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    Object.assign(accountListOpt, ...platforms.map((item,index) => ({ [item]: res.data[item==='otherAoAccount'?100:index+1] }))); // 将每个元素设为true或其他值
    console.log(accountListOpt)
  } else {
    message.error(res.retMsg);
  }
};
//重新选择一类时清空二类
const changegbBig = (value) => {
  getSecondList(value);
  scheduleFrom.secondTag = "";
}
onMounted(() => {
  getFirstTree();
  getAccountList();
});
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  scheduleFrom,
  scheduleRules
);
//暴露数据给信息补充页面
defineExpose({validate,scheduleFrom})
//新增排期数据
const scheduleSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      confirmLoading.value = true;
      const fromData = { ...toRaw(scheduleFrom) };
      fromData.firstTag = scheduleFrom.bigCategory[1];
      delete fromData.bigCategory;
      let accountIds = [
        ...scheduleFrom?.publicAot,
        ...scheduleFrom?.videoAot,
        ...scheduleFrom?.weiboAot,
        ...scheduleFrom?.douyinAot,
        ...scheduleFrom?.xiaohongshuAot,
        ...scheduleFrom?.kuaishouAot,
        ...scheduleFrom?.toutiaoAot,
        ...scheduleFrom?.otherAot
      ];
      accountIds = accountIds.filter(item => item !== '' && item !== null && item !== undefined);
      if(accountIds.length === 0) {
        message.error('请至少选择一个平台');
        modalLoading.value = false;
        confirmLoading.value = false;
        return false;
      }
      fromData.accountId = accountIds.join(',');
      fromData.dutyEditor = encodeURI(encryptionMobile(fromData.dutyEditor));
      fromData.businessLiaisonPerson = encodeURI(
        encryptionMobile(fromData.businessLiaisonPerson)
      );
      let res = {};
      if(fromData.id) {
        res = await workUrl.scheduleEdit(fromData);
      }else {
        res = await workUrl.scheduleAdd(fromData);
      }
      modalLoading.value = false;
      confirmLoading.value = false;
      if (res.retCode === "000000") {
        props.scheduleData.modalOpen = false;
        message.success("操作成功！");
        emitter.emit("refreshWork", "schedule");
      } else if (res.retCode === "000010") {
        message.error("策划描述必填");
      } else{
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
    
  