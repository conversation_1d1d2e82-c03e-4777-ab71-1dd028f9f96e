<!-- 行业弹窗 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.industryModalData.modalOpen"
    okText="确定"
    :confirm-loading="modalLoading"
    @ok="selectIdustry"
    class="industry-modal"
  >
  <template #title>
    <div class="modal-title">
      {{ modalTitle }}
      <span>{{ idustrys.length }}/</span>
      <span class="industry-number">5</span>
    </div>
  </template>
    <a-spin :spinning="modalLoading">
      <div class="industry-tag" :class="{'industry-tag-activity':idustrys.includes(item.id)}" @click="checkIdustry(item)" v-for="item in industryData" :key="item.id">
        {{ item.name }}
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { message } from "ant-design-vue";
import { getIndustry, industrySave } from "@/api/intelligentTopicSelect";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  industryModalData: Object,
});
const modalTitle = ref('选择您的创作行业');
const modalLoading = ref(false);
const industryData = ref([])


//获取行业
const getIndustryList = async ()=> {
  modalLoading.value = true;
  const res = await getIndustry();
  modalLoading.value = false;
  if (res.retCode === "000000") {
    industryData.value = res.data;
  } else {
    message.error(res.retMsg);
  }
};
// 选择行业
const idustrys = ref([]);
const idustrysName = ref([]);
idustrys.value = props.industryModalData.idustrysId;
idustrysName.value = props.industryModalData.idustrys;
const checkIdustry = (item)=> {
  if(idustrys.value.length < 5 && !idustrys.value.includes(item.id)) {
    idustrys.value.push(item.id);
    idustrysName.value.push(item);
  } else if(idustrys.value.includes(item.id)) {
    idustrys.value = idustrys.value.filter(el => el != item.id);
    idustrysName.value = idustrysName.value.filter(el => el.id != item.id);
    console.log(idustrysName.value)
    emitter.emit("selectIdustry", idustrysName.value);
  }
};
const selectIdustry = async ()=> {
  modalLoading.value = true;
  const res = await industrySave({industryId:idustrys.value.join(',')});
  modalLoading.value = false;
  if (res.retCode === "000000") {
    props.industryModalData.modalOpen = false;
    emitter.emit("selectIdustry", idustrysName.value);
  } else {
    message.error(res.retMsg);
  }
  
};
onMounted(()=>{
  getIndustryList()
})
</script>

<style lang="less" scoped>
  .industry-tag {
    min-width: 80px;
    padding: 5px 10px;
    background: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 4px;
    font-size: 14px;
    color: #1E293B;
    text-align: center;
    cursor: pointer;
  }
  .industry-tag:hover{
    border: 1px solid #FE8308;
    color: #FE8308;
  }
  .industry-tag-activity:hover{
    border: 1px solid #FE8308;
    color: #fff;
  }
  .industry-tag-activity {
    border: 1px solid #FE8308;
    color: #fff;
    background: #FE8308;
  }
  .modal-title {
    font-size: 14px;
    color: #1E293B;
    font-weight: bold;
  }
  .industry-number {
    color: #FE8308;
  }
</style>
<style lang="less">
.industry-modal {
  .ant-modal-body {
    min-height: 150px;
  }
  .ant-spin-container {
    display: flex;
    grid-gap: 20px 15px;
    flex-wrap: wrap;
  }
}
</style>
