<!-- 信息维护 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.msgData.modalOpen"
    title="信息维护"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="msgSubmit"
    width="460px"
  >
    <a-spin :spinning="confirmLoading">
      <a-form class="msg-maintain">
        <a-form-item label="账号名称" v-bind="validateInfos.name">
          <a-input v-model:value="msgFrom.name" placeholder="请输入账号名称" />
        </a-form-item>
        <a-form-item label="粉丝量" v-bind="validateInfos.fansCount">
          <a-input
            v-model:value="msgFrom.fansCount"
            placeholder="请输入粉丝量"
            @blur="validate('fansCount', { trigger: 'blur' }).catch(() => {})"
          />
          <div class="get-data" @click="getFansCount">手动获取</div>
        </a-form-item>
        <a-form-item label="在网内容" v-bind="validateInfos.contentCount">
          <a-input
            v-model:value="msgFrom.contentCount"
            placeholder="请输入在网内容"
            @blur="
              validate('contentCount', { trigger: 'blur' }).catch(() => {})
            "
          />
          <div class="get-data" @click="getContentCount">手动获取</div>
        </a-form-item>
        <a-form-item label="更新频率" v-bind="validateInfos.updateFrequency">
          <a-select
            v-model:value="msgFrom.updateFrequency"
            :options="updateFrequencyOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择更新频率"
          >
          </a-select>
          <div class="get-data" @click="getEditHz">手动获取</div>
        </a-form-item>
      </a-form></a-spin
    >
  </a-modal>
</template>
  
<script setup>
import { message, Form } from "ant-design-vue";
import { fansCount, contentCount, editHz, editOpr } from "@/api/accountList";
import emitter from "@/assets/js/emitter";
import { validateNumber } from "@/assets/js/formValidator";
const props = defineProps({
  msgData: Object,
});
const confirmLoading = ref(false);
const modalLoading = ref(false);
const msgFrom = props.msgData.item;
const updateFrequencyOpt = ref([
  { id: 1, name: "7日内更新" },
  { id: 2, name: "15日内更新" },
  { id: 3, name: "30日内更新" },
  { id: 4, name: "超过30日未更新" },
]);
//手动获取粉丝量
const getFansCount = async () => {
  confirmLoading.value = true;
  const res = await fansCount({ cntEcOprAccountId: msgFrom.cntEcOprAccountId });
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    msgFrom.fansCount = res.data;
  } else if (res.retCode === "000031") {
    message.error("无效账号，无法获取");
  } else if (res.retCode === "000020") {
    message.error("无权获取");
  } else if (res.retCode === "000040") {
    message.error("审核通过的账号才能获取");
  } else if (res.retCode === "990009") {
    message.error("接入融媒体的公众号才能获取");
  } else if (res.retCode === "990010") {
    message.error("公众号ID为空，无法获取");
  } else if (res.retCode === "990013") {
    message.error("获取失败，请手动输入");
  } else {
    message.error(res.retMsg);
  }
};
//手动获取在网内容
const getContentCount = async () => {
  confirmLoading.value = true;
  const res = await contentCount({
    cntEcOprAccountId: msgFrom.cntEcOprAccountId,
  });
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    msgFrom.contentCount = res.data;
  } else if (res.retCode === "000031") {
    message.error("无效账号，无法获取");
  } else if (res.retCode === "000020") {
    message.error("无权获取");
  } else if (res.retCode === "000040") {
    message.error("审核通过的账号才能获取");
  } else if (res.retCode === "990009") {
    message.error("接入融媒体的公众号才能获取");
  } else if (res.retCode === "990010") {
    message.error("公众号ID为空，无法获取");
  } else if (res.retCode === "990013") {
    message.error("获取失败，请手动输入");
  } else {
    message.error(res.retMsg);
  }
};
//手动获取更新频率
const getEditHz = async () => {
  confirmLoading.value = true;
  const res = await editHz({ cntEcOprAccountId: msgFrom.cntEcOprAccountId });
  confirmLoading.value = false;
  if (res.retCode === "000000") {
    msgFrom.updateFrequency = res.data;
  } else if (res.retCode === "000031") {
    message.error("无效账号，无法获取");
  } else if (res.retCode === "000020") {
    message.error("无权获取");
  } else if (res.retCode === "000040") {
    message.error("审核通过的账号才能获取");
  } else if (res.retCode === "990009") {
    message.error("接入融媒体的公众号才能获取");
  } else if (res.retCode === "990010") {
    message.error("公众号ID为空，无法获取");
  } else if (res.retCode === "990013") {
    message.error("获取失败，请手动输入");
  } else {
    message.error(res.retMsg);
  }
};
//表单规则验证数据
const msgRules = reactive({
  name: [
    {
      required: true,
      message: "请输入账号名称",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change",
    },
  ],
  fansCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    },
  ],
  contentCount: [
    {
      required: true,
      validator: validateNumber,
      trigger: "blur",
    },
  ],
  updateFrequency: [
    {
      required: true,
      message: "请选择更新频率",
      trigger: "change",
    },
  ],
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(msgFrom, msgRules);
const msgSubmit = () => {
  validate().then(async () => {
    confirmLoading.value = true;
    modalLoading.value = true;
    const fromData = toRaw(msgFrom);
    const res = await editOpr(fromData);
    confirmLoading.value = false;
    modalLoading.value = false;
    if (res.retCode === "000000") {
      message.success("操作成功！");
      emitter.emit("refreshAccount", "msg");
    } else if (res.retCode === "990006") {
      message.error("已存在相同名称的账号");
    } else {
      message.error(res.retMsg);
    }
  });
};
</script>
  
<style lang="less" scoped>
.msg-maintain {
  display: block;
  :deep(.ant-form-item-control-input-content) {
    display: flex;
    align-items: center;
  }
  .get-data {
    font-size: 12px;
    color: #FE8308;
    cursor: pointer;
    margin-left: 5px;
  }
}
</style>
  