<!-- 关键字管理 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-input
        v-model:value="searchForm.templateName"
        placeholder="请输入文本名称"
      />
      <a-button type="primary" @click="searchFun" class="search-btn">
        <template #icon>
          <SearchOutlined />
        </template>
        查询
      </a-button>
      <weChatConfig />
    </div>
    <a-button type="primary" @click="showModal()" class="submit-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增文本名称
    </a-button>
  </div>
  <div class="user-content">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'adminFlag'">
          {{ record.adminFlag === 1 ? "是" : "否" }}
        </template>
        <template v-if="column.dataIndex === 'msgTypeName'">
          {{ record.msgTypeName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'accountName'">
          {{ record.accountName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span v-if="record.adminFlag !== 1" @click="showModal(record)"
              >编辑</span
            >
            <span
              class="del-btn"
              v-if="record.adminFlag !== 1"
              @click="delFun(record)"
              >删除</span
            >
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <addEdit v-if="addEditData.modalOpen" :addEditData="addEditData" />
</template>
    
  <script setup>
import { txtList, txtDel } from "@/api/weChatTextMaterial";
import weChatConfig from "@/components/weChat-config/weChatConfig.vue";
import addEdit from "./weChat-text-material/addEdit.vue";
import useGetList from "@/hooks/useGetList";
import { message } from "ant-design-vue";
import emitter from "@/assets/js/emitter";
import useDel from "@/hooks/useDel";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "文本名称",
    dataIndex: "templateName",
  },
  {
    title: "文本内容",
    dataIndex: "templateContent",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    width: 160,
  },
  {
    title: "操作",
    dataIndex: "operate",
    width: 120,
    fixed: "right",
  },
]);
const pageLoading = ref(false);
//搜索表单数据
const searchForm = ref({
  templateName: "",
});
//查询列表
const searchFun = () => {
  useGetList(txtList, pageLoading, dataSource, pagination, {
    ...searchForm.value,
    pageNo: 1,
    chkCurWx: weChatId.value,
  });
};
// 获取列表数据
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
const weChatId = ref("");
onMounted(() => {
  emitter.on("initWeChatId", (value) => {
    weChatId.value = value;
    if (value) {
      useGetList(txtList, pageLoading, dataSource, pagination, {
        pageNo: 1,
        chkCurWx: value,
      });
    }
  });
});
//分页处理
const handleTableChange = (pag) => {
  pagination.pageSize = pag.pageSize;
  useGetList(txtList, pageLoading, dataSource, pagination, {
    pageNo: pag.current,
    pageSize: pag.pageSize,
    chkCurWx: weChatId.value,
  });
};
//新增/编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: "新增素材",
  weChatId: weChatId.value,
  item: {
    templateName: "",
    templateContent: "",
  },
});
const showModal = async (item = { templateName: "", templateContent: "" }) => {
  if (item?.id) {
    addEditData.modalTitle = "编辑素材";
  } else {
    addEditData.modalTitle = "新增素材";
  }
  Object.assign(addEditData.item, item);
  addEditData.modalOpen = true;
  addEditData.weChatId = weChatId.value;
};

//新增/编辑之后更新数据
emitter.on("addEditTxt", (value) => {
  if (value) {
    addEditData.modalOpen = false;
    useGetList(txtList, pageLoading, dataSource, pagination, {
      pageNo: 1,
      chkCurWx: weChatId.value,
    });
  }
});
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("addEditTxt");
  emitter.off("initWeChatId");
});
// 删除数据
const delFun = async (list) => {
  useDel(
    txtDel,
    { id: list.id, chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        useGetList(txtList, pageLoading, dataSource, pagination, {
          pageNo: 1,
          chkCurWx: weChatId.value,
        });
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    }
  );
};
</script>
    
  <style lang="less" scoped>
.user-content { 
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-box {
    display: flex;
  }
  .search-btn {
    margin-right: 20px;
  }
}
</style>
    