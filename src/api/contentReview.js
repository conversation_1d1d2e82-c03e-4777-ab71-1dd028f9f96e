import $https from "@/assets/js/request"
//--------业务标签--------
// 列表
export const auditPage = (data) => {
    return $https.fetchGet('/cop/audit/page',data)
}
//搜索账号名称（初始化选择框）
export const accountListCity = (data) => {
  return $https.fetchGet('/cop/manage/account/list/city',data)
}
//获取发布平台
export const accountList = (data) => {
  return $https.fetchGet('/cop/manage/schedule/account/list',data)
}
//详情
export const auditDetail = (data) => {
  return $https.fetchGet('/cop/audit/detail',data)
}
//审核记录
export const auditDetailRec = (data) => {
  return $https.fetchGet('/cop/audit/detail/rec',data)
}
// 新增(草稿)
export const auditAdd = (data) => {
  return $https.fetchPost('/cop/audit/add',data,{'headers': {'Content-Type':'application/json'}})
}
// 编辑(草稿)
export const auditEdit = (data) => {
  return $https.fetchPost('/cop/audit/edit',data,{'headers': {'Content-Type':'application/json'}})
}
// 新增并提交审核
export const auditSubmitAdd = (data) => {
  return $https.fetchPost('/cop/audit/add/submit',data,{'headers': {'Content-Type':'application/json'}})
}
// 编辑并提交审核
export const auditSubmitEdit = (data) => {
  return $https.fetchPost('/cop/audit/edit/submit',data,{'headers': {'Content-Type':'application/json'}})
}
// 编辑并提交审核
export const batchDel = (data) => {
  return $https.fetchPost('/cop/audit/del/batch',data)
}
// 导出
export const auditExport = '/cop/audit/export'
// 删除
export const auditDel = (data) => {
  return $https.fetchPost('/cop/audit/del',data)
}