<!-- 视频 -->
<template>
  <a-spin :spinning="props.initTabs.pageLoading">
    <div class="tabs-content">
      <ul class="tabs-list">
        <li
          class="list-item"
          :key="item.id"
          v-for="item in props.initTabs.dataSource"
        >
          <div class="imasge-box">
            <video class="img-item" :src="item.localUrl"></video>
            <div class="mask-box">
              <EyeOutlined @click="goVideoDetail(item.localUrl)" />
              <a
                class="icon-download"
                title="下载"
                :href="item.localUrl"
                :download="item.templateName"
              >
                <CloudDownloadOutlined />
              </a>
              <CopyOutlined @click="copyMaterial(item.localUrl)" />
              <CloseOutlined @click="deleteMaterial(item.id)" />
            </div>
          </div>
          <p class="item-templateName">{{ item.templateName }}</p>
          <p class="item-updateTime">{{ item.updateTime }}</p>
        </li>
      </ul>
      <a-pagination
        class="tabs-pagination"
        v-model:current="props.initTabs.pagination.current"
        :total="props.initTabs.pagination.total"
        @change="handleTableChange"
      />
    </div>
  </a-spin>
</template>

<script setup>
import emitter from "@/assets/js/emitter";
const props = defineProps({
  initTabs: Object,
});
const handleTableChange = (pageNo, pageSize) => {
  // 点击分页处理
  props.initTabs.pageLoading = true;
  emitter.emit("handleTableChange", { pageNo, pageSize });
};
function goVideoDetail(url) {
  window.open(url)
}
//删除
const deleteMaterial = (id) => {
  emitter.emit('deleteMaterial',id)
}
//复制
const copyMaterial = (text) => {
  emitter.emit('copyMaterial',text)
}
</script>
<style lang="less" scoped>
.tabs-content {
  height: calc(100vh - 170px);
  overflow-y: scroll;
  background: #fff;
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .tabs-list {
    min-height: calc(100vh - 220px);
    display: flex;
    padding: 10px;
    flex-wrap: wrap;
    gap: 10px 30px;
  }
  .list-item {
    width: 200px;
    min-height: 225px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: content-box;
  }
  ::deep(.ant-image-img) {
    width: 200px;
  }
  .imasge-box {
    width: 200px;
    height: 200px;
    position: relative;
    overflow: hidden;
    padding: 2px;
  }
  .img-item {
    max-width: 200px;
    max-height: 200px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }
  .imasge-box:hover {
    .mask-box {
      opacity: 1;
    }
  }
  .mask-box {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    .anticon {
      color: #fff;
      font-size: 16px;
      margin-left: 5px;
      cursor: pointer;
    }
  }
  .item-templateName {
    font-size: 14px;
    color: #222;
    margin-top: 6px;
    margin-bottom: 3px;
    word-wrap: break-word;
    word-break: break-all;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 0 5px;
  }
  .item-updateTime {
    font-size: 12px;
    color: #606266;
    padding: 0 5px;
  }
  .tabs-pagination {
    display: flex;
    justify-content: flex-end;
    padding-right: 10px;
    padding-bottom: 10px;
  }
}
</style>
