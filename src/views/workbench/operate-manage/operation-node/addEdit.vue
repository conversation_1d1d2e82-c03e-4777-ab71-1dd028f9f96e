<!-- 运营节点新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    @ok="addEditSubmit"
  >
    <a-form>
      <a-form-item label="节点日期" v-bind="validateInfos.nodeDate">
        <a-date-picker
          :disabled="addEditFrom.id"
          v-model:value="addEditFrom.nodeDate"
        />
      </a-form-item>
      <a-form-item label="可策划节点" v-bind="validateInfos.name">
        <a-input
          v-model:value="addEditFrom.name"
          placeholder="请输入节点名称"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message, Form } from "ant-design-vue";
import { nodeEdit, nodeAdd } from "@/api/operationNode";
import emitter from '@/assets/js/emitter';
const props = defineProps({
  addEditData: Object,
});
const addEditFrom = props.addEditData.list
//表单规则验证数据
const addEditRules = reactive({
  name: [
    {
      required: true,
      message: "请输入节点名称",
    },
    {
      min: 1,
      max: 100,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  nodeDate: [
    {
      required: true,
      message: "请选择节点日期",
      trigger: "change",
      type: "object"
    },
  ],
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      let res = {};
      if (!addEditFrom?.id) {
        //新增
        const { name, nodeDate } = toRaw(addEditFrom);
        let fromData = {
          name: name,
          nodeDate: nodeDate.format('YYYY-MM-DD'),
        };
        res = await nodeAdd(fromData);
      } else {
        //编辑提交
        const { name, nodeDate, id } = toRaw(addEditFrom);
        let fromData = {
          name,
          id,
          nodeDate: nodeDate.format('YYYY-MM-DD'),
        };
        res = await nodeEdit(fromData);
      }
      if (res.retCode === "000000") {
        message.success("操作成功！")
        emitter.emit('addEditComplete',true)
      } else if(res.retCode === "000030") {
        message.error('已存在同名同日期节点');
      }else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
<style lang="less">
/deep/.ant-popover-inner-content {
  width: 160px !important;
}
.lunarCls /deep/.ant-calendar.ant-calendar-picker-container-content {
  width: 300px !important;
}
.lunarCls .ant-calendar-date {
  width: 40px !important;
  height: 40px;
  line-height: 16px;
}
</style>
