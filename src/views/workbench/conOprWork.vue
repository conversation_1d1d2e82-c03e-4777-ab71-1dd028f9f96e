<!-- 运营日历 -->
<template>
  <a-tabs class="top-operate" 
  destroyInactiveTabPane
  @change="changeTabs"
  v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="排期">
      <schedule/>
    </a-tab-pane>
    <a-tab-pane key="2" tab="数据">
      <msgSupplement/>
    </a-tab-pane>
  </a-tabs>
  <!-- <scheduleForm v-if="scheduleData.modalOpen" :scheduleData="scheduleData" />
  <msgSupplement v-if="msgData.modalOpen" :msgData="msgData" />
  <uploadImg v-if="uploadImgData.modalOpen" :uploadImgData="uploadImgData" /> -->
</template>
<script setup>
import schedule from "./con-opr-work/schedule.vue";
import msgSupplement from "./con-opr-work/msgSupplement.vue";
import emitter from "@/assets/js/emitter";
const activeKey = ref('1');
const changeTabs = ()=>{
  // dataSource.value = [];
  // getScheduleInit();
}
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("refreshWork");
  emitter.off("msgWork");
  localStorage.removeItem("searchDate")
});
</script>

<style lang="less" scoped>
.ant-tabs >:deep(.ant-tabs-nav){
  justify-content: flex-end;
  margin-bottom: 7px;
  .ant-tabs-nav-wrap {
    flex: none;
  }
} 
:deep(.ant-tabs-nav-list) {
  border: 1px solid rgba(51,65,85,.15);
  border-radius: 4px
}
.ant-tabs {
  :deep(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    color: #fff
  }
  :deep(.ant-tabs-tab+.ant-tabs-tab) {
    margin: 0
  }
  :deep(.ant-tabs-tab) {
    padding: 3px 13px;
    margin: 0;
}
}

:deep(.ant-tabs-tab-active) {
  background: #fe8308;
}
:deep(.ant-tabs-content-holder) {
  position: relative;
}
.ant-tabs :deep(.ant-tabs-ink-bar) {
  background: transparent;
}
.ant-tabs-top {
  :deep(.ant-tabs-tab::before) {
    width: 22px;
    height: 20px;
  }
}
.ant-table-body {
  .ant-btn {
    font-size: 12px;
    height: 28px;
    padding: 2px 6px;
    border-radius: 4px;
    position: relative;
    left: -35px;
  }
} 
.ant-tabs-top >:deep(.ant-tabs-nav::before) {
  border: 0;
}
.top-operate {
  margin-bottom: 0; 
  top: -38px;
}
.work-list {
  background: #fff;
  height: calc(100vh - 170px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .ant-tabs-top {
    margin-bottom: 0;
  }
  .audit-img-box {
    position: relative;
    top: -15px;
    display: flex;
  }
  .schedule-date-name {
    span {
      color: #1E293B;
      opacity: 0.5;
      font-size: 12px;
    }
    .node-name {
      color: #FF8986;
      opacity: 1;
    }
    .schedule-year {
      position: absolute;
      top: 0px;
      left: 2px;
      color: #FE8308;
      opacity: 0.5;
      font-size: 10px;
    }
  }
  .audit-name {
    color: #FE8308;
    cursor: pointer;
  }
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
  .ant-steps {
    position: relative;
    left: -50px;
    top: 3px;
  }
  .ant-steps-icon-dot {
    width: 20px !important;
    height: 20px !important;
  }
  .ant-steps .ant-steps-item-process .ant-steps-item-icon >.ant-steps-icon .ant-steps-icon-dot,
  .ant-steps .ant-steps-item-finish .ant-steps-item-icon >.ant-steps-icon .ant-steps-icon-dot {
    background: transparent;
    background-image: linear-gradient(270deg, #20D1F6 0%, rgba(32,209,246,0.50) 99%);
    cursor: pointer;
  }
  :where(.css-dev-only-do-not-override-11grni8).ant-steps .ant-steps-item-wait .ant-steps-item-icon >.ant-steps-icon .ant-steps-icon-dot {
    background: #E9E9E9;
  }
  :deep(.ant-steps.ant-steps-label-vertical .ant-steps-item) {
    width: 80px;
    flex: none;
  }
  :deep(.ant-steps.ant-steps-dot .ant-steps-item-tail) {
    top: 9px;
    left: 10px;
    width: 60%;
  }
  :deep(.ant-steps .ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-tail)::after {
    background: transparent;
    background-image: linear-gradient(270deg, #20D1F6 0%, rgba(32,209,246,0.50) 99%);
  }
  :deep(.ant-steps .ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-tail)::after {
    background-color: rgba(0, 0, 0, 0.15);
  }
  :deep(.ant-steps.ant-steps-dot .ant-steps-item-tail)::after{
    width: calc(100% - 25px);
    height: 1px;
    margin-inline-start: 24px;
  }
}

</style>
