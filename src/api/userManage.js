import $https from "@/assets/js/request"

// 列表
export const userPage = (data) => {
    return $https.fetchGet('/cop/manage/user/page',data)
}
// 编辑
export const userEdit = (data) => {
    return $https.fetchPost('/cop/manage/user/edit',data)
}
// 新增
export const userAdd = (data) => {
    return $https.fetchPost('/cop/manage/user/add',data)
}
// 新增编辑中机构的树结构
export const cityAllTree = (data) => {
    return $https.fetchGet('/cop/manage/dept/city/all/tree',data)
}
// 删除
export const userDel = (data) => {
    return $https.fetchPost('/cop/manage/user/del',data)
}
//授权账号适配树
export const relTree = (data) => {
    return $https.fetchGet('/cop/manage/account/user/rel/tree',data)
}
//设置授权账号适配
export const relSave = (data) => {
    return $https.fetchPost('/cop/manage/user/account/rel/save',data)
}
//授权菜单的机构树
export const userProvince = (data) => {
    return $https.fetchGet('/cop/manage/user/province',data)
}
//授权菜单的菜单下拉
export const menuDeptTree = (data) => {
    return $https.fetchGet('/cop/manage/menu/dept/tree',data)
}
//授权菜单的保存
export const menuRelSave = (data) => {
    return $https.fetchPost('/cop/manage/user/menu/rel/save',data)
}
//授权当前机构菜单时的详情
export const menuRelDetail = (data) => {
    return $https.fetchGet('/cop/manage/user/menu/rel/detail',data)
}
