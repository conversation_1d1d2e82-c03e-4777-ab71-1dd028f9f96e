<!-- 账号授权 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.setData.modalOpen"
    title="适用账号"
    okText="确定"
    :confirm-loading="modalLoading"
    @ok="setAccSubmit"
  >
  <a-tree
    v-model:checkedKeys="checkedKeys"
    checkable
    :fieldNames="{ children: 'children', title: 'label', key: 'id' }"
    :tree-data="props.setData.deptListDatas"
  >
  </a-tree>
  </a-modal>
</template> 

<script setup>
import { message } from "ant-design-vue";
import { relSave } from '@/api/userManage';
import emitter from "@/assets/js/emitter";
const props = defineProps({
  setData: Object,
  accountId: Array
});
//账号树形控件
//这两个值只接受ref的值
const checkedKeys = ref([]); //选中复选框
Object.assign(checkedKeys.value,props.accountId)
//提交数据
const modalLoading = ref(false)
const setAccSubmit = async () => {
  let checkedIds = []
  checkedKeys.value.forEach((el,i) => {
    if(el < 100000) {
      checkedIds.push(el)
    }
  });
  if(!checkedIds.length) {
    message.error("请选择需要配置的账号");
    return
  }
  const res = await relSave({ userId: props.setData.userId, accountId: checkedIds.join(',') });
  modalLoading.value = false
  if (res.retCode === "000000") {
    message.success("操作成功！");
    emitter.emit("addEditUser", true);
  } else {
    message.error(res.retMsg);
  }


}
// const addEditFrom = props.setData.list
</script>

<style lang="less" scoped>
</style>
