<!-- 智能写作 -->
<template>
  <div class="write-content" :class="{'is-empty':historyData.isEmpty}">
    <a-tabs
      @change="getNewsList"
      v-model:activeKey="activeKey"
      destroyInactiveTabPane
    >
      <a-tab-pane key="1">
        <template #tab> 
          <span>
            <img v-if="activeKey==='1'" src="../../assets/image/write-active.png" alt="">
            <img v-else src="../../assets/image/write-icon.png" alt="">
            文章创作
          </span>
        </template>
        <write @emitHistory="showDrawer" :historyData="historyData"/>
      </a-tab-pane>
      <a-tab-pane key="2">
        <template #tab>
          <span>
            <img v-if="activeKey==='2'" src="../../assets/image/change-active.png" alt="">
            <img v-else src="../../assets/image/change-icon.png" alt="">
            文章改写
          </span>
        </template>
        <articleEdit @emitHistory="showDrawer" :historyData="historyData"/>
      </a-tab-pane>
      <a-tab-pane key="3">
        <template #tab>
          <span>
            <img v-if="activeKey==='3'" src="../../assets/image/Imitative-active.png" alt="">
            <img v-else src="../../assets/image/Imitative-icon.png">
            文章仿写
          </span>
        </template>
        <imitateEdit @emitHistory="showDrawer" :historyData="historyData"/>
      </a-tab-pane>
      <a-tab-pane key="4">
        <template #tab>
          <span>
            <img v-if="activeKey==='4'" src="../../assets/image/polish-active.png" alt="">
            <img v-else src="../../assets/image/polish-icon.png" alt="">
            文章润色
          </span>
        </template>
        <styleEdit @emitHistory="showDrawer" :historyData="historyData"/>
      </a-tab-pane>
    </a-tabs>
    <a-drawer 
      v-if="historyData.isEmpty"
      :width="210"
      :mask="false"
      :closable="false"
      placement="right" 
      class="history-list-drawer"
      :get-container="false"
      :force-render="true"
      :open="historyListShow">
      <div class="switch-off-box">
        <img @click="showDrawer" v-if="historyData.isEmpty" class="switch-off" src="../../assets/image/switch-off.png" alt="">
      </div>
      <a-spin :spinning="historyLoading">
        <div class="loading-box">
            <div class="history-list-box" ref="drawerRef">
              <p class="date-time" v-if="historyData.currentDate.length">当天</p>
              <div 
                class="history-list"
                @click="historyTag(item)"
                v-for="item in historyData.currentDate" 
                :key="item.createTime">
                <div class="history-row">
                  <p class="history-title">{{item.userParam.require}}</p>
                  <p class="history-content">{{item.content}}</p>
                </div>
              </div>
              <p class="date-time" v-if="historyData.weeks.length">最近7天</p>
              <div class="history-list" 
              @click="historyTag(item)"
              v-for="item in historyData.weeks" 
              :key="item.createTime">
                <div class="history-row">
                  <p class="history-title">{{item.userParam.require}}</p>
                  <p class="history-content">{{item.content}}</p>
                </div>
              </div>
              <p class="date-time" v-if="historyData.month.length">最近30天</p>
              <div class="history-list" 
              @click="historyTag(item)"
              v-for="item in historyData.month" 
              :key="item.createTime">
                <div class="history-row">
                  <p class="history-title">{{item.userParam.require}}</p>
                  <p class="history-content">{{item.content}}</p>
                </div>
              </div>
              <div v-if="params.pageNum === 0 && !historyData.isEmpty" class="no-more">暂无更多数据！</div>
            </div>
            <!-- <a-empty v-if="historyData.isEmpty"/> -->
        </div>
      </a-spin>
    </a-drawer>
    <a-drawer 
      v-if="historyData.isEmpty"
      :width="35"
      :mask="false"
      :closable="false"
      placement="right" 
      :get-container="false"
      class="history-drawer"
      :open="historyShow">
      <div class="switch-on-box">
        <img @click="showDrawer" v-if="historyData.isEmpty" class="switch-on" src="../../assets/image/switch-on.png" alt="">
      </div>
      <div class="history-icon">
        <ClockCircleFilled />
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import { message } from "ant-design-vue";
import write from '@/views/intelligent-center/intelligent-write/write.vue'
import articleEdit from '@/views/intelligent-center/intelligent-write/edit.vue'
import imitateEdit from '@/views/intelligent-center/intelligent-write/imitate.vue'
import styleEdit from '@/views/intelligent-center/intelligent-write/style.vue'
import emitter from '@/assets/js/emitter';
import { writeHistory } from "@/api/intelligentWrite";
const historyShow = ref(false);
const historyListShow = ref(false);
const historyLoading = ref(true);
const drawerRef = ref();
// 监听路由更改菜单数据
const route = useRoute();

//历史数据
let historyData = ref({
  currentDate:[],//当日
  weeks:[],//7天内
  month: [],//一月
  isEmpty: false //空状态
})
const showDrawer = () => {
  historyShow.value = !historyShow.value;
  historyListShow.value = !historyListShow.value;
  if(historyListShow.value) {
    params.pageNum = 1;
    historyData.value.currentDate = [];
    historyData.value.weeks = [];
    historyData.value.month = [];
    getHistory();
  }
};
const params = {
  pageNum: 1,
  pageSize: '20',
  writeFlag: '1'
}
async function getHistory() {
  if(params.pageNum === 0) {
    return false
  }
  historyLoading.value = true;
  const res = await writeHistory(params);
  historyLoading.value = false;
  if(res.retCode === '000000') {
    params.pageNum = res.data.nextPage;
    if(historyData.value.isEmpty === false && res.data.total > 0) {
      historyData.value.isEmpty = true;
      historyShow.value = true;
    }
    res.data.list.forEach(val => {
      val.userParam = JSON.parse(val.userParam);
      if(val.dateFlag === 1) {
        historyData.value.currentDate.push(val);
      }else if(val.dateFlag === 2) {
        historyData.value.weeks.push(val);
      }else {
        historyData.value.month.push(val);
      }
    });
    nextTick(()=>{
      if (drawerRef.value) {
        drawerRef.value.addEventListener('scroll', handleScroll);
      }
    })
  }else {
    message.error(res.retMsg);
  }
}
// 定义滚动函数
const handleScroll = () => {
	const tableContainer = drawerRef.value;
	const scrollPosition = tableContainer.scrollTop;
	const isTop = scrollPosition === 0;
	const isBottom = tableContainer.scrollHeight - scrollPosition === tableContainer.clientHeight;
	if (isBottom) {
    getHistory();
	}
}
// 移除scroll监听
onUnmounted(()=>{
	nextTick(()=>{
		if (drawerRef.value) {
			drawerRef.value.removeEventListener('scroll', handleScroll);
		}
	})
})
//回显历史数据
function historyTag(item) {
  emitter.emit("historyTag", item);
  historyShow.value = false;
}
//组件卸载后销毁emitter
onUnmounted(()=>{
  emitter.off('historyTag');
})
const activeKey = ref('1');
watch(()=>route.query,(newVal)=>{
  if(JSON.stringify(newVal) !== '{}') {
    activeKey.value = newVal.act
  }
},{ immediate: true })
function getNewsList(value) {
  params.writeFlag = value;
  historyData.value.isEmpty = false;
  historyShow.value = false;
  historyListShow.value = false;
  params.pageNum = 1;
  historyData.value.currentDate = [];
  historyData.value.weeks = [];
  historyData.value.month = [];
  getHistory();
}
onMounted(()=>{
  getHistory()
})
</script>
<style lang="less" scoped>
.ant-layout {
  background: #FFF;
}
.is-empty {
  padding-right: 28px;
}
.write-content {
  padding-top: 10px;
  :deep(.ant-tabs-content-holder) {
    height: calc(100vh - 170px);
    background: #FFFFFF;
    border: 1px solid #E2E8F0;
    box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  }
  .ant-tabs-top{
    :deep(.ant-tabs-nav::before) {
      border: 0;
    }
  }
  :deep(.ant-tabs-top >.ant-tabs-nav) {
    margin: 0 0 10px 0;
  }
  :deep(.ant-tabs-tab) {
    color: #1E293B;
    font-size: 14px;
    padding: 6px 15px;
    border: 1px solid rgba(51, 65, 85, 0.15);
    border-radius: 4px;
    margin-left: 0;
    margin-right: 20px;
    background-color: #fff;
    img {
      width: 16px;
      vertical-align: top;
      margin-top: 3px;
      margin-right: 3px;
    }
  }
  :deep(.ant-tabs-tab-active) {
    color: #1E293B;
    background: #FFEEDA;
    border: 1px solid #FFDEC2;
  }
  :deep(.ant-tabs-ink-bar) {
    width: 0 !important;
  }
  :deep(.ant-drawer-inline) {
    position: fixed;
    top: 64px;
  }
  :deep(.ant-drawer-content) {

  }
  :deep(.ant-drawer-content-wrapper) {
    border: 1px solid #E2E8F0;
    box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  }
  :deep(.history-drawer) {
    .ant-drawer-body {
      overflow: hidden;
      padding: 0; 
    }
  }
  :deep(.history-list-drawer) {
    .ant-drawer-body {
      padding: 0;
    }
    .ant-empty {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }    
  }
  .ant-drawer-body {
    position: relative;
    .switch-off-box {
      width: 16px;
      position: absolute;
      left: -16px;
      top: 50%;
      transform: translateY(-50%);
    }
    .switch-off,.switch-on {
      width: 100%;
      vertical-align: top;
      cursor: pointer;
    }
    .switch-on-box {
      width: 16px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    .history-icon {
      text-align: center;
      color: #94A3B8;
      padding: 10px 0;
      font-size: 20px;
    }
    .history-list {
      width: 172px;
      overflow: hidden;
    }
    .date-time {
      font-size: 12px;
      color: #94A3B8;
      margin-bottom: 10px;
    }
    .history-row {
      margin-bottom: 10px;
      background: rgba(148, 163, 184, .1);
      border-radius: 2px;
      border-radius: 2px;
      padding: 8px 5px;
      p {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        cursor: pointer;
      }
    }
    .history-row:last-child {
      margin-bottom: 20px;
    }
    .history-title {
      font-size: 14px;
      color: #1E293B;
      font-weight: bold;
      padding: 3px 0 5px;
      -webkit-line-clamp: 2;
    }
    .history-content {
      font-size: 12px;
      color: #1E293B;
      -webkit-line-clamp: 3;
      text-align: justify;
    }
  }
  .loading-box {
    height: calc(100vh - 90px);
    
  }
  .history-list-box {
    height: calc(100vh - 82px);
    overflow-y: auto;
    padding: 10px;
  }
  .no-more {
    font-size: 12px;
    color: #94A3B8;
    text-align: center;
  }
}
</style>
