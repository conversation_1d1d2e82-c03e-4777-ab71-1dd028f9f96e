import $https from "@/assets/js/request"

// 列表
export const subList = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/sub/list',data)
}
// 编辑 
export const subEdit = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/sub/edit?chkCurWx='+chkCurWx,data)
}
// 新增
export const subAdd = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/sub/add?chkCurWx='+chkCurWx,data)
}
// 删除
export const subDel = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/sub/del',data)
}