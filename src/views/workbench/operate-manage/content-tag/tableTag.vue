<!-- 业务标签 -->
<template>
  <div class="biz-tag">
    <a-button type="primary" @click="showModal()" class="add-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增标签
    </a-button>
    <div class="tag-list">
      <a-table
        :dataSource="dataSource"
        :columns="tableColumns"
        childrenColumnName="list"
        size="small"
        :pagination="pagination"
        @change="handleTableChange"
        :loading="pageLoading"
        :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'broadCategory'">
            {{ record.label }}
          </template>
          <template v-if="column.dataIndex === 'subclass'">
            <a-space>
              <a-tag
                :bordered="false"
                v-for="item in record.children"
                :key="item"
                :color="acquiesceColor[index]"
              >
                {{ item.label }}
              </a-tag>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'operate'">
            <div class="operate-box">
              <span class="edit-btn" @click="showModal(record)">编辑</span>
              <span class="del-btn" @click="delFun(record)">删除</span>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <addEdit
      v-if="addEditData.modalOpen"
      :addEditData="addEditData"
      @addEditTag="callbackDddEditTag"
    />
  </div>
</template>

<script setup>
import useGetList from "@/hooks/useGetList";
import addEdit from "./addEdit.vue";
import useDel from "@/hooks/useDel";
import { tagDel } from "@/api/contentTag";
import { message } from "ant-design-vue";
const props = defineProps({
  tagData: Object,
});
const acquiesceColor = ref([
  "red",
  "volcano",
  "orange",
  "gold",
  "lime",
  "green",
  "cyan",
  "blue",
  "geekblue",
  "purple",
]);
const pageLoading = ref(false);
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "一类",
    dataIndex: "broadCategory",
  },
  {
    title: "二类",
    dataIndex: "subclass",
  },
  {
    title: "操作",
    dataIndex: "operate",
  },
]);
// 分页/筛选
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
// 获取列表数据
onMounted(() => {
  useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination);
});
const handleTableChange = (pag, filters, sorter) => {
  useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination, {
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
//新增/编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: props.tagData.addText,
  addTag: props.tagData.addTag,
  list: {
    name: "",
    children: [],
  },
});
const showModal = (item = { name: "", children: [], id: null }) => {
  Object.assign(addEditData.list, item);
  if (item.id) {
    addEditData.modalTitle = props.tagData.editText;
  } else {
    addEditData.modalTitle = props.tagData.addText;
    addEditData.list.label = "";
  }
  addEditData.modalOpen = true;
};

const callbackDddEditTag = () => {
  addEditData.modalOpen = false;
  useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination);
};
// 删除数据
const delFun = async (list) => {
  useDel(tagDel, { firstTagId: list.id }, pageLoading, (res) => {
    if (res.retCode === "000000") {
      useGetList(props.tagData.tagPage, pageLoading, dataSource, pagination);
      message.success("操作成功！");
    } else {
      message.error(res.retMsg);
    }
  });
};
</script>

<style lang="less" scoped>
.biz-tag {
  background: #fff;
  padding: 10px;
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
  .add-btn {
    position: absolute;
    right: 0;
    top: -54px;
  }
}
.tag-list {
  background: #fff;
  height: calc(100vh - 200px);
  .operate-box {
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .edit-btn {
      color: #FE8308;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
</style>
