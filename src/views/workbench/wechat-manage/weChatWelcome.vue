<!-- 文本素材 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <weChatConfig />
    </div>
    <a-button type="primary" @click="showModal()" class="submit-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增欢迎语
    </a-button>
  </div>
  <div class="user-content">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'adminFlag'">
          {{ record.adminFlag === 1 ? "是" : "否" }}
        </template>
        <template v-if="column.dataIndex === 'msgTypeName'">
          {{ record.msgTypeName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'accountName'">
          {{ record.accountName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span v-if="record.adminFlag !== 1" @click="showModal(record)"
              >编辑</span
            >
            <span
              class="del-btn"
              v-if="record.adminFlag !== 1"
              @click="delFun(record)"
              >删除</span
            >
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <addEdit v-if="addEditData.modalOpen" :addEditData="addEditData" />
</template>
  
<script setup>
import { subList, subDel } from "@/api/weChatWelcome";
import weChatConfig from "@/components/weChat-config/weChatConfig.vue";
import addEdit from "./weChat-welcome/addEdit.vue";
import useGetList from "@/hooks/useGetList";
import { message } from "ant-design-vue";
import emitter from "@/assets/js/emitter";
import useDel from "@/hooks/useDel";
let dataSource = ref([]);
const tableColumns = reactive([
  {
    title: "消息类型",
    dataIndex: "msgTypeName",
    width: 120,
  },
  {
    title: "素材名称",
    dataIndex: "templateName",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    width: 160,
  },
  {
    title: "操作",
    dataIndex: "operate",
    width: "240px",
    fixed: "right",
  },
]);
const pageLoading = ref(false);
//表单数据
const searchForm = reactive({
  name: "",
  mobile: "",
});
// 获取列表数据
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
});
const weChatId = ref("");
onMounted(() => {
  emitter.on("initWeChatId", (value) => {
    weChatId.value = value;
    if (value) {
      useGetList(subList, pageLoading, dataSource, pagination, {
        pageNo: 1,
        pageSize: 10,
        chkCurWx: value,
      });
    }
  });
});
//分页处理
const handleTableChange = (pag) => {
  useGetList(subList, pageLoading, dataSource, pagination, {
    pageNo: pag.current,
    pageSize: pag.pageSize,
    chkCurWx: weChatId.value,
  });
};
//新增/编辑数据
const addEditData = reactive({
  modalOpen: false,
  modalTitle: "新增欢迎语",
  weChatId: weChatId.value,
  list: {},
});
const showModal = (
  item = { msgType: "", templateId: "", templateName: "" }
) => {
  addEditData.list = {}
  if (item?.id) {
    addEditData.modalTitle = "编辑欢迎语";
    addEditData.list.templateName = `${addEditData.list.templateName}-${addEditData.list.msgTypeName}`;
  } else {
    addEditData.modalTitle = "新增欢迎语";
  }
  Object.assign(addEditData.list, item);
  addEditData.modalOpen = true;
  addEditData.weChatId = weChatId.value;
};
//新增/编辑之后更新数据
emitter.on("addEditWelcome", (value) => {
  if (value) {
    addEditData.modalOpen = false;
    useGetList(subList, pageLoading, dataSource, pagination, {
      pageNo: 1,
      pageSize: 10,
      chkCurWx: weChatId.value,
    });
  }
});
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("addEditWelcome");
  emitter.off("initWeChatId");
});
// 删除数据
const delFun = async (list) => {
  useDel(
    subDel,
    { id: list.id, chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        useGetList(subList, pageLoading, dataSource, pagination, {
          pageNo: 1,
          pageSize: 10,
          chkCurWx: weChatId.value,
        });
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    }
  );
};
</script>
  
<style lang="less" scoped>
.user-content {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
}
.top-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-box {
    display: flex;
  }
}
</style>
  