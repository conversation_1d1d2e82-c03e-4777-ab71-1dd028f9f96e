import $https from "@/assets/js/request"

// 页面初始化数据
export const scheduleInit = (data) => {
    return $https.fetchGet('/cop/manage/schedule/init',data)
}
// 列表
export const scheduleList = (data) => {
    return $https.fetchGet('/cop/manage/schedule/list',data)
}
// 数据列表
export const scheduleDataList = (data) => {
    return $https.fetchGet('/cop/manage/schedule/data/list',data)
}
// 排期-一类标签下拉数据
export const firstTree = (data) => {
    return $https.fetchGet('/cop/manage/tag/first/tree',data)
}
// 排期-二类标签下拉数据
export const secondList = (data) => {
    return $https.fetchGet('/cop/manage/tag/first/second/list',data)
}
// 排期
export const scheduleAdd = (data) => {
    return $https.fetchPost('/cop/manage/schedule/add',data)
}
// 排期
export const scheduleEdit = (data) => {
    return $https.fetchPost('/cop/manage/schedule/edit',data)
}
// 删除
export const scheduleDel = (data) => {
    return $https.fetchPost('/cop/manage/schedule/del',data)
}
// 排期
export const publishEdit = (data) => {
    return $https.fetchPost('/cop/manage/schedule/edit',data)
}
// 数据编辑
export const dataEdit = (data) => {
    return $https.fetchPost('/cop/manage/schedule/data/edit',data,{'headers': {'Content-Type':'application/json'}})
}
// 信息补充-获取标题下拉数据
export const publishTitle = (data) => {
    return $https.fetchGet('/cop/manage/schedule/publish/title',data)
}
// 信息补充-获取url
export const publishUrl = (data) => {
    return $https.fetchGet('/cop/manage/schedule/publish/url',data)
}
// 信息平台信息
export const accountList = (data) => {
    return $https.fetchGet('/cop/manage/schedule/account/list',data)
}
// 信息补充-获取阅读量
export const readCount = (data) => {
    return $https.fetchGet('/cop/manage/schedule/publish/readCount',data)
}
// 信息补充-获取分享数
export const shareCount = (data) => {
    return $https.fetchGet('/cop/manage/schedule/publish/shareCount',data)
}
// 初审-上传图片
export const uploadAuditFirst = (data) => {
    return $https.fetchGet('/cop/manage/schedule/upload/audit/first',data)
}
// 二审-上传图片
export const uploadAuditSecond = (data) => {
    return $https.fetchGet('/cop/manage/schedule/upload/audit/second',data)
}
// 终审-上传图片
export const uploadAuditFinal = (data) => {
    return $https.fetchGet('/cop/manage/schedule/upload/audit/final',data)
}