<!-- 账号总览账号文章数 -->
<template>
  <a-spin :spinning="spinLoading">
    <div class="top-box">
      <!-- 省份账号总数 -->
      <div class="account-number-box">
        <div class="title-text">阵地账号总数</div>
        <div class="account-bottom-box">
          <div class="account-number-echarts">
            <div class="progress-large">
              <a-progress
                stroke-color="#21D1F5"
                trailColor="#f6f7f8"
                :size="160"
                type="circle"
                :strokeWidth="4.06"
                :percent="(pageData.weChatCount / pageData.totalCount) * 100"
                :show-info="false"
              />
              <div class="progress-middle">
                <a-progress
                  stroke-color="#fcda69"
                  trailColor="#f6f7f8"
                  :size="130"
                  type="circle"
                  :strokeWidth="5"
                  :percent="(pageData.tikTokCount / pageData.totalCount) * 100"
                  :show-info="false"
                />
                <div class="progress-middle">
                  <a-progress
                    stroke-color="#FDA7B2"
                    trailColor="#f6f7f8"
                    :size="100"
                    type="circle"
                    :strokeWidth="6.5"
                    :percent="
                      (pageData.weChatVideoCount / pageData.totalCount) * 100
                    "
                    :show-info="false"
                  />
                  <div class="account-number-text" :class="pageData.totalCount>=1000?'number-text-small':''">
                    <countTo 
                    :start-val="0" 
                    :end-val="pageData.totalCount || 0" 
                    :duration="2000"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="account-list-box">
            <div class="account-list">
              <div class="account-list-top">
                <p class="account-color account-large-color"></p>
                <p>公众号总数</p>
              </div>
              <p class="account-count">
                <countTo :start-val="0" :end-val="pageData.weChatCount || 0" :duration="2000"/>
              </p>
            </div>
            <div class="account-list">
              <div class="account-list-top">
                <p class="account-color account-middle-color"></p>
                <p>抖音号总数</p>
              </div>
              <p class="account-count">
                <countTo :start-val="0" :end-val="pageData.tikTokCount || 0" :duration="2000"/>
              </p>
            </div>
            <div class="account-list">
              <div class="account-list-top">
                <p class="account-color account-small-color"></p>
                <p>视频号总数</p>
              </div>
              <p class="account-count">
                <countTo :start-val="0" :end-val="pageData.weChatVideoCount || 0" :duration="2000"/>
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 在网文章数 -->
      <div class="network-box">
        <div class="network-list">
          <div class="title-text">在网文章数（公众号）</div>
          <div class="content-box">
            <div class="number-con">
              <span class="network-number">
                <countTo :start-val="0" :end-val="pageData.weChatArticleCount || 0" :duration="2000"/>
              </span>
              篇
            </div>
            <img
              src="@/assets/image/wechat-img.png"
              class="network-img"
              alt=""
            />
          </div>
        </div>
        <div class="network-list">
          <div class="title-text">在网视频数（抖音）</div>
          <div class="content-box">
            <div class="number-con">
              <span class="network-number">
                <countTo :start-val="0" :end-val="pageData.tikTokVideoCount || 0" :duration="2000"/>
              </span>
              个
            </div>
            <img
              src="@/assets/image/tiktok-img.png"
              class="network-img"
              alt=""
            />
          </div>
        </div>
      </div>
      <!-- 7日更新账号数（公众号） -->
      <div class="public-account-box">
        <div class="public-account-list">
          <div class="title-text">7日更新账号数（公众号）</div>
          <div class="content-box">
            <div class="number-con">
              <span class="network-number">
                <countTo :start-val="0" :end-val="pageData.sevenDayUpdateWeChatCount || 0" :duration="2000"/>
              </span>
              个
            </div>
            <div class="number-proportion">
              <countTo :start-val="0" :end-val="pageData.sevenDayUpdateWeChatProportion || 0" :duration="2000"/>%
            </div>
          </div>
          <div class="proportion-box seven-proportion">
            <a-progress
              trailColor="#f6f7f8"
              :stroke-color="{
                '0%': 'rgba(32,209,246,0.50)',
                '100%': '#20D1F6',
              }"
              :percent="pageData.sevenDayUpdateWeChatProportion"
              :show-info="false"
            />
          </div>
        </div>
        <div class="public-account-list">
          <div class="title-text">15日更新账号数（公众号）</div>
          <div class="content-box">
            <div class="number-con">
              <span class="network-number">
                <countTo :start-val="0" :end-val="pageData.fifteenDayUpdateWeChatCount || 0" :duration="2000"/>
              </span>
              个
            </div>
            <div class="number-proportion">
              <countTo :start-val="0" :end-val="pageData.fifteenDayUpdateWeChatProportion || 0" :duration="2000"/>%
            </div>
          </div>
          <div class="proportion-box seven-proportion">
            <a-progress
              trailColor="#f6f7f8"
              :stroke-color="{
                '0%': 'rgba(32,209,246,0.50)',
                '100%': '#20D1F6',
              }"
              :percent="pageData.fifteenDayUpdateWeChatProportion"
              :show-info="false"
            />
          </div>
        </div>
      </div>
      <!-- 超过30日未更新（公众号） -->
      <div class="not-updated-box">
        <div class="title-text not-updated-text">超过30日未更新（公众号）</div>
        <div class="content-box">
          <div class="not-update-m-con">
            <span class="network-number">
              <countTo :start-val="0" :end-val="pageData.notUpdateMoreThirtyDayWeChatCount || 0" :duration="2000"/>
            </span>
            个
          </div>
          <div class="number-proportion not-update-m-proportion">
            <countTo :start-val="0" :end-val="pageData.notUpdateMoreThirtyDayWeChatProportion || 0" :duration="2000"/>%
          </div>
        </div>
        <div class="proportion-box m-proportion">
          <a-progress
            trailColor="#ffe6e9"
            :stroke-color="{
              '0%': 'rgba(253,164,175,0.50)',
              '100%': '#FDA4AF',
            }"
            :percent="pageData.notUpdateMoreThirtyDayWeChatProportion"
            :show-info="false"
          />
        </div>
        <div class="not-u-m-l">
          <div
            class="not-u-m"
            v-for="(item, index) in pageData.notUpdateMoreThirtyDayWeChat"
            :key="index"
          >
            {{ item }}
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>
 
<script setup>
import { CountTo } from 'vue3-count-to'
import { message } from "ant-design-vue";
import { accountOverview } from "@/api/accountOverview";
import emitter from "@/assets/js/emitter";
const pageData = ref({});
const spinLoading = ref(true)
const getAccountOverview = async (data = null) => {
  const res = await accountOverview();
  if (res.retCode === "000000") {
    pageData.value = res.data;
    emitter.emit("initChart", pageData.value);
    spinLoading.value = false
  } else {
    message.error(res.retMsg);
  }
};
onMounted(() => {
  getAccountOverview();
});
</script>

<style lang="less" scoped>
.top-box {
  height: 295px;
  display: flex;
  margin-bottom: 28px;
}
.title-text {
  font-size: 16px;
  color: #1e293b;
  border-bottom: 1px solid #f1f5f9;
  padding: 15px;
}
.account-number-box {
  width: 365px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  border-radius: 2px;
}
.network-box,
.public-account-box {
  flex: 1;
  display: flex;
  margin-left: 8px;
  flex-direction: column;
  justify-content: space-between;
}
.network-img {
  width: 44px;
}
.content-box {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  padding: 15px 20px 0;
  margin-bottom: 5px;
}
.number-con {
  font-size: 16px;
  color: #1e293b;
}
.not-update-m-con {
  font-size: 16px;
  color: #881337;
}
.network-number {
  font-size: 34px;
  font-weight: bold;
}
.number-proportion {
  padding: 5px 10px;
  font-size: 14px;
  color: #fff;
  background-image: linear-gradient(90deg, #FE7209 0%, #FE8409 100%);
  box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.5);
  border-radius: 11px;
}
.not-update-m-proportion {
  background-image: linear-gradient(
    90deg,
    #fb7185 0%,
    rgba(244, 63, 94, 0.7) 100%
  );
  box-shadow: 0 2px 4px 0 rgba(244, 63, 94, 0.5);
}
.proportion-box {
  padding: 0 20px;
}
.seven-proportion {
  .ant-progress-line {
    margin-bottom: 0;
  }
  :deep(.ant-progress-inner) {
    // overflow: inherit;
  }
  :deep(.ant-progress-bg) {
    height: 20px !important;
    box-shadow: 0 1px 4px 0 rgba(52, 212, 246, 0.6);
    background-color: transparent;
  }
}
.m-proportion {
  .ant-progress-line {
    margin-bottom: 30px;
  }
  :deep(.ant-progress-inner) {
    // overflow: inherit;
  }
  :deep(.ant-progress-bg) {
    height: 20px !important;
    box-shadow: 0 1px 4px 0 rgba(253, 168, 178, 0.6);
    background-color: transparent;
  }
}
.network-list,
.public-account-list {
  height: 143px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  border-radius: 2px;
}
.not-updated-box {
  width: 240px;
  margin-left: 8px;
  background-image: linear-gradient(
    179deg,
    #ffffff 0%,
    #fff5f5 52%,
    #ffedee 100%
  );
  border: 1px solid #e7d5d7;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  border-radius: 2px;
  .not-updated-text {
    border-bottom: 1px solid #f9ebec;
  }
}
.account-bottom-box {
  display: flex;
  justify-content: space-between;
  padding: 23px;
}
.account-number-text {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  font-weight: bold;
  color: #1e293b;
}
.number-text-small {
  font-size: 32px;
}
.account-number-echarts {
  width: 165px;
  :deep(.ant-progress-inner) {
    background-color: transparent;
  }
}
.account-list-box {
  width: 115px;
  padding-top: 15px;
}
.account-list {
  padding: 5px;
  background: #ffffff;
  border: 1px solid #f1f5f9;
  box-shadow: 0 1px 6px -1px rgba(15, 23, 42, 0.08);
  border-radius: 2px;
  margin-bottom: 10px;
}
.account-list-top {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #64748b;
  margin-bottom: 5px;
}
.account-color {
  width: 6px;
  height: 6px;
  margin-right: 5px;
}
.account-large-color {
  background-color: #21d1f5;
}
.account-middle-color {
  background-color: #fcda69;
}
.account-small-color {
  background-color: #fda7b2;
}
.account-count {
  font-size: 16px;
  color: #1e293b;
  font-weight: bold;
  padding-left: 12px;
}
.progress-large {
  width: 100%;
  position: relative;
}
.progress-middle {
  position: absolute;
  top: 15px;
  left: 15px;
}
.not-u-m-l {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0 20px;
}
.not-u-m {
  background: #ffe4e6;
  border-radius: 13px;
  font-size: 14px;
  color: #881337;
  padding: 5px 15px;
  margin-bottom: 15px;
}
</style>
