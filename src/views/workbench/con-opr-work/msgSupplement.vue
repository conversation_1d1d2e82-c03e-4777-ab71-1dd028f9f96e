<!-- 数据 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <a-date-picker
        :allowClear="false"
        picker="month"
        placeholder="请选择时间"
        v-model:value="searchForm.searchDate"
      />
      <a-button type="primary" :disabled="pageLoading" @click="searchFun(true)" class="submit-btn">
        <template #icon>
          <SearchOutlined />
        </template>
        查询
      </a-button>
    </div>
  </div>
  <div class="work-list">
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      :pagination="false"
      ref="msgTableRef"
      size="small"
      bordered
      :loading="pageLoading"
      :scroll="{ x: 1200, y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'scheduleDateName'">
          <div class="schedule-date-name">
            {{ record.scheduleDateName }} <br>
            <template v-if="record.nodeName">
              <span class="node-name">{{ record.nodeName }}</span>
            </template>
            <template v-else>
              <span v-if="record.scheduleWeek === 1">星期日</span>
              <span v-if="record.scheduleWeek === 2">星期一</span>
              <span v-if="record.scheduleWeek === 3">星期二</span>
              <span v-if="record.scheduleWeek === 4">星期三</span>
              <span v-if="record.scheduleWeek === 5">星期四</span>
              <span v-if="record.scheduleWeek === 6">星期五</span>
              <span v-if="record.scheduleWeek === 7">星期六</span>
            </template>
          </div>
        </template>
        <template v-if="column.dataIndex === 'platform' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              {{ dataItem.accountName }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'title' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              <a-tooltip placement="topLeft" color="#fff" >
                <template #title>
                  <span class="ant-work-tooltip">{{ dataItem.title || "-" }}</span>
                </template>
                {{ dataItem.title || "-" }}
              </a-tooltip>
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'url' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              <a :href="dataItem.url" target="_blank">
                {{ dataItem.url || "-" }}
              </a>
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'readCount' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              {{ dataItem.readCount || "-" }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'shareCount' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              {{ dataItem.shareCount || "-" }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'likeCount' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              {{ dataItem.likeCount || "-" }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'watchCount' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              {{ dataItem.watchCount || "-" }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'commentCount' && record.scheduleDataInfo">
          <div class="td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
            <span
              v-for="dataItem in item.scheduleDataAcctInfo"
              :key="dataItem.id"
              class="count-list"
            >
              {{ dataItem.commentCount || "-" }}
            </span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'operate' && record.scheduleDataInfo">
            <div class="operate-box td-list" v-for="item in record.scheduleDataInfo" :key="item.id">
              <span 
                class="count-list"
                v-for="(dataItem,index) in item.scheduleDataAcctInfo"
                :key="dataItem.id"
                @click="showScheduleModal(item, record)"
              >
              {{ index === 0 ? "编辑" : "" }}
              </span>
            </div>
        </template>
        <!-- <template v-else-if="column.dataIndex === 'operate' && !record.scheduleDataInfo">
          <div class="operate-box td-list">
            <span class="count-list" @click="showScheduleModal(null, record)">编辑</span>
          </div>
        </template> -->
      </template>
    </a-table>
  </div>
  <msgSupplementForm v-if="msgData.modalOpen" :msgData="msgData" />
</template>
<script setup>
import { message } from "ant-design-vue";
import * as workUrl from "@/api/conOprWork";
import emitter from "@/assets/js/emitter";
import msgSupplementForm from "./msgSupplementForm.vue";
import dayjs from "dayjs";
const props = defineProps({
  scheduleData: Object,
});
const msgTableRef = ref();
const pageLoading = ref(false);
const tableColumns = reactive([
  {
    title: "日期",
    dataIndex: "scheduleDateName",
    width: 80,
    fixed: "left"
  },
  {
    title: "发布平台",
    dataIndex: "platform",
    width: 180
  },
  {
    title: "标题",
    dataIndex: "title",
    ellipsis: true,
    width: 180
  },
  {
    title: "链接",
    dataIndex: "url",
    ellipsis: true,
    width: 200
  },
  {
    title: "阅读量",
    dataIndex: "readCount",
    width: 80
  },
  {
    title: "分享数",
    dataIndex: "shareCount",
    width: 80
  },
  {
    title: "点赞",
    dataIndex: "likeCount",
    width: 80
  },
  {
    title: "在看",
    dataIndex: "watchCount",
    width: 80
  },
  {
    title: "评论",
    dataIndex: "commentCount",
    width: 80
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    width: 80,
    fixed: "right"
  },
]);
let dataSource = ref([]);
//搜索数据
const searchForm = reactive({
  searchDate: "",
});
// 获取列表数据
const getScheduleList = async (data, isSearch = false) => {
  pageLoading.value = true;
  const res = await workUrl.scheduleDataList(data);
  pageLoading.value = false;
  if (res.retCode === "000000") {
    if (!isSearch) {
      dataSource.value.push(...res.data);
    } else {
      dataSource.value = res.data;
    }
    nextTick(() => {
      if (msgTableRef.value) {
        const tableContainer =
          msgTableRef.value.$el.querySelector(".ant-table-body");
        tableContainer.addEventListener("scroll", handleScroll);
      }
    });
  } else {
    message.error(res.retMsg);
  }
};
//初始化页面底层数据(日期)
let params = {}; //列表查询参数
const getScheduleInit = async () => {
  pageLoading.value = true;
  const res = await workUrl.scheduleInit();
  pageLoading.value = false;
  if (res.retCode === "000000") {
    const monthStr =
      res.data.month < 10 ? "0" + res.data.month : res.data.month.toString();
    searchForm.searchDate = dayjs(res.data.year + "-" + monthStr, "YYYY-MM");
    params = {
      year: res.data.year,
      month: res.data.month,
    };
    localStorage.setItem("searchDate", JSON.stringify(searchForm.searchDate))
    getScheduleList(params);
  } else {
    message.error(res.retMsg);
  }
};
// 搜索
let loadM = 1;
function searchFun(isSearch) {
  loadM = 1;
  if (localStorage.getItem("searchDate") && !isSearch) {
    let storageDate = JSON.parse(localStorage.getItem("searchDate"));
    searchForm.searchDate = dayjs(storageDate);
    params = {
      year: parseInt(dayjs(storageDate).format("YYYY-MM").split("-")[0]),
      month: parseInt(dayjs(storageDate).format("YYYY-MM").split("-")[1])
    }
  } else{
    params = {
      year: parseInt(searchForm.searchDate.format("YYYY-MM").split("-")[0]),
      month: parseInt(searchForm.searchDate.format("YYYY-MM").split("-")[1])
    }
    localStorage.setItem("searchDate", JSON.stringify(searchForm.searchDate));
  }
  nextTick(()=>{
    if (msgTableRef.value) {
      const tableContainer = msgTableRef.value.$el.querySelector('.ant-table-body');
      tableContainer.scrollTop = 0;
      getScheduleList(params,true);
    }
  })
}

// 定义滚动函数
const handleScroll = () => {
  const tableContainer = msgTableRef.value.$el.querySelector(".ant-table-body");
  const scrollPosition = tableContainer.scrollTop;
  const isTop = scrollPosition === 0;
  const isBottom =
    tableContainer.scrollHeight - scrollPosition ===
    tableContainer.clientHeight;
  // if (isTop) {
  // 	console.log('重新加载');
  // }
  if (isBottom) {
    //是否超过三年
    loadM++;
    if (loadM > 36) {
      message.error("已达加载数据上限，请重选日期！");
      return false;
    }
    pageLoading.value = true;
    //超过一年自然月赋值为1月；否则加1
    if (params.month === 12) {
      params.month = 1;
      params.year += 1;
      message.warning(`已跨年！当前为${params.year}年`);
    } else {
      params.month++;
    }
    getScheduleList(params);
  }
};
//排期/编辑
const scheduleData = reactive({
  modalOpen: false,
  isSchedule: true, //是否为排期
  item: {},
});
const msgData = reactive({
  modalOpen: false,
  item: {},
  scheduleData,
});
const platforms = [
  {name:"公众号",id:1},
  {name:"小红书",id:2},
  {name:"微博",id:3},
  {name:"视频号",id:4},
  {name:"抖音",id:5},
  {name:"头条",id:6},
  {name:"快手",id:7}
]
const showScheduleModal = (item, record) => {
  item.scheduleDate = record.scheduleDate;
  item.scheduleWeek = record.scheduleWeek;
  item.scheduleDataAcctInfo.forEach((el,i) => {
    platforms.forEach((val,j) => {
      if(el.platform === val.id) {
        el.platformName = val.name;
      }
    });
  });
  msgData.modalOpen = true;
  Object.assign(msgData.item, item);
  scheduleData.isSchedule = false;
  scheduleData.modalOpen = true;
  Object.assign(scheduleData.item, item);
};
//新增/编辑/排期之后更新数据
emitter.on("msgWork", (value) => {
  searchFun();
});

onMounted(() => {
  if(localStorage.getItem('searchDate')) {
    searchFun()
  } else {
    getScheduleInit();
  }
});
// 移除scroll监听
onUnmounted(() => {
  nextTick(() => {
    if (msgTableRef.value) {
      const tableContainer =
        msgTableRef.value.$el.querySelector(".ant-table-body");
      tableContainer.removeEventListener("scroll", handleScroll);
    }
  });
});
</script>
<style lang="less" scoped>
.top-operate {
  display: flex;
  margin-bottom: 0;
  position: absolute;
  left: 0;
  top: -38px;
  .search-box,
  .top-box {
    display: flex;
  }
}
.work-list {
  background: #fff;
  height: calc(100vh - 170px);
  border: 1px solid #e2e8f0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .td-list {
    padding: 0 8px 10px 8px;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .td-list:not(:last-child) {
    border-bottom: 1px solid #fe8308;
  }
  :deep(.ant-table-tbody .ant-table-cell:not(:first-child)) {
    padding: 0;
  }
  .count-list {
    line-height: 30px;
    width: 100%;
    min-height: 30px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
  }
  .audit-img-box {
    position: relative;
    top: -15px;
    display: flex;
    align-items: center;
  }
  .schedule-date-name {
    span {
      color: #1e293b;
      opacity: 0.5;
      font-size: 12px;
    }
    .node-name {
      color: #fe8308;
      opacity: 1;
    }
    .schedule-year {
      position: absolute;
      top: 0px;
      left: 2px;
      color: #fe8308;
      opacity: 0.5;
      font-size: 10px;
    }
  }
  .audit-name {
    color: #fe8308;
    cursor: pointer;
  }
  .operate-box {
    color: #fe8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
  .ant-steps {
    position: relative;
    left: -50px;
  }
  .ant-steps-icon-dot {
    width: 20px !important;
    height: 20px !important;
  }
  .ant-steps
    .ant-steps-item-process
    .ant-steps-item-icon
    > .ant-steps-icon
    .ant-steps-icon-dot,
  .ant-steps
    .ant-steps-item-finish
    .ant-steps-item-icon
    > .ant-steps-icon
    .ant-steps-icon-dot {
    background: transparent;
    background-image: linear-gradient(
      270deg,
      #20d1f6 0%,
      rgba(32, 209, 246, 0.5) 99%
    );
    cursor: pointer;
  }
  :where(.css-dev-only-do-not-override-11grni8).ant-steps
    .ant-steps-item-wait
    .ant-steps-item-icon
    > .ant-steps-icon
    .ant-steps-icon-dot {
    background: #e9e9e9;
  }
  :deep(.ant-steps.ant-steps-label-vertical .ant-steps-item) {
    width: 80px;
    flex: none;
  }
  :deep(.ant-steps.ant-steps-dot .ant-steps-item-tail) {
    top: 9px;
    left: 10px;
    width: 60%;
  }
  :deep(
      .ant-steps
        .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-tail
    )::after {
    background: transparent;
    background-image: linear-gradient(
      270deg,
      #20d1f6 0%,
      rgba(32, 209, 246, 0.5) 99%
    );
  }
  :deep(
      .ant-steps
        .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-tail
    )::after {
    background-color: rgba(0, 0, 0, 0.15);
  }
  :deep(.ant-steps.ant-steps-dot .ant-steps-item-tail)::after {
    width: calc(100% - 25px);
    height: 1px;
    margin-inline-start: 24px;
  }
}
</style>
 