<!-- 首页内容 -->
<template>
  <div class="index-section">
    <div class="index-banner">
      <img src="../../assets/image/banner.jpg" alt="">
    </div>
    <div class="tool-section">
      <div class="news-box">
        <div class="news-title">
          <span class="hot-spot">行业热点</span>
          <span class="refresh-text" @click="refreshNews">
            <sync-outlined/>
            换一批
          </span>
        </div>
        <div class="news-list">
          <p v-for="(item,index) in newsLists" :key="item.name">
            <a :href="item.url">
              <span>{{ index+1 }}</span>
            {{item.name}}
            </a>
          </p>
        </div>
      </div>
      <div class="data-box">
        <div class="data-analyze" @click="linkAccountOverview">
          <p class="data-title">数据分析</p>
          <p class="data-desc">多维度数据解析、运营策略指引</p>
          <div class="data-icon">
            <img src="../../assets/image/data-analyze.png" alt="">
            <span>7日内爆款文章</span>
          </div>
        </div>
        <div class="operating-calendar" @click="linkCOW">
          <p class="data-title">运营日历</p>
          <p class="data-desc">规范化运营、数据资产线上化</p>
          <div class="data-icon">
            <img src="../../assets/image/operating-calendar.png" alt="">
            <div class="keyword-box">
              <p class="keyword-text">运营关键字</p>
              <span class="keyword-name" v-for="item in keywords" :key="item">
                {{ item }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="tool-box">
        <p class="tool-text">AI工具箱</p>
        <div class="tool-icon">
          <div class="icon-box" @click="linkIntelligentCenter(1)">
            <img src="../../assets/image/write-img.png" alt="">
            <span>文章创作</span>
          </div>
          <div class="icon-box" @click="linkIntelligentCenter(2)">
            <img src="../../assets/image/edit-img.png" alt="">
            <span>文章改写</span>
          </div>
          <div class="icon-box" @click="linkIntelligentCenter(3)">
            <img src="../../assets/image/Imitative-img.png" alt="">
            <span>文章仿写</span>
          </div>
          <div class="icon-box" @click="linkIntelligentCenter(4)">
            <img src="../../assets/image/style-img.png" alt="">
            <span>文章润色</span>
          </div>
        </div>
      </div>
    </div>
    <div class="index-footer">
      <img src="../../assets/image/aspirecn-img.png" alt="">
      <span>Copyright©-2024 卓望公司版权所有</span>
      <span>|</span>
      <span>卓望信息/内容与审查服务部</span>
    </div>
  </div>
</template>
<script setup>
import { homeKeyword } from "@/api/indexContent";
const newsLists1 = ref([
  {name:'中国移动｜谁懂啊，离岸20km也有信号！',url:'https://mp.weixin.qq.com/s/UcGpiVI3DIAgacVPo0rILg'},
  {name:'南京移动｜放假‼就在本周',url:'https://mp.weixin.qq.com/s/kPWLufv2wjYsA4d6zdSUdQ'},
  {name:'广州移动｜广州住房公积金最新调整来了',url:'https://mp.weixin.qq.com/s/OADuvaB4DoAlvEGt3QgBCg'},
  {name:'中国移动｜央视报道！火到了巴塞罗那！',url:'https://mp.weixin.qq.com/s/QecGJedEbonNXFxQrGyvfw'},
  {name:'中国移动｜GTI国际产业大会丨中国移动总经理何飚：激活5G-A×AI乘数效应 共创智能时代新价值',url:'https://mp.weixin.qq.com/s/rxxyUNBk6_3epekP9dw8vA'},
  {name:'中国移动｜MWC25丨中国移动总经理何飚：拥抱“AI+”新时代 共谱数智新篇章',url:'https://mp.weixin.qq.com/s/5lKn6SnDtvtR7b_BWZ1_BA'},
  {name:'中国移动｜倒计时1天！巴塞罗那亮点抢先看！',url:'https://mp.weixin.qq.com/s/kaZWUXEknM4veHpfrNvikg'},
  {name:'中国移动和粉俱乐部｜领1GB‼️春龙抬首，鸿运当头~',url:'https://mp.weixin.qq.com/s/m7yX9KY06A0m1uMZWTGyqQ'},
  {name:'中国移动高频骚扰电话防护｜如何一键屏蔽不想接听的境外来电',url:'https://mp.weixin.qq.com/s/vbgsxi5cGChtxQPGmCGkmw'},
  {name:'中国移动云盘｜来云盘用DeepSeek R1满血版，助你生活职场开挂！',url:'https://mp.weixin.qq.com/s/yU99dp-QlugjPqac2rOsVA'}
])
const newsLists2 = ref([
  {name:'电网头条｜全国人大代表、国网浙江电力董事长陈安伟：以“AI+电力”抢得发展先机',url:'https://mp.weixin.qq.com/s/ThVWo4QzIvjn0-SXTvHvMQ'},
  {name:'中国联通｜锁定，巴塞罗那！',url:'https://mp.weixin.qq.com/s/lfu5ofci2-VbgfX_-yDrIQ'},
  {name:'中国电信｜国家又开始退钱啦！',url:'https://mp.weixin.qq.com/s/J-Ng1rHEOaCem8cQX_nEeg'},
  {name:'人民邮电报｜再爆AI圈！中国团队发布通用型AI Agent产品Manus',url:'https://mp.weixin.qq.com/s/gR2ce6nQ2rbeIGq8T8YKxA'},
  {name:'电网头条｜经济大省挑大梁丨绿色嬗变 破茧蝶变 共融共变',url:'https://mp.weixin.qq.com/s/aJJzrhmmoepy1ysOdEAZ7w'},
  {name:'中国联通｜这项计划，刚刚启动！',url:'https://mp.weixin.qq.com/s/iJNj1s0B9Wmct9MWVYfvpQ'},
  {name:'中国电信｜巴塞罗那，万众瞩目！',url:'https://mp.weixin.qq.com/s/ZRGEusMY_NrLfey-YmOOBw'},
  {name:'中国联通客服｜弘扬雷锋精神，筑牢反诈防线',url:'https://mp.weixin.qq.com/s/0atdNe0xxPJysq1GCesaZg'},
  {name:'天翼防骚扰｜营销电话不停变化？一招教您免受侵扰！',url:'https://mp.weixin.qq.com/s/WNIpgm1g_wJ2BXuE1kbhQg'},
  {name:'天翼终端｜臻情AI智能门锁，家的“超能力”守护者',url:'https://mp.weixin.qq.com/s/B6S0VIfPTBiT7rCqH6BaXQ'}
])
const newsLists = ref(newsLists1.value);
let isRefresh = false;
const refreshNews = () =>{
  isRefresh = !isRefresh;
  if(isRefresh) {
    newsLists.value = newsLists2.value
  }else {
    newsLists.value = newsLists1.value
  }
}
const router = useRouter();
const linkIntelligentCenter = (tab) =>{
  router.push(`/intelligentCenter/intelligentWrite?act=${tab}`);
}
const linkCOW = () =>{
  router.push("/workbench/contentOperateWorkbench");
}
const linkAccountOverview = () =>{
  router.push("/dataAnalysis/accountOverview");
}
//获取日历关键字
const keywords = ref([])
const getHomeKeyword = async () => {
  const res = await homeKeyword();
  if (res.retCode === "000000") {
    keywords.value = res.data
  }
}
onMounted(()=>{
  getHomeKeyword()
})
</script>

<style lang="less" scoped>
.index-section {
  height: calc(100vh - 68px);
  padding: 0 135px;
  overflow-y: auto;
  .index-banner {
    padding-top: 20px;
    margin-bottom: 20px;
    img {
      width: 100%;
      vertical-align: top;
      border-radius: 18px;
    }
  }
  .tool-section {
    height: 400px;
    display: flex;
    .news-box,.data-box,.tool-box {
      width: 350px;
      border-radius: 18px;
      overflow: hidden;
    }
    .news-box {
      background-image: linear-gradient(45deg, #FFF9F5 0%, #FFF0EA 100%);
      box-shadow: 0 0 4px 0 rgba(254,131,8,0.20);
      padding: 15px 20px;
      flex: 1;
      margin-right: 20px;
    }
    .data-box {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      margin-right: 20px;
    }
    .tool-box {
      background-image: linear-gradient(225deg, #FFF6F0 0%, #FAF1FF 54%, #FFF2EE 100%);
      box-shadow: 0 0 4px 0 rgba(254,131,8,0.20);
      padding: 15px;
    }
    .tool-text {
      font-size: 16px;
      color: #1E293B;
      margin-bottom: 40px;
    }
    .tool-icon {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .icon-box {
        width: 150px;
        height: 140px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 14px;
        color: #011738;
        margin-bottom: 20px;
        background: #FFFFFF;
        border-radius: 4px;
      }
      img {
        width: 50px;
        vertical-align: top;
        margin-bottom: 10px;
      }
    }
    .data-title {
      font-size: 16px;
      color: #1E293B;
      margin-bottom: 15px;
    }
    .data-desc {
      font-size: 14px;
      color: rgba(30, 41, 59, .6);
      margin-bottom: 20px;
    }
    .data-icon {
      min-height: 90px;
      display: flex;
      align-items: center;
      font-size: 20px;
      color: #011738;
      font-weight: bold;
      background-color: #fff;
      padding: 10px 15px;
      border-radius: 8px;
      cursor: pointer;
      img {
        width: 48px;
        margin-right: 30px;
        vertical-align: top;
      }
      .keyword-text {
        margin-bottom: 5px;
      }
      .keyword-name {
        font-size: 12px;
        color: rgba(30, 41, 59, .6);
        margin-right: 5px;
        font-weight: normal;
      }
      .keyword-box {

      }
    }
    .data-analyze,.operating-calendar {
      background-image: linear-gradient(225deg, #FAF2FF 0%, #FFECEC 44%, #FFF4E9 100%);
      box-shadow: 0 0 4px 0 rgba(254,131,8,0.20);
      border-radius: 2px;
      padding:15px;
      border-radius: 18px;
    }
    .news-title {
      display: flex;
      justify-content: space-between;
    }
    .hot-spot {
      font-size: 16px;
      color: #1E293B;
    }
    .refresh-text {
      font-size: 14px;
      color: rgba(30, 41, 59, .5);
      cursor: pointer;
    }
  }
  .news-list {
    color: #000;
    font-size: 14px;
    padding-top: 20px;
    p {
      margin-bottom: 15px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    a {
      color: #000;
      font-size: 14px;
    }
    span {
      margin-right: 3px;
      font-size: 16px;
      color: rgba(30, 41, 59, 0.6);
    }
    p:nth-child(1)>a>span {
      color: #EA514F;
    }
    p:nth-child(2)>a>span {
      color: #EA864F;
    }
    p:nth-child(3)>a>span {
      color: #EAA54F;
    }
  }
  .index-footer {
    font-size: 12px;
    color: rgba(1, 23, 56, .5);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 70px;
      vertical-align: top;
    }
    span {
      margin-left: 10px;
    }
  }
}
</style>
