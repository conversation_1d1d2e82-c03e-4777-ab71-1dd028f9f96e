//验证手机号码
export const validateMobile = async (_rule, value) => {
    if (/0?(13|14|15|16|17|18|19)[0-9]{9}/.test(value) || (value.indexOf('****') === 3 && value.length === 11)) {
        return Promise.resolve();
    } else {
        return Promise.reject('请输入正确的手机号码');
    }
}; 
//正整数
export const validateNumber = async (_rule, value) => {
    console.log(value)
    if (value && !/^[1-9]\d*$/.test(value)) {
        return Promise.reject('请输入正整数');
    } else if (parseInt(value) > 2147483647) {
        return Promise.reject('数据应小于或等于2147483647');
    } else {
        return Promise.resolve();
    }
};
//url
const URLTest = /^((https|http|ftp|rtsp|mms)?:\/\/)+[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/
export const validateUrl = async (_rule, value) => {
    if (!URLTest.test(value)) {
      return Promise.reject('请输入正确的url')
    } else {
        return Promise.resolve();
    }
};