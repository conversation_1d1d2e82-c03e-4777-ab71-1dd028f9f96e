import axios from 'axios'
axios.defaults.timeout = 60000;//响应时间
import { message } from 'ant-design-vue'
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';//配置请求头
axios.defaults.baseURL = '';   //配置接口地址
import router from "@/router/index.js";
//POST传参序列化(添加请求拦截器)
axios.interceptors.request.use((config) => {
    return config;
}, (error) => {
    return Promise.reject(error);
});
//返回状态判断(添加响应拦截器)
axios.interceptors.response.use((res) => {
    //对响应数据做些事
    if (res.data.retCode === "100050") {
        message.error('未登录或登录超时，请重新登录！')
        setTimeout(() => {
            router.push('/login')
        }, 2000)
        return false
    }
    return res;
}, (error) => {
    console.log(error)
    if (error.response.status === 408) {
        message.error('请求超时，请检查系统时间')
    } else if (error.response.status === 403) {
        message.error('请勿重复操作');
    } else {
        message.error('未知错误请联系管理员');
        // router.push('/login')
    }
});

//返回一个Promise(发送post请求)
export function fetchPost(url, params, config) {
    let isParms = url.indexOf('?') > -1?'&':'?';
    const urlTime = url + isParms +'replayTime=' + new Date().getTime()
    return new Promise((resolve, reject) => {
        axios.post(urlTime, params, config)
            .then(response => {
                resolve(response.data);
            }, err => {
                reject(err);
            }).catch((error) => {
                reject(error)
            })
    })
}
//返回一个Promise(发送get请求)
export function fetchGet(url, params) {
    const paramTime = {
        ...params,
        replayTime: new Date().getTime()
    }
    return new Promise((resolve, reject) => {
        axios.get(url, { params: paramTime })
            .then(response => {
                resolve(response.data)
            }, err => {
                reject(err)
            })
            .catch((error) => {
                reject(error)
            })
    })
}
export default {
    fetchPost,
    fetchGet,
    axios,
}