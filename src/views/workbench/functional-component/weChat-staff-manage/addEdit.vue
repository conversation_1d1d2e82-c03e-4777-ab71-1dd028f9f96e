<!-- 员工新增/编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    :width="720"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="confirmLoading">
      <a-form>
        <a-form-item label="姓名" v-bind="validateInfos.name">
          <a-input v-model:value="addEditFrom.name" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item label="手机号" v-bind="validateInfos.mobile">
          <a-input 
          v-model:value="addEditFrom.mobile" 
          @blur="validate('mobile', { trigger: 'blur' }).catch(() => {})"
          :maxlength="11"
          placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item label="工号" v-bind="validateInfos.bossCode">
          <a-input 
          v-model:value="addEditFrom.bossCode" 
          placeholder="请输入工号" />
        </a-form-item>
        <a-form-item label="区县" v-bind="validateInfos.county">
          <a-input 
          v-model:value="addEditFrom.county" 
          placeholder="请输入区县" />
        </a-form-item>
        <a-form-item label="区县代码" v-bind="validateInfos.districtCode">
          <a-input 
          v-model:value="addEditFrom.districtCode" 
          placeholder="请输入区县代码" />
        </a-form-item>
        <a-form-item label="渠道" v-bind="validateInfos.channel">
          <a-input 
          v-model:value="addEditFrom.channel" 
          placeholder="请输入渠道" />
        </a-form-item>
        <a-form-item label="子渠道" v-bind="validateInfos.channelName">
          <a-input 
          v-model:value="addEditFrom.channelName" 
          placeholder="请输入子渠道" />
        </a-form-item>
        <a-form-item label="渠道编码" v-bind="validateInfos.channelCode">
          <a-input 
          v-model:value="addEditFrom.channelCode" 
          placeholder="请输入渠道编码" />
        </a-form-item>
        <a-form-item label="渠道类别" v-bind="validateInfos.channelType">
          <a-select
            v-model:value="addEditFrom.channelType"
            :options="channelTypeOpt"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择渠道类别"
          >
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { staffEdit, staffAdd} from "@/api/weChatStaffManage";
import { encryptionMobile } from "@/assets/js/utils";
import emitter from "@/assets/js/emitter";
import {validateMobile} from "@/assets/js/formValidator";
const props = defineProps({
  addEditData: Object,
});
const channelTypeOpt = [
  {
    name: '代办',
    id: '1'
  },
  {
    name: '自办',
    id: '2'
  }
]
const modalLoading = ref(false);
const confirmLoading = ref(false);
const addEditFrom = props.addEditData.list;
//表单规则验证数据
const addEditRules = reactive({
  name: [
    {
      required: true,
      message: "请输入姓名"
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  bossCode: [
    {
      min: 1,
      max: 60,
      message: "长度在1 到 60个字符",
      trigger: "change"
    }
  ],
  county:[
  { 
      min: 1,
      max: 60,
      message: "长度在1 到 60个字符",
      trigger: "change"
    }
  ],
  channelName:[
  { 
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符",
      trigger: "change"
    }
  ],
  districtCode:[
  { 
      min: 1,
      max: 60,
      message: "长度在1 到 60个字符",
      trigger: "change"
    }
  ],
  channel:[
  { 
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符",
      trigger: "change"
    }
  ],
  channelCode:[
  { 
      min: 1,
      max: 100,
      message: "长度在1 到 100个字符",
      trigger: "change"
    }
  ],
  mobile: [
    {
      required: true,
      validator: validateMobile,
      trigger: "blur"
    }
  ]
});
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      confirmLoading.value = modalLoading.value = true;
      let res = {};
      const fromData = { ...toRaw(addEditFrom)}; 
      
      fromData.name = encodeURI(encryptionMobile(fromData.name));
      fromData.mobile = encodeURI(encryptionMobile(fromData.mobile));
      console.log(fromData,props.addEditData.weChatId)
      if (!addEditFrom.id) {
        res = await staffAdd(fromData,props.addEditData.weChatId);
      } else {
        res = await staffEdit(fromData,props.addEditData.weChatId);
      }
      confirmLoading.value = modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditStaff", true);
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};

</script>
  