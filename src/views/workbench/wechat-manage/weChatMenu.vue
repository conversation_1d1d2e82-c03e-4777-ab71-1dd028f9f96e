<!-- 关键字管理 -->
<template>
  <div class="top-operate">
    <div class="search-box">
      <weChatConfig />
    </div>
    <a-button type="primary" @click="getMenuDown()" class="top-btn">
      <template #icon>
        <ArrowDownOutlined />
      </template>
      下载菜单
    </a-button>
    <a-button type="primary" @click="upSyncMenu()" class="top-btn">
      <template #icon>
        <ArrowUpOutlined />
      </template>
      上传菜单
    </a-button>
    <a-button type="primary" @click="showModal()" class="top-btn">
      <template #icon>
        <PlusOutlined />
      </template>
      新增菜单
    </a-button>
  </div>
  <div class="user-content">
    <a-table
      v-if="dataSource && dataSource.length"
      :dataSource="dataSource"
      :columns="tableColumns"
      size="small"
      :pagination="false"
      :loading="pageLoading"
      childrenColumnName="childMenu"
      :defaultExpandAllRows="true"
      :expandIconColumnIndex="-1"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'msgtypeName'">
          {{ record.msgtypeName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span @click="showModal(record)">编辑</span>
            <span class="del-btn" @click="delFun(record)">删除</span>
          </div>
        </template>
      </template>
    </a-table>
    <a-empty v-else :image="simpleImage" />
  </div>
  <addEdit v-if="addEditData.modalOpen" :addEditData="addEditData" />
</template>
  <script setup>
import {
  menuList,
  menuDel,
  menuDetail,
  menuDown,
  syncMenu,
} from "@/api/weChatMenu";
import weChatConfig from "@/components/weChat-config/weChatConfig.vue";
import addEdit from "./weChat-menu/addEdit.vue";
import { message, Empty } from "ant-design-vue";
import emitter from "@/assets/js/emitter";
import useDel from "@/hooks/useDel";
let dataSource = ref([]);
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const menuOptions = [
  {
    label: "位置1",
    options: [
      {
        value: "1",
        label: "一级菜单",
      },
      {
        value: "11",
        label: "子菜单1*1",
      },
      {
        value: "12",
        label: "子菜单1*2",
      },
      {
        value: "13",
        label: "子菜单1*3",
      },
      {
        value: "14",
        label: "子菜单1*4",
      },
      {
        value: "15",
        label: "子菜单1*5",
      },
    ],
  },
  {
    label: "位置2",
    options: [
      {
        value: "2",
        label: "二级菜单",
      },
      {
        value: "21",
        label: "子菜单2*1",
      },
      {
        value: "22",
        label: "子菜单2*2",
      },
      {
        value: "23",
        label: "子菜单2*3",
      },
      {
        value: "24",
        label: "子菜单2*4",
      },
      {
        value: "25",
        label: "子菜单2*5",
      },
    ],
  },
  {
    label: "位置3",
    options: [
      {
        value: "3",
        label: "三级菜单",
      },
      {
        value: "31",
        label: "子菜单3*1",
      },
      {
        value: "32",
        label: "子菜单3*2",
      },
      {
        value: "33",
        label: "子菜单3*3",
      },
      {
        value: "34",
        label: "子菜单3*4",
      },
      {
        value: "35",
        label: "子菜单3*5",
      },
    ],
  },
];
const tableColumns = reactive([
  {
    title: "菜单名称",
    dataIndex: "menuName",
    width: 200,
  },
  {
    title: "菜单类型",
    dataIndex: "menuTypeName",
    width: 160,
  },
  {
    title: "响应消息类型",
    dataIndex: "msgtypeName",
    width: 160,
  },
  {
    title: "菜单位置",
    dataIndex: "labelName",
    width: 100,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    width: 160,
  },
  {
    title: "操作",
    dataIndex: "operate",
    width: 120,
    fixed: "right",
  },
]);
const pageLoading = ref(false);
const weChatId = ref("");
//递归菜单的菜单位置
function formatConversion(arr) {
  arr.map((x, i) => {
    //匹配菜单位置
    menuOptions.map((m, l) => {
      m.options.map((v, n) => {
        if (v.value == x.orders) {
          v.disabled = true;
          x.labelName = v.label;
        }
      });
      if (x.childMenu) {
        formatConversion(x.childMenu);
      }
    });
  });
}
// 获取列表数据
const getDeptList = async () => {
  pageLoading.value = true;
  const res = await menuList({ chkCurWx: weChatId.value });
  pageLoading.value = false;
  if (res.retCode === "000000") {
    dataSource.value = res.data;
    formatConversion(dataSource.value);
  } else {
    message.error(res.retMsg);
  }
};
// 下载菜单
const getMenuDown = async (list) => {
  useDel(
    menuDown,
    { chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        getDeptList();
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    },
    "此操作将同步公众号菜单至列表, 是否继续?"
  );
};
// 上传菜单
const upSyncMenu = async (list) => {
  useDel(
    syncMenu,
    { chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    },
    "此操作将同步菜单至微信公众号, 是否继续?"
  );
};
onMounted(() => {
  emitter.on("initWeChatId", (value) => {
    weChatId.value = value;
    getDeptList();
  });
});
//新增/编辑数据
const addEditData = reactive({
  menuOptions,
  modalOpen: false,
  modalTitle: "新增微信菜单",
  weChatId: weChatId.value,
  item: {
    menuName: "",
    menuType: "",
    orders: "",
    msgtype: "",
    templateId: "",
    miniprogramPagepath: "",
    miniprogramAppid: "",
    url: "",
    templateName: "",
  },
});
const showModal = async (item = { templateName: "", templateContent: "" }) => {
  if (item?.id) {
    addEditData.modalTitle = "编辑微信菜单";
    pageLoading.value = true;
    const res = await menuDetail({ id: item.id, chkCurWx: weChatId.value });
    pageLoading.value = false;
    if (res.retCode === "000000") {
      Object.assign(addEditData.item, res.data);
    } else {
      message.error(res.retMsg);
    }
  } else {
    addEditData.modalTitle = "新增微信菜单";
    addEditData.item = {
      menuName: "",
      menuType: null,
      orders: null,
      msgtype: "",
      templateId: "",
      miniprogramPagepath: "",
      miniprogramAppid: "",
      url: "",
      templateName: "",
    };
  }
  addEditData.modalOpen = true;
  addEditData.weChatId = weChatId.value;
};

//新增/编辑之后更新数据
emitter.on("addEditMenu", (value) => {
  addEditData.modalOpen = false;
  getDeptList();
});
//组件卸载后销毁emitter
onUnmounted(() => {
  emitter.off("addEditMenu");
  emitter.off("initWeChatId");
});
// 删除数据
const delFun = async (list) => {
  useDel(
    menuDel,
    { id: list.id, chkCurWx: weChatId.value },
    pageLoading,
    (res) => {
      if (res.retCode === "000000") {
        getDeptList();
        message.success("操作成功！");
      } else {
        message.error(res.retMsg);
      }
    }
  );
};
</script>
    
<style lang="less" scoped>
.user-content {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 160px);
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.08);
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
  .ant-empty-normal {
    margin: 0;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.top-operate {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
    