import $https from "@/assets/js/request"

// 列表
export const rspList = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/list',data)
}
// 详情
export const rspDetail = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/detail',data)
}
// 编辑 
export const rspEdit = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/edit?chkCurWx='+chkCurWx,data,{'headers': {'Content-Type':'application/json'}})
}
// 新增
export const rspAdd = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/add?chkCurWx='+chkCurWx,data,{'headers': {'Content-Type':'application/json'}})
}
// 删除
export const rspDel = (data) => {
    return $https.fetchGet('/cop/wx/back/rsp/del',data)
}