<!-- 内容审核 -->
<template>
  <div class="top-operate" v-show="!detailData.modalOpen">
    <div class="search-box">
      <a-select
        v-model:value="searchForm.auditStatus"
        :options="auditStatusOpt"
        :field-names="{ label: 'name', value: 'id' }"
        placeholder="请选择审核状态"
        allow-clear
      >
      </a-select>
      <a-input
        v-model:value="searchForm.title"
        placeholder="请输入文章标题"
      />
      <a-select
        v-model:value="searchForm.articleCategory"
        :options="articleCategoryOpt"
        :field-names="{ label: 'name', value: 'id' }"
        placeholder="请选择文章分类"
        allow-clear
      ></a-select>
      <a-select
        v-model:value="searchForm.accountId"
        :options="accountListOpt"
        :field-names="{ label: 'name', value: 'id' }"
        placeholder="请选择账号名称"
        allow-clear
      ></a-select>
      <a-button type="primary" @click="searchFun()" class="top-btn">
        <template #icon>
          <SearchOutlined />
        </template>
        查询 
      </a-button>
    </div>
    <div class="search-box">
      <a-input
        v-model:value="searchForm.submitterName"
        placeholder="请输入提交人"
      />
      <a-date-picker 
      format="YYYY-MM-DD"
      v-model:value="searchForm.submitDate" 
      placeholder="请选择提交日期"
      />
      <a-date-picker 
        format="YYYY-MM-DD"
        v-model:value="searchForm.auditDate" 
        placeholder="请选择审核日期"
      />
      <a-select
        v-model:value="searchForm.accountPlatform"
        :options="platformOpt"
        :field-names="{ label: 'name', value: 'id' }"
        allow-clear
        placeholder="请选择发布平台"
      ></a-select>
      <a-button type="primary" @click="redoOutlined()" class="top-btn">
        <template #icon>
          <RedoOutlined />
        </template>
        重制
      </a-button>
    </div>
  </div>
  <div class="content-review" :class="{'border-node':!detailData.modalOpen}">
    <div class="edit-btn">
      <a-button type="primary" @click="showModal()" class="top-btn">
        <template #icon>
          <PlusOutlined />
        </template>
        新增审核
      </a-button>
      <div class="btn-r">
        <a-button type="primary"
        :loading="batchDelLoading"
        :disabled="selectedDelItem.length === 0"
        @click="batchDelFn()" 
        class="top-btn">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
        <a-button type="primary"
        :loading="exportLoading"
        :disabled="exportLoading || dataSource.length === 0"
        @click="exportExcle()" 
        class="top-btn">
          <template #icon>
            <UploadOutlined />
          </template>
          导出数据
        </a-button>
      </div>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="tableColumns"
      :row-selection="rowSelection"
      size="small"
      :pagination="pagination"
      @change="(pag)=>searchFun(pag)"
      :loading="pageLoading"
      :scroll="{ x: 'max-content', y: 'max-content', hideOnSinglePage: true }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'articleCategory'">
          {{{1:'图文类', 2: '音视频类'}[record.articleCategory]}}
        </template>
        <template v-if="column.dataIndex === 'platform'">
          <div class="td-list platform-icon">
            <a-tooltip placement="top" v-if="record.weChatAccountName" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{record.weChatAccountName.join(",")}}</span>
              </template>
              <img src="../../assets/image/icon-wechat.png" alt="" />
            </a-tooltip>
            <a-tooltip placement="top" v-if="record.weiboAccountName" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{record.weiboAccountName.join(",")}}</span>
              </template>
              <img src="../../assets/image/icon-weibo.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="record.redNoteAccountName"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{record.redNoteAccountName.join(",")}}</span>
              </template>
              <img src="../../assets/image/icon-xiaohongshu.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="record.touTiAoAccountName"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{
                  record.touTiAoAccountName.join(",")
                }}</span>
              </template>
              <img src="../../assets/image/icon-toutiao.png" alt="" />
            </a-tooltip>
            <a-tooltip placement="top" v-if="record.tikTokAccountName" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{
                  record.tikTokAccountName.join(",")
                }}</span>
              </template>
              <img src="../../assets/image/icon-douyin.png" alt="" />
            </a-tooltip>
            <a-tooltip placement="top" v-if="record.videoAccountName" color="#fff">
              <template #title>
                <span class="ant-work-tooltip">{{
                  record.videoAccountName.join(",")
                }}</span>
              </template>
              <img src="../../assets/image/icon-video.png" alt="" />
            </a-tooltip>
            <a-tooltip
              placement="top"
              v-if="record.kuAiShouAccountName"
              color="#fff"
            >
              <template #title>
                <span class="ant-work-tooltip">{{
                  record.kuAiShouAccountName.join(",")
                }}</span>
              </template>
              <img src="../../assets/image/icon-kuaishou.png" alt="" />
            </a-tooltip>
          </div>
        </template>
        <template v-if="column.dataIndex === 'submitterName'">
          {{ record.submitterName || "-" }}
        </template>
        <template v-if="column.dataIndex === 'auditTime'">
          {{ record.auditTime || "-" }}
        </template>
        <template v-if="column.dataIndex === 'submitTime'">
          {{ record.submitTime || "-" }}
        </template>
        <template v-if="column.dataIndex === 'statusFlag'">
          {{ {0: '草稿', 1: '审核通过', 2: '审核未通过', 3: '待审核'}[record.statusFlag] || '-' }}
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div class="operate-box">
            <span @click="showDetail(record)" v-if="record.statusFlag === 1 || record.statusFlag === 2">查看</span>
            <span @click="showModal(record)" v-if="record.statusFlag === 0 || record.statusFlag === 2">编辑</span>
            <span class="del-btn" @click="delFun(record)" v-if="record.statusFlag === 0">删除</span>
            <span v-if="record.statusFlag === 3">-</span>
          </div>
        </template>
      </template>
    </a-table>
    <auditDetailPage :detailData="detailData" v-if="detailData.modalOpen"/>
  </div>
  <addEdit :addEditData="addEditData" v-if="addEditData.modalOpen"/>
</template>

<script setup>
import { auditPage, auditDel, auditDetail, auditExport, accountListCity, batchDel } from '@/api/contentReview';
import useGetList from '@/hooks/useGetList';
import addEdit from './content-review/addEdit.vue';
import auditDetailPage from './content-review/auditDetail.vue';
import { message } from 'ant-design-vue';
import emitter from '@/assets/js/emitter';
import useDel from '@/hooks/useDel';
import useExportData from "@/hooks/useExportData";
import { encryptionMobile } from "@/assets/js/utils";
let dataSource = ref([]);
const tableColumns = [
  {
    title: '单号',
    dataIndex: 'tid',
    width:120
  },
  {
    title: '文章标题',
    dataIndex: 'title',
    ellipsis: true,
    width:120
  },
  {
    title: '文章分类',
    dataIndex: 'articleCategory',
    width:100
  },
  {
    title: '审核状态',
    dataIndex: 'statusFlag',
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    width:160
  },
  {
    title: '提交人',
    dataIndex: 'submitterName'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    width:160
  },
  {
    title: '发布平台',
    dataIndex: "platform",
    width: 240
  },
  {
    title: '操作',
    dataIndex: 'operate',
    width: 140,
    fixed: "right"
  },
];
const pageLoading = ref(false);
//表单数据
let searchForm = reactive({
  title: '',
  auditStatus: null,
  articleCategory: null,
  submitterName: '',
  submitDate: null,
  auditDate: null,
  accountId: null,
  accountPlatform: null
});
//平台
const platformOpt = ref([
  {name: "公众号",id: '1'},
  {name: "小红书",id: '2'},
  {name: "微博",id: '3'},
  {name: "视频号",id: '4'},
  {name: "抖音",id: '5'},
  {name: "头条",id: '6'},
  {name: "快手",id: '7'}
])
//审核状态
const auditStatusOpt = ref([
  {name: "草稿",id: '0'},
  {name: "审核通过",id: '1'},
  {name: "审核未通过",id: '2'},
  {name: "待审核",id: '3'}
])
//文章分类
const articleCategoryOpt = ref([
  {name: "图文类",id: '1'},
  {name: "音视频类",id: '2'}
])
// 获取列表数据
const pagination = reactive({
  total: 0,
  current: 0,
  pageSize: 0,
})
//批量删除
const selectedDelItem = ref([]);
const batchDelLoading = ref(false)
const rowSelection = {
  onChange: (selectedRowKeys, selectedRows) => {
    selectedDelItem.value = selectedRows
  },
  getCheckboxProps: record => ({
    disabled: record.statusFlag !== 0,
  })
};
const batchDelFn = async () => {
  let ids = selectedDelItem.value.map((el)=> el.id)
  useDel(batchDel,{id:ids.join(',')}, pageLoading, (res)=>{
    if (res.retCode === '000000') {
      searchFun()
      message.success('操作成功！')
      selectedDelItem.value = [];
      console.log(selectedDelItem.value)
    } else {
      message.error(res.retMsg);
    }
  },'您确定要批量删除数据吗？');
}
//重置
function redoOutlined() {
  searchForm.title = '';
  searchForm.auditStatus = null;
  searchForm.articleCategory = null;
  searchForm.submitterName = '';
  searchForm.submitDate = null;
  searchForm.auditDate = null;
  searchForm.accountId = null;
  searchForm.accountPlatform = null;
  searchFun()
}
//查询列表
const searchFun = (pag) => {
  const searchDate = {...toRaw(searchForm)};
  searchDate.submitDate = searchDate.submitDate?searchDate.submitDate.format('YYYY-MM-DD'):null;
  searchDate.auditDate = searchDate.auditDate?searchDate.auditDate.format('YYYY-MM-DD'):null;
  searchDate.submitterName = searchDate.submitterName?encryptionMobile(searchDate.submitterName):'';
  useGetList(auditPage, pageLoading, dataSource, pagination,{ ...searchDate,pageNum: pag?pag.current:1, pageSize:10});
};
//新增编辑
const addEditData = reactive({
  modalOpen: false,
  modalTitle: '新增审核',
  list: {
    accountsId:[],
  }
});
const showModal = async (item) => {
  if (item?.id) {
    pageLoading.value = true;
    addEditData.modalTitle = '编辑审核';
    const res = await auditDetail({id:item.id});
    pageLoading.value = false;
    if (res.retCode === "000000") {
      Object.assign(addEditData.list, res.data);
      //初始化发布平台对象
      for(let i = 0;i < 7;i++){
        addEditData.list.accountsId.push({accounts:[]});
      }
      //平台数据
      addEditData.list.accountsId[0].accounts = res.data.weChatAccountId || [];
      addEditData.list.accountsId[1].accounts = res.data.redNoteAccountId || [];
      addEditData.list.accountsId[2].accounts = res.data.weiboAccountId || [];
      addEditData.list.accountsId[3].accounts = res.data.videoAccountId || [];
      addEditData.list.accountsId[4].accounts = res.data.tikTokAccountId || [];
      addEditData.list.accountsId[5].accounts = res.data.touTiAoAccountId || [];
      addEditData.list.accountsId[6].accounts = res.data.kuAiShouAccountId || [];
      addEditData.list.iamgeList = res.data.iamgeList || [];
      addEditData.list.avList = res.data.avList || [];
      addEditData.list.fileList = res.data.fileList || [];
    }else {
      message.error(res.retMsg);
    }
  } else {
    addEditData.modalTitle = '新增审核';
    addEditData.list = {
      accountId: "",
      title: null,
      author: null,
      uploadMethod: 1,
      articleCategory: 1,
      content: '',
      videoIntro: '',
      cloudAddress: '',
      accountsId:[],
      iamgeList: [],
      avList: [],
      fileList: []
    }
    //初始化发布平台对象
    for(let i = 0;i < 7;i++){
      addEditData.list.accountsId.push({accounts:[]});
    }
  }
  addEditData.modalOpen = true;
};
//查看详情
const detailData = ref({
  modalOpen: false,
  id: '',
  item: {}
});
const showDetail = (item) =>{
  detailData.value.modalOpen = true;
  detailData.value.id = item.id;
  detailData.value.item = item;
}
//新增/编辑之后更新数据
emitter.on('addEditAudit',(value)=>{
  addEditData.modalOpen = false;
  useGetList(auditPage, pageLoading, dataSource, pagination);
}) 
emitter.on('anewEditItem',(value)=>{
  detailData.value.modalOpen = false;
  showModal(value)
})
const accountListOpt = ref([])
const getAccountList = async (item) => {
  const res = await accountListCity();
  if (res.retCode === "000000") {
    accountListOpt.value = res.data;
  }else {
    message.error(res.retMsg);
  }
}
onMounted(() => {
  getAccountList()
  useGetList(auditPage, pageLoading, dataSource, pagination);
});
//组件卸载后销毁emitter
onUnmounted(()=>{
  emitter.off('addEditAudit')
  emitter.off('anewEditItem')
})
// 数据导出
const exportLoading = ref(false);
const exportExcle = () => {
  const searchDate = {...toRaw(searchForm)};
  searchDate.submitDate = searchDate.submitDate?searchDate.submitDate.format('YYYY-MM-DD'):null;
  searchDate.auditDate = searchDate.auditDate?searchDate.auditDate.format('YYYY-MM-DD'):null;
  searchDate.submitterName = searchDate.submitterName?encryptionMobile(searchDate.submitterName):'';
  useExportData(auditExport,pageLoading,exportLoading,searchDate);
};
// 删除数据
const delFun = async (list) => {
  useDel(auditDel,{id:list.id}, pageLoading, (res)=>{
    if (res.retCode === '000000') {
      useGetList(auditPage, pageLoading, dataSource, pagination);
      message.success('操作成功！')
    } else {
      message.error(res.retMsg);
    }
  });
};
</script>
<style lang="scss" scoped>
.border-node {
  border: 1px solid #E2E8F0;
  box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
}
.content-review {
  background: #fff;
  padding: 10px;
  height: calc(100vh - 210px);
  position: relative;
  box-sizing: border-box;
  .ant-table-wrapper {
    height: calc(100% - 40px);
  }
  .edit-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .btn-r {
    display: flex;
    .ant-btn-primary {
      margin-left: 10px;
    }
  }
  .operate-box {
    color: #FE8308;
    span {
      margin-right: 10px;
      cursor: pointer;
    }
    .del-btn {
      color: #fd7271;
    }
  }
  .td-list:not(:last-child) {
    margin-bottom: 10px;
  }
  .platform-icon {
    height: 32px;
    img {
      width: 20px;
      margin-right: 10px;
      cursor: pointer;
    }
    .ant-work-tooltip {
      color: #333;
    }
  }
}
.top-operate {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 10px;
  .search-box {
    margin-bottom: 10px;
    display: flex;
  }
}
</style>
<style lang="less">
.ant-tooltip .ant-tooltip-inner {
  .ant-work-tooltip {
    color: #222;
  }
}
</style>