import { message } from "ant-design-vue";
// 获取页面表格数据
export default async function useGetList(url,loading,resData,pagination,params={pageNum: 1,pageSize:10},callback) {
    loading.value = true
    const res = await url({
        ...params
    });
    loading.value = false
    if (res.retCode === "000000") {
        resData.value = [];
        res.data.list.forEach((el,i) => {
            el.key = el.id || i;
            resData.value.push(el)
        });
        pagination.total = res.data.total;
        pagination.current = res.data.pageNum;
        pagination.pageSize = res.data.pageSize;
        if(typeof(callback) === 'function'){
            callback()
        }
    } else {
        message.error(res.retMsg);
    }
}