<!-- 审核图片上传 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.uploadImgData.modalOpen"
    :title="props.uploadImgData.title"
    okText="确定"
    @ok="addGivingHandle"
    :confirm-loading="modalLoading"
    width="320px" 
  >
    <a-spin :spinning="confirmLoading">
      <div class="upload-box">
        <a-upload
          v-model:file-list="fileList"
          list-type="picture-card"
          @preview="handlePreview"
          @change="successUpload"
          :before-upload="beforeUpload"
        >
          <div v-if="fileList.length < 1">
            <plus-outlined />
            <div style="margin-top: 8px">上传图片</div>
          </div>
        </a-upload>
        <p class="upload-desc">请上传jpg/png，文件大小不能超过5MB；每个留痕最多可上传三次。</p>
      </div>
    </a-spin>
  </a-modal>
  <a-modal
    :open="previewVisible"
    :title="previewTitle"
    :footer="null"
    @cancel="handleCancel"
  >
    <img alt="example" style="width: 100%" :src="previewImage" />
  </a-modal>
</template>

<script setup>
import { message } from "ant-design-vue";
import https from "@/assets/js/request";
import emitter from "@/assets/js/emitter";
const props = defineProps({
  uploadImgData: Object,
});
const confirmLoading = ref(false);
const modalLoading = ref(false);

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
const previewVisible = ref(false);
const previewImage = ref("");
const previewTitle = ref("");
const fileList = ref([])
watch(props,(newValue,oldValue)=>{
  if(newValue.uploadImgData.title === '初审' && newValue.uploadImgData.item.firstAuditUrl) {
    fileList.value.push({
      uid: '-1',
      name: newValue.uploadImgData.item.firstAuditName,
      status: 'done',
      url: newValue.uploadImgData.item.firstAuditUrl
    })
  } else if(newValue.uploadImgData.title === '二审' && newValue.uploadImgData.item.secondAuditUrl) {
    fileList.value.push({
      uid: '-1',
      name: newValue.uploadImgData.item.secondAuditName,
      status: 'done',
      url: newValue.uploadImgData.item.secondAuditUrl
    })
  } else if(newValue.uploadImgData.title === '终审' && newValue.uploadImgData.item.finalAuditUrl) {
    fileList.value.push({
      uid: '-1',
      name: newValue.uploadImgData.item.finalAuditName,
      status: 'done',
      url: newValue.uploadImgData.item.finalAuditUrl
    })
  }
},{immediate:true})
const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = "";
};
//预览
const handlePreview = async (file) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj);
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value =
    file.name || file.url.substring(file.url.lastIndexOf("/") + 1);
};
// 上传前判断格式
const beforeUpload = (file) => {
  confirmLoading.value = true;
  const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png";
  if (!isJpgOrPng) {
    message.error("上传格式有误！");
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 5;
  if (!isLt2M) {
    message.error("图片大小不能超过5M！");
    return false;
  }
  fileList.value = [...(fileList.value || []), file];
  return false;
};
// 本地上传成功
let uploadImg = {};
const successUpload = (file) => {
  uploadImg = file.file;
  confirmLoading.value = false;
};
// 上传图片
const addGivingHandle = () => {
  if(JSON.stringify(uploadImg) === '{}') {
    message.error("请上传图片文件");
    return false
  }
  const formData = new FormData();
  formData.append("scheduleId", props.uploadImgData.item.id);
  formData.append("file", uploadImg);
  const config = {
    headers: { "Content-Type": "multipart/form-data; charset=utf-8" },
  };
  const instance = https.axios.create({ withCredentials: true });
  modalLoading.value = true;
  confirmLoading.value = true;
  instance
    .post(`${props.uploadImgData.item.uploadUrl}?replayTime=` + new Date().getTime(), formData, config)
    .then((xhr) => {
      modalLoading.value = false;
      confirmLoading.value = false;
      if (xhr.data.retCode === "000000") {
        message.success("操作成功");
        emitter.emit("refreshWork", true);
      } else if (xhr.data.retCode === "000520") {
        message.error("请上传图片文件");
      } else if (xhr.data.retCode === "990011") {
        message.error("初审图片只能上传3次");
      } else {
        message.error(xhr.data.retMsg);
      }
    });
};
</script>
<style lang="less" scoped>
.upload-box {
  :deep(.ant-upload-list) {
    display: flex;
    justify-content: center;
  }
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
  .upload-desc {  
    font-size: 12px;
    color: #A8AEB8;
  }
}
</style>
