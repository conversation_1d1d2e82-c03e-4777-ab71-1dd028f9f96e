<!-- 内容标签 -->
<template>
  <div class="content-tag">
    <a-tabs v-model:activeKey="activeKey" 
    :destroyInactiveTabPane="isTabPane">
      <a-tab-pane key="1" tab="业务类标签">
        <bizTag :tagData="initBizTag" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="综合类标签">
        <bizTag :tagData="initSynthesisTag" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script setup>
import { bizAdd, bizPage, synthesisAdd, synthesisPage } from "@/api/contentTag";
import bizTag from "./content-tag/tableTag.vue";
//业务标签数据
const initBizTag = {
  tagPage: bizPage, //tabl的请求url
  addTag: bizAdd, //新增的请求url
  addText: "新增业务类标签",
  editText: "编辑业务类标签",
};
//综合类标签数据
const initSynthesisTag = {
  tagPage: synthesisPage, //tabl的请求url
  addTag: synthesisAdd, //新增的请求url
  addText: "新增综合类标签",
  editText: "编辑综合类标签",
};
const activeKey = ref("1");
const isTabPane = ref(true);
</script>
<style lang="less" scoped>
::deep(.ant-tabs) {
  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    font-size: 14px;
    color: #011738;
  }
}
</style>
