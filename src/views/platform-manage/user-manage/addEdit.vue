<!-- 账号新增编辑 -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="props.addEditData.modalOpen"
    :title="props.addEditData.modalTitle"
    okText="确定"
    :afterClose="resetFields"
    :confirm-loading="modalLoading"
    @ok="addEditSubmit"
  >
    <a-spin :spinning="modalLoading">
      <a-form>
        <a-form-item label="姓名" v-bind="validateInfos.name">
          <a-input v-model:value="addEditFrom.name" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item 
        label="电话号码" 
        v-bind="validateInfos.mobile">
          <a-input
            v-model:value="addEditFrom.mobile"
            @blur="validate('mobile', { trigger: 'blur' }).catch(() => {})"
            :maxlength="11"
            placeholder="请输入电话号码"
          />
        </a-form-item>
        <a-form-item label="机构" v-bind="validateInfos.dept">
          <a-tree-select
            v-model:value="addEditFrom.dept"
            :tree-data="cityAllTree"
            tree-checkable
            allow-clear
            placeholder="请选择机构"
            :maxTagCount="1"
            tree-node-filter-prop="label"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>
<script setup>
import { message, Form } from "ant-design-vue";
import { userEdit, userAdd } from "@/api/userManage";
import { encryptionMobile } from "@/assets/js/utils";
import emitter from "@/assets/js/emitter";
import {validateMobile} from "@/assets/js/formValidator";
const props = defineProps({
  addEditData: Object,
});

const modalLoading = ref(false);
const addEditFrom = props.addEditData.list;
const cityAllTree = props.addEditData.cityAllTree;
//表单规则验证数据
const addEditRules = reactive({
  name: [
    {
      required: true,
      message: "请输入姓名",
    },
    {
      min: 1,
      max: 50,
      message: "长度在1 到 50个字符",
      trigger: "change"
    }
  ],
  mobile: [
    {
      required: true,
      validator: validateMobile,
      trigger: "blur"
    }
  ],
  dept: [
    {
      required: true,
      trigger: "change",
      message: "请选择机构"
    }
  ]
});

const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  addEditFrom,
  addEditRules
);
//新增/编辑数据
const addEditSubmit = () => {
  validate()
    .then(async () => {
      modalLoading.value = true;
      let res = {};
      const { name, mobile, id } = toRaw(addEditFrom);
      if (!addEditFrom.id) {
        //新增
        const fromData = {
          name: encodeURI(encryptionMobile(name)),
          mobile: encodeURI(encryptionMobile(mobile)),
          dept: addEditFrom.dept.join(',')
        };
        res = await userAdd(fromData);
      } else {
        //编辑提交
        const fromData = {
          name: encodeURI(encryptionMobile(name)),
          mobile: encodeURI(encryptionMobile(mobile)),
          id,
          dept: addEditFrom.dept.join(',')
        };
        res = await userEdit(fromData);
      }
      modalLoading.value = false;
      if (res.retCode === "000000") {
        message.success("操作成功！");
        emitter.emit("addEditUser", true);
      } else if (res.retCode === "990001") {
        message.error("已存在相同姓名的用户");
      } else if (res.retCode === "990002") {
        message.error("已存在相同电话号码的用户");
      } else {
        message.error(res.retMsg);
      }
    })
    .catch((err) => {
      console.log("error", err);
    });
};
</script>
  