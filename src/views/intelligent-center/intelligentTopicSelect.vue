<!-- 智能选题 -->
<template>
  <div class="intelligent-topic-select">
    <div class="topic-tool">
      <p class="box-title">话题生成器</p>
      <div class="tool-box">
        <div class="feature-select" @click="industryModal">
          <img class="topic-icon" src="../../assets/image/topic-industry.png" alt="">
          <div class="profession-box">
            <p class="profession-text">
              {{industryModalData.idustrys.length === 0 ? "请选择您的行业赛道" : "行业赛道"}}
            </p>
            <p class="profession-tag" v-if="industryModalData.idustrys.length">
              <span v-for="item in industryModalData.idustrys" :key="item.id">{{ item.name }}</span>
            </p>
          </div>
          <RightOutlined />
        </div>
        <div class="feature-select" @click="hotspotDrawer">
          <img class="topic-icon" src="../../assets/image/topic-hotspot.png" alt="">
          <div class="profession-box">
            <p class="profession-text">热点结合</p>
            <p class="profession-tag select-hotspot" v-if="hotspotDrawerData.hotspot.title || hotspotDrawerData.festival.name">
              {{ hotspotDrawerData.hotspot.title || hotspotDrawerData.festival.name }}
            </p>
          </div>
          <RightOutlined />
        </div>
        <div class="topic-form">
          <a-spin :spinning="aiHelpWriteLoading">
            <a-form>
              <a-form-item v-bind="validateInfos.require">
                <div class="topic-ai-box">
                  <img src="../../assets/image/topic-ai.png" alt="">
                  <span @click="aiHelpWrite">AI帮写</span>
                </div>
                <a-textarea
                class="topic-textarea"
                  v-model:value="topicForm.require"
                  placeholder="补充话题描述，生成结果更精准"
                  allow-clear
                  :auto-size="{ minRows: 13, maxRows: 13 }"
                />
                <div class="button-box">
                  <a-button 
                  type="primary" 
                  :disabled="formDisabled"
                  @click="createTopic">{{submitText}}</a-button>
                </div>
              </a-form-item>
            </a-form>
          </a-spin>
        </div>
      </div>
    </div>
    <div class="topic-result">
      <p class="box-title">生成话题</p>
      <div class="topic-empty" v-if="emptyStatus">
        <p>暂无话题，请操作左侧话题设置项</p>
        <img class="empty-img" src="../../assets/image/topic-empty.png" alt="">
      </div>
      <div class="result-box" ref="masonryRef" >
        <template v-if="!emptyStatus && topicContents.length">
          <div class="topic-list result-box-hover" v-for="item in topicContents" :key="item.title">
            <div class="topic-box">
              <h2 class="topic-title">{{ item.title }}</h2>
              {{ item.summary }}
            </div>
            <div class="topic-button-box">
              <div class="topic-tag" v-if="hotspotDrawerText">
                <img src="../../assets/image/topic-ai.png" alt="">
                根据您的热点“{{ hotspotDrawerText }}”生成 
              </div>
              <a-button type="primary" @click="writeTopic(item)">
                <EditFilled />
                写成文章
              </a-button>
            </div>
          </div>
        </template>
        <template v-if="formDisabled">
          <div class="topic-list" v-for="item in 5" :key="item" ref="topicList">
            <div class="topic-box">
              <a-skeleton active />
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
  <industry v-if="industryModalData.modalOpen" :industryModalData="industryModalData"/>
  <hotspot v-if="hotspotDrawerData.drawerOpen" :hotspotDrawerData="hotspotDrawerData"/>
</template>

<script setup>
import { message, Form } from "ant-design-vue";
import industry from "./intelligent-topic-select/industry.vue"
import hotspot from "./intelligent-topic-select/hotspot.vue"
import emitter from "@/assets/js/emitter";
import { industryMine } from "@/api/intelligentTopicSelect";
import Masonry from 'masonry-layout';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { marked }from 'marked';
const masonryRef = ref();
const topicList = ref();

//瀑布流布局
const initMasonry = ()=> {
  const topicListW = topicList.value.offsetWidth;
  const msnry = new Masonry(masonryRef.value, {
    // Masonry的配置选项
    itemSelector: '.topic-list', // 确保你的项目有相应的类名
    columnWidth: topicListW, // 或者使用百分比，如 20% 等
  });
}
let submitText = ref('开始生成')
//表单数据
const topicForm = reactive({
  require: '',
})
const addEditRules = reactive({
  require: [
    {
      required: false,
      message: "请输入您的创作主题",
    },
    {
      min: 1,
      max: 2000,
      message: "长度在1 到 2000个字符",
      trigger: "change"
    }
  ]
});
//表单数据处理
const useForm = Form.useForm;
const { resetFields, validate, validateInfos } = useForm(
  topicForm,
  addEditRules
);
// 行业数据
const industryModalData = ref({
  modalOpen: false,
  idustrys: [], //行业集合
  idustrysId: [], //id
  idustrysName: [] //name
})
const industryModal = ()=> {
  industryModalData.value.modalOpen = true;
}
//获取预选行业
const getIndustryMine = async ()=> {
  const res = await industryMine();
  if (res.retCode === "000000") {
    industryModalData.value.idustrys = res.data;
    industryModalData.value.idustrysId = res.data.map(el => el.id);
    industryModalData.value.idustrysName = res.data.map(el => el.name)
    createTopic();
  } else {
    emptyStatus.value = true;
  }
};
//选择行业后更新数据
emitter.on('selectIdustry',(value)=>{
  console.log(value)
  industryModalData.value.idustrysId = value.map(el => el.id);
  industryModalData.value.idustrysName = value.map(el => el.name)
  industryModalData.value.idustrys = value;
}) 
// 热点数据
const hotspotDrawerData = reactive({
  drawerOpen: false,
  hotspot:{},
  festival:{}
})
const hotspotDrawer = ()=> {
  hotspotDrawerData.drawerOpen = true;
}
emitter.on('selectHotspot',(value)=>{
  hotspotDrawerData.hotspot = value;
  hotspotDrawerData.drawerOpen = false;
  hotspotDrawerData.festival = {};
}) 
emitter.on('selectFestival',(value)=>{
  hotspotDrawerData.festival = value;
  hotspotDrawerData.drawerOpen = false;
  hotspotDrawerData.hotspot = {};
})
const formDisabled = ref(false);
watch( () => topicForm.require,(newValue,oldValue)=>{
  if(!newValue) {
    formDisabled.value = false;
  }
})
// 使用marked将Markdown转换为HTML
const topicContents = ref([])
const compiledMarkdown = computed(() => {
  return marked(writeData.content);
});
const emptyStatus = ref(false);
const hotspotDrawerText = ref(null);
const createTopic = () => {
  validate()
    .then(async () => {
      formDisabled.value = true;
      submitText.value = '话题生成中...';
      topicContents.value = [];
      fetchEventSource(`/cop/ai/topic/generate?replayTime=${new Date().getTime()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        },
        openWhenHidden: true,
        body: `industry=${industryModalData.value.idustrysName.join(',')}&hot=${hotspotDrawerData.hotspot?.title || ''}&festivalName=${hotspotDrawerData.festival?.name || ''}&festivalKeyWord=${hotspotDrawerData.festival?.keyword || ''}&userInput=${topicForm.require}`,
        onmessage(ev) {
          let writeContent = JSON.parse(ev.data);
          submitText.value = '话题生成中...';
          if (writeContent.content === 'content') {//内容状态
            formDisabled.value = true;
            emptyStatus.value = false;
            hotspotDrawerText.value = hotspotDrawerData.hotspot.title || hotspotDrawerData.festival.name;
          } else if (writeContent.content === 'COPSSEEND'){//关闭
            submitText.value = '开始生成';
            formDisabled.value = false;
          } else if(writeContent.content === 'COPSSEERROR') {
            formDisabled.value = false;
            submitText.value = '开始生成';
            emptyStatus.value = true;
          } else if(writeContent.content === 'COP_SSE_DATA_INSPECTION_FAILED') {
            formDisabled.value = false;
            emptyStatus.value = true;
            submitText.value = '开始生成';
            message.error('您的创作要求含有敏感词汇，请修改后再重新生成');
          } else {
            topicContents.value.push(JSON.parse(writeContent.content))
          }
        },
        onerror(err) {
          submitText.value = '开始生成';
          formDisabled.value = false;
          emptyStatus.value = true;
          throw err;
        },
      });
    })
};
//ai帮写
const aiHelpWriteLoading = ref(false)
const aiHelpWrite = () => {
  validate()
    .then(async () => {
      if(industryModalData.value.idustrysName.length === 0) {
        message.error('请至少选择一个行业赛道')
      }
      aiHelpWriteLoading.value = true;
      topicForm.require = '';
      let params = {
        writeFlag: '1',
        prompt: `你是一个专业的内容运营专家，请根据我的赛道【${industryModalData.value.idustrysName[0]}】帮我生成个从创作方向、内容面向群体、带来的价值等方面考虑的创作方向，字数 150字以内`,
        userParam: JSON.stringify({"writeFlag":5,"replayTime":1744017806977})
      },sseStatus='';
      fetchEventSource(`/cop/ai/write/ali/dp/r?replayTime=${new Date().getTime()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        },
        openWhenHidden: true,
        body: `writeFlag=${params.writeFlag}&prompt=${params.prompt}&userParam=${encodeURIComponent(params.userParam)}`,
        onmessage(ev) {
          let writeContent = JSON.parse(ev.data);
            // 这里可以根据接收到的流式数据更新前端界面
          if(writeContent.content === 'reasoning') {//推理状态
            sseStatus = writeContent.content;
            aiHelpWriteLoading.value = true;
          }else if (writeContent.content === 'content') {//内容状态
            sseStatus = writeContent.content;
            aiHelpWriteLoading.value = false;
          }else if (writeContent.content === 'COPSSEEND'){//关闭
            aiHelpWriteLoading.value = false;
          }else if(writeContent.content === 'COPSSEERROR') {
            writeData.content = '';
            aiHelpWriteLoading.value = false;
          }
          if(sseStatus === 'content' && !writeContent.content.includes('content') && !writeContent.content.includes('COPSSEEND')) {
            topicForm.require += writeContent.content;
          }
        },
        onerror(err) {
          message.error('系统繁忙，请稍后再试');
          aiHelpWriteLoading.value = false;
          throw err;
        },
      });
    })
};
//写成文章
const router = useRouter();
const writeTopic = (item)=>{
  localStorage.setItem('topicRequire',JSON.stringify(item));
  router.push("/intelligentCenter/intelligentWrite");
}
onMounted(()=> {
  getIndustryMine();
})
//组件卸载后销毁emitter
onUnmounted(()=>{
  localStorage.getItem('topicRequire')
  emitter.off('selectIdustry');
  emitter.off('hotspotDrawerData');
  emitter.off('selectFestival');
})
</script>

<style lang="less" scoped>
.intelligent-topic-select {
  height: calc(100vh - 120px);
  display: flex;
  padding-top: 10px;
  position: relative;
  .box-title {
    font-size: 14px;
    color: #1E293B;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .topic-tool {
    width: 340px;
  }
  .tool-box {
    height: calc(100% - 34px);
    padding: 20px 12px;
    background: #fff;
    border: 1px solid #F5F8FB;
    box-shadow: 0 10px 15px -3px rgba(15,23,42,0.08);
    border-radius: 8px;
    overflow: auto;
    .feature-select {
      height: 75px;
      display: flex;
      padding: 0 15px;
      align-items: center;
      background: rgba(254, 131, 8, .05);
      border-radius: 4px;
      margin-bottom: 20px;
      color: rgba(30, 41, 59, .8);
      cursor: pointer;
    }
    .profession-box {
      display: flex;
      flex-wrap: wrap;
      flex: auto;
      flex-direction: column;
      padding-left: 8px;
    }
    .profession-text {
      font-size: 14px;
      font-weight: bold;
    }
    .topic-icon {
      width: 20px;
    }
    .profession-tag {
      font-size: 12px;
      padding-top: 10px;
      span{ 
        margin-right: 5px;
      }
    }
    .select-hotspot {
      color: #FE8308;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .hotspot-text {
      flex: auto;
      font-size: 14px;
      font-weight: bold;
    }
  }
  .topic-form {
    :deep(.topic-textarea) {
      text-align: justify;
      margin-bottom: 10px;
      padding-bottom: 25px;
    }
    .topic-ai-box {
      width: 94%;
      display: flex;
      position: absolute;
      align-items: center;
      padding-bottom: 4px;
      bottom: 21px;
      left: 5px;
      z-index: 99;
      background: #fff;
      img {
        width: 20px;
        margin-right: 5px;
      }
      span {
        cursor: pointer;
      }
    }
    .button-box {
      position: relative;
      button { 
        width: 130px;
        position: absolute;
        right: 0;
        top: 0;
        border: 0;
      }
      :deep(span) {
        margin: 0 auto;
      }
      .ant-btn-primary:disabled {
        border: 0;
        color: #fff;
        background-image: linear-gradient(90deg, #CACACA 0%, #CACACA 100%);
      }
      .write-loading:disabled {
        background-image: linear-gradient(90deg, rgba(254, 115, 9, .7) 0%, rgba(254, 115, 9, .7) 100%);
      }
    }
  }
  
  .topic-result {
    flex: 1;
    padding-left: 30px;
    // overflow-y: auto;
  }
  .topic-empty {
    height: calc(100% - 34px);
    overflow-y: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: rgba(0, 0, 0, .3);
  }
  .empty-img {
    width: 372px;
  }
  .result-box {
    height: calc(100% - 34px) !important;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .ant-skeleton {
      margin-bottom: 20px;
    }
    .topic-list {
      width: calc(50% - 10px);
      margin-bottom: 20px;
      border-radius: 8px;
      border: 1px solid #F0F4F9;
      background: #fff;
    }
    // .topic-list:nth-child(odd) {
    //   padding-right: 10px;
    // }
    // .topic-list:nth-child(even) {
    //   padding-left: 10px;
    // }
    .topic-box {
      padding: 15px 20px;
      
      border-radius: 8px;
      font-size: 12px;
      color: #1E293B;
      text-align: justify;
      line-height: 23px;
    }
    .topic-title {
      font-size: 14px;
      color: #1E293B;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .result-box-hover:hover {
      background-color: rgba(254, 131, 8, .1);
    }
    .topic-button-box {
      display: flex;
      justify-content: space-between;
      padding: 0 10px 10px 20px;
      overflow: hidden;
      .ant-btn-primary {
        border-radius: 17px;
      }
    }
    .topic-tag {
      max-width: calc(100% - 130px);
      white-space: nowrap;
      overflow: hidden;
      font-size: 12px;
      color: #0009;
      background: #fe83081a;
      border-radius: 17px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      img {
        width: 20px;
        margin-right: 10px;
      }
    }
  }
  
}
</style>
