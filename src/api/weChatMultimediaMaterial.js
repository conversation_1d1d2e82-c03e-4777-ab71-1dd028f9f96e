import $https from "@/assets/js/request"

// 列表
export const newsList = (data) => {
    return $https.fetchGet('/cop/wx/back/news/list',data)
}
//同步素材
export const syncMaterial = (data,chkCurWx) => {
    return $https.fetchPost('/cop//wx/back/news/syncMaterial?chkCurWx='+chkCurWx,data)
}
// 编辑 
export const ukwEdit = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/ukw/edit?chkCurWx='+chkCurWx,data)
}
// 新增
export const ukwAdd = (data,chkCurWx) => {
    return $https.fetchPost('/cop/wx/back/rsp/ukw/add?chkCurWx='+chkCurWx,data)
}
// 删除
export const ukwDel = (data) => {
    return $https.fetchGet('/cop/wx/back/news/deleteMaterial',data)
}